package com.hqins.agent.postsale.dao.mybatis;

import com.google.common.collect.Sets;
import com.hqins.common.generator.CodeGenerator;
import com.hqins.common.generator.config.GenerateConfig;
import org.junit.Test;

import java.io.File;


/**
 * <AUTHOR> <PERSON>
 * @date 2021/4/6
 * @Description
 */
public class GenerateTest {

    @Test
    public void testCodeGenerate() {
        //工作空间
        final String WORKSPACE = "F:\\hq-code\\devops\\hqins-agent-postsale";

        GenerateConfig generateConfig = new GenerateConfig();
        //作者
        generateConfig.setAuthor("li yang");
        //最顶层包名
        generateConfig.setBasePackage("com.hqins.agent.postsale");
        //工程名-默认取artifactId
        generateConfig.setProjectName("hqins-agent-postsale");
        //当前服务注册在nacos中的服务名-默认取artifactId
        generateConfig.setServiceNameRegisterInNacos("hqins-agent-postsale");
        //provider模块的根路径
//        generateConfig.setProviderRootPath(WORKSPACE + "\\" + generateConfig.getProjectName() + "\\" + generateConfig.getProjectName() + "-provider");
        //api模块的根路径
//        generateConfig.setApiRootPath(WORKSPACE + "\\" + generateConfig.getProjectName() + "\\" + generateConfig.getProjectName() + "-api");

        String thisPath = new File(new File(this.getClass().getClassLoader().getResource("").getPath()).getParent()).getParent();
        generateConfig.setProviderRootPath(thisPath);
        generateConfig.setApiRootPath(thisPath.replace("provider", "api"));

        /**
         * jdbc连接参数,每次生成前，请根据各个项目实际情况，自己更新jdbc连接参数之后再生成
         */
        generateConfig.setJdbcDriverClassName("com.mysql.cj.jdbc.Driver");
        generateConfig.setJdbcUrl("*************************************************************************************************************************************************************************************");
        generateConfig.setJdbcUsername("postsale_uat");
        generateConfig.setJdbcPassword("Hqins@66");
        /**
         * 代码生成时要过滤掉的包名，至少要包含sys_config。
         *
         * 在mysql 8中，创建的数据库默认会加上这张表
         * 因为sys_config表并不能被读取字段信息，故而会报错
         * 所以我们要在代码生成之前，将该表过滤掉
         */
        generateConfig.setFilterTableNames(Sets.newHashSet("sys_config"));

        //表名前缀，比如t_user，那么该参数设置为t_，这样生成的实体类就是User，而不是TUser了。
        generateConfig.setTableNamePrefix("");


        /**
         * 哪些表需要生成对应的CRUD代码，包含api,request,vo,controller, service,serviceImpl
         * 如果不指定的话，默认只会生成entity, mapper, mapper.xml
         * 生成策略：只有entity重新生成时，才会根据最新的表结构替换掉原来的entity类
         * 其它的代码，如mapper, mapper.xml, service等，如果之前已经存在已经生成的代码文件，就不会重复覆盖生成。
         */
        generateConfig.setTableNames(Sets.newHashSet(
                "agent_share_relation"
        ));
        CodeGenerator generator = new CodeGenerator();
//        assertNull(generator.getAllTableNames(generateConfig));
        generator.generateAll(generateConfig);

    }

    @Test
    public void generate() {
        GenerateConfig generateConfig = new GenerateConfig();
        //作者
        generateConfig.setAuthor("LiYang");
        //最顶层包名
        generateConfig.setBasePackage("com.hqins.agent.postsale");
        //工程名-默认取artifactId
        generateConfig.setProjectName("hqins-agent-postsale");
        //当前服务注册在nacos中的服务名-默认取artifactId
        generateConfig.setServiceNameRegisterInNacos("hqins-agent-postsale");
        String thisPath = new File(new File(this.getClass().getClassLoader().getResource("").getPath()).getParent()).getParent();
        generateConfig.setProviderRootPath(thisPath);
        generateConfig.setApiRootPath(thisPath.replace("provider", "api"));
        generateConfig.setJdbcDriverClassName("com.mysql.jdbc.Driver");
        generateConfig.setJdbcUrl("***************************************************************************************************");
        generateConfig.setJdbcUsername("postsale_uat");
        generateConfig.setJdbcPassword("Hqins@66");
        generateConfig.setFilterTableNames(Sets.newHashSet());

        //表名前缀，比如t_user，那么该参数设置为t_，这样生成的实体类就是User，而不是TUser了。
        generateConfig.setTableNamePrefix("");
        //指定筛选掉包含字符串的表名
        generateConfig.setSpecifiesFilterTableName("");
        //指定生成包含字符串的表名
//        generateConfig.setSpecifiesTableName("two");
        //指定生成指定字符串的标题
        generateConfig.setTableNames(Sets.newHashSet(
                "repair_leave_approval"
        ));

        /**
         * 哪些表需要生成对应的CRUD代码，包含api,request,vo,controller, service,serviceImpl
         * 如果不指定的话，默认只会生成entity, mapper, mapper.xml
         * 生成策略：只有entity重新生成时，才会根据最新的表结构替换掉原来的entity类
         * 其它的代码，如mapper, mapper.xml, service等，如果之前已经存在已经生成的代码文件，就不会重复覆盖生成。
         */
        generateConfig.setCrudRequiredTableNames(Sets.newHashSet(""));

        generateConfig.setGeneratorMapperXML(true);
        generateConfig.setGeneratorBatch(true);

        CodeGenerator generator = new CodeGenerator();
        generator.getAllTableNames(generateConfig) ;
        generator.generateAll(generateConfig);
    }
}
