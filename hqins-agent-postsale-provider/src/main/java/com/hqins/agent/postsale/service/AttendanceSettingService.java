package com.hqins.agent.postsale.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hqins.agent.postsale.dao.entity.AgentAttendanceTime;
import com.hqins.agent.postsale.dao.entity.AttendanceAddress;
import com.hqins.agent.postsale.dao.entity.AttendanceLeaveCheck;
import com.hqins.agent.postsale.dao.entity.LeaveCheckConfig;
import com.hqins.agent.postsale.dto.enums.LeaveApplyType;
import com.hqins.agent.postsale.dto.request.ApplyOARequestBean;
import com.hqins.agent.postsale.dto.request.KmReviewParamterFormPublicRequest;
import com.hqins.agent.postsale.dto.request.LeaveCheckConfigRequest;
import com.hqins.agent.postsale.dto.response.AllowOrNotResponse;
import com.hqins.agent.postsale.dto.response.AuditResponse;
import com.hqins.agent.postsale.dto.response.KmReviewParamterFormPublicResponse;
import com.hqins.agent.postsale.dto.response.ProcessCompletedReturnRequestBean;
import com.hqins.agent.postsale.model.enums.AddressType;
import com.hqins.agent.postsale.model.enums.MessageContentType;
import com.hqins.agent.postsale.model.request.*;
import com.hqins.agent.postsale.model.vo.*;
import com.hqins.common.base.page.PageInfo;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> Li
 * @Date 2023/3/1 16:35
 */
public interface AttendanceSettingService extends IService<AttendanceAddress> {

    PageInfo<AttendanceSettingVO> queryList(String companyCode, String companyInstCode, AddressType addressType, String merchantCode, String merchantOrgCode, Boolean status,Boolean authenticationOrNot, long current, long size);

    Boolean updateSetting(AttendanceSettingRequest request);

    Boolean saveSetting(AttendanceSettingRequest request);

    PageInfo<AttendanceAuditQueryVO> examineQuery(AttendanceQueryRequest attendanceQueryRequest, long current, long size, Long adminAppId, Long staffId);

    AuditResponse auditAttendance(AuditRequest request);

    void export(String exportId, AttendanceDetailQueryRequest request, Long adminAppId, Long staffId);

    Map<String, Object> download(String exportId , boolean status);

    void spotCheck();

    void checkAddress();

    AgentAttendanceTime queryAgentAttendanceTime(String year, String orgIns,Boolean ybFlag);

    void addAttendanceTime(AgentAttendanceTime request);

    AttendanceTeamVO team(AttendanceTeamRequest request);

    Map<String, AttendanceAgentVO> attendanceAgents(AttendanceAgentsRequest request);

    PageInfo<AttendanceDetailQueryVO> detailQuery(AttendanceDetailQueryRequest request, long current, long size , Long adminAppId, Long staffId);

    void callBack(ProcessCompletedReturnRequestBean request);

    KmReviewParamterFormPublicResponse postProcessCompleted(KmReviewParamterFormPublicRequest form);

    KmReviewParamterFormPublicResponse postProcessCompletedNew(ApplyOARequestBean form);

    KmReviewParamterFormPublicResponse applicationForm(Long id , LeaveApplyType type);

    List<AgentAttendanceTime> configuration(LocalDateTime requestTime, String orgIns);

    void messageSaveByLeader(String employeeCode  , MessageContentType messageContentType,String name);


    List<LeaveCheckConfig> queryLeaveConfig(String year,String orgCode);

    void addLeaveConfig(List<LeaveCheckConfigRequest> request);

    void saveAllStaff();

    void updateAllStaff();

    void addCheck(AttendanceLeaveCheck request);

    AttendanceLeaveCheck queryCheck(String orgCode);

    AllowOrNotResponse allowOrNot(String employeeCode);

    void exportAttendance(String exportId, AttendanceQueryRequest request, Long adminAppId, Long staffId);

    void missedSignatureMark(String orgCode);
}
