package com.hqins.agent.postsale.web.controller;

import cn.hutool.json.JSONUtil;
import com.hqins.agent.postsale.dto.response.AuditResponse;
import com.hqins.agent.postsale.dto.response.MeAttendanceAndLeaveResponse;
import com.hqins.agent.postsale.model.request.*;
import com.hqins.agent.postsale.model.vo.*;
import com.hqins.agent.postsale.service.LeaveApplicationsService;
import com.hqins.common.base.ApiResult;
import com.hqins.common.base.constants.Strings;
import com.hqins.common.base.page.PageInfo;
import com.hqins.common.web.RequestContextHolder;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.LocalDate;
import java.util.List;

/**
 * 请假记录表控制器
 * <AUTHOR>
 * @since 2023-07-14
 */
@Api(tags = "请假记录表控制器")
@RestController
@RequestMapping("/leave-application")
@Slf4j
public class LeaveApplicationsController {

    private final LeaveApplicationsService leaveApplicationsService;

    public LeaveApplicationsController(LeaveApplicationsService leaveApplicationsService) {
        this.leaveApplicationsService = leaveApplicationsService;
    }

    /**
     *前端H5
     */
    @ApiOperation("校验是否一次性修完的")
    @GetMapping("/check-one")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<Boolean> checkOne(@RequestParam("leaveType") String leaveType,
                                       @RequestParam("employeeCode") String employeeCode) {
        return ApiResult.ok(leaveApplicationsService.checkOne(leaveType,employeeCode));
    }
    @ApiOperation("校验是否能申请请假")
    @GetMapping("/check")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<Boolean> check(@RequestParam("beginTime") String beginTime,
                                    @RequestParam("endTime") String endTime,
                                    @RequestParam("days") double days,
                                    @RequestParam("leaveType") String leaveType) {
        return ApiResult.ok(leaveApplicationsService.check( beginTime,  endTime ,leaveType, days));
    }

    @ApiOperation("新增请假记录表")
    @PostMapping("/add")
    @ResponseStatus(HttpStatus.CREATED)
    public ApiResult<Void> add(@Valid @RequestBody LeaveApplicationsAddRequest request) {
        log.info("LeaveApplicationsController add 入参 {} ,employee:{} ", JSONUtil.toJsonStr(request), RequestContextHolder.getEmployeeCode());
        leaveApplicationsService.add(request);
        return ApiResult.ok();
    }



    /**
     *后台
     */
    @ApiOperation("分页查询请假记录表")
    @GetMapping("/query")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<PageInfo<LeaveAttendanceAuditQueryVO>> query(
            @ApiParam(Strings.CURRENT_PAGE_MSG) @RequestParam("current") long current,
            @ApiParam(Strings.CURRENT_PAGE_MSG) @RequestParam("size") long size,
            @ApiParam("工号") @RequestParam(value = "employeeCode", required = false) String employeeCode,
            @ApiParam("人员姓名") @RequestParam(value = "employeeName", required = false) String employeeName,
            @ApiParam("合伙人组织code") @RequestParam(value = "companyCode", required = false) String companyCode,
            @ApiParam("归属合伙人机构code") @RequestParam(value = "companyInstCode", required = false) String companyInstCode,
            @ApiParam("请假日期起期") @RequestParam(value = "applyStartTime", required = false) LocalDate applyStartTime,
            @ApiParam("请假日期止期") @RequestParam(value = "applyEndTime", required = false) LocalDate applyEndTime,
            @ApiParam("请假类型") @RequestParam(value = "leaveType", required = false) String leaveType,
            @ApiParam("请假子类型") @RequestParam(value = "leaveChildType", required = false) String leaveChildType,
            @ApiParam("审核状态") @RequestParam(value = "audit", required = false) String audit
    ) {
        LeaveApplicationsQueryRequest leaveApplicationsQueryRequest = LeaveApplicationsQueryRequest.builder()
                .current(current)
                .size(size)
                .employeeCode(employeeCode)
                .employeeName(employeeName)
                .companyCode(companyCode)
                .companyInstCode(companyInstCode)
                .applyStartTime(applyStartTime)
                .applyEndTime(applyEndTime)
                .leaveType(leaveType)
                .leaveChildType(leaveChildType)
                .audit(audit)
                .build();
        leaveApplicationsQueryRequest.correctPageQueryParameters();
        return ApiResult.ok(leaveApplicationsService.query(leaveApplicationsQueryRequest));
    }

    @ApiOperation("审核详情天数")
    @GetMapping("/sumCount")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<LeaveApplicationsVO> sumCount(@ApiParam("代理人工号") @RequestParam("employeeCode") String employeeCode,
                                                   @ApiParam("类型") @RequestParam("type") String type) {
        return ApiResult.ok(leaveApplicationsService.sumCount(employeeCode,type));
    }
    @ApiOperation("审核通过/不通过")
    @PostMapping("/audit")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<AuditResponse> audit(@Valid @RequestBody AuditRequest request) {
        return ApiResult.ok(leaveApplicationsService.audit(request));
    }

    @ApiOperation("我的页面签到和请假计数")
    @GetMapping("/attendance-and-leave")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<MeAttendanceAndLeaveResponse> attendanceAndLeave(@RequestParam("employeeCode") String employeeCode) {
        return ApiResult.ok(leaveApplicationsService.attendanceAndLeave(employeeCode));
    }

    @ApiOperation("H5页面查询审核信息")
    @GetMapping("/h5/query")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<PageInfo<H5LeaveListVO>> h5Query(@RequestParam(value = "type",required = false) String type,
                                                      @RequestParam(value = "employeeCode",required = false) String employeeCode,
                                                      @RequestParam(value = "isSub") Boolean isSub,
                                                      @ApiParam(Strings.CURRENT_PAGE_MSG) @RequestParam(value = "current") long current,
                                                      @ApiParam(Strings.SIZE_PAGE_MSG) @RequestParam("size") long size) {

        LeaveApplicationsQueryRequest leaveApplicationsQueryRequest = LeaveApplicationsQueryRequest.builder()
                .current(current)
                .size(size)
                .employeeCode(employeeCode)
                .leaveType(type)
                .isSub(isSub)
                .build();
        leaveApplicationsQueryRequest.correctPageQueryParameters();
        return ApiResult.ok(leaveApplicationsService.h5Query(leaveApplicationsQueryRequest));
    }

    @ApiOperation("H5审核通过/不通过")
    @PostMapping("/h5/audit")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<Void> h5Audit(@Valid @RequestBody AuditRequest request) {
        leaveApplicationsService.h5Audit(request);
        return ApiResult.ok();
    }


    @ApiOperation("新增销假记录表")
    @PostMapping("/addRevoke")
    @ResponseStatus(HttpStatus.CREATED)
    public ApiResult<Void> addRevoke(@Valid @RequestBody LeaveRevokeAddRequest request) {
        log.info("LeaveApplicationsController addRevoke 入参 {} ,employee:{} ", JSONUtil.toJsonStr(request), RequestContextHolder.getEmployeeCode());
        leaveApplicationsService.addRevoke(request);
        return ApiResult.ok();
    }

    @ApiOperation("代理人查询补卡/销假/请假")
    @PostMapping ("/querySubmit")
    @ResponseStatus(HttpStatus.CREATED)
    public ApiResult<PageInfo<LeaveApplicationsQueryVO>> querySubmit(@Valid @RequestBody LeaveRevokeQueryRequest request) {
        log.info("LeaveApplicationsController querySubmit 入参 {} ,employee:{} ", JSONUtil.toJsonStr(request), RequestContextHolder.getEmployeeCode());
        return ApiResult.ok(leaveApplicationsService.querySubmit(request));
    }


    @ApiOperation("代理人查询补卡/销假/请假")
    @PostMapping ("/employeeLeaveQuery")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<List<EmployeeLeaveQueryVO>> employeeLeaveQuery(@Valid @RequestBody EmployeeLeaveQueryRequest request) {
        log.info("LeaveApplicationsController employeeLeaveQuery 入参 {} ,employee:{} ", JSONUtil.toJsonStr(request), RequestContextHolder.getEmployeeCode());
        return ApiResult.ok(leaveApplicationsService.employeeLeaveQuery(request));
    }





    @ApiOperation("定时任务刷历史补卡请假数据")
    @PostMapping("/job-supplement")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<Void> jobSupplement() {
        leaveApplicationsService.jobSupplement();
        return ApiResult.ok();
    }

    @ApiOperation("compress")
    @PostMapping("/compress")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<Void> compress() {
        leaveApplicationsService.compress();
        return ApiResult.ok();
    }


}
