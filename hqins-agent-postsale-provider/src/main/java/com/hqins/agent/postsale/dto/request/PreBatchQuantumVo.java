package com.hqins.agent.postsale.dto.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PreBatchQuantumVo  implements Serializable {

    //标题
    private String title;

    //申请人姓名
    private String employeeName;

    //申请人所属机构
    private String topName;

    //申请类型
    private String type;

    //所属团队
    private String team;

    //请假时间
    private String leaveDate;

    //销假时间
    private String revokeDate;

    //销假事由
    private String revokeDetail;

    //补卡时间
    private String appointDate;

    //补卡地点
    private String appointAddress;

    //请假事由
    private String leaveDetail;

    //本月请假情况
    private String sumLeaveCount;

    //本月剩余补卡次数
    private String surplusAppointCount;

    //补卡原因
    private String appointDetail;

    //流程提交时间
    private String applyDate;

    //抄送知会人
    private String cszhr;

    //部门负责人
    private String bmfzr;

    //机构分管领导
    private String jgfgld;

    //机构负责人
    private String jgfzr;

    //审批后知会人员
    private String sphzhry;

//    //申请类型
//    private String sqlx;
//
//    //部门代码
//    private String bmdm;
}
