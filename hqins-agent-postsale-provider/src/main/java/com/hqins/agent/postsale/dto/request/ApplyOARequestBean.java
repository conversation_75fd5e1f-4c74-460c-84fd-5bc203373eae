package com.hqins.agent.postsale.dto.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;


@ApiModel(value = "申请OA对象", description = "申请OA对象")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ApplyOARequestBean implements Serializable {

    @ApiModelProperty(value = "申请OA人员信息", example = "申请OA人员信息")
    private String applyUser;

    @ApiModelProperty(value = "requestId(唯一标识,驳回后重新提交使用)", example = "requestId(唯一标识,驳回后重新提交使用)")
    private Integer requestId;

    @ApiModelProperty(value = "流程标题")
    private String requestName;

    @ApiModelProperty(value = "业务类型id", example = "1:银保手续费")
    private String ywlxid;

    @ApiModelProperty(value = "部门代码(成本中心代码)", example = "部门代码(成本中心代码)")
    private String bmdm;

    @ApiModelProperty(value = "部门名称(成本中心名称)", example = "部门名称(成本中心名称)")
    private String bmmc;

    @ApiModelProperty(value = "主表数据")
    private List<OAFormDataVo> mainData;

    @ApiModelProperty(value = "附件数据")
    private List<OAFileVo> fileList;

}
