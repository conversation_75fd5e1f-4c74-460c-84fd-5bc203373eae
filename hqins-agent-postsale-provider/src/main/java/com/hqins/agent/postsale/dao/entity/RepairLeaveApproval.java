package com.hqins.agent.postsale.dao.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.*;

import java.time.LocalDateTime;
import java.io.Serializable;

/**
 * 请假审批表
 *
 * <AUTHOR>
 * @since 2025-05-08
 */
@TableName("repair_leave_approval")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class RepairLeaveApproval implements Serializable {


    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 类型：请假/补卡/销假
     */
    private String applicationType;

    /**
     * 是否待审核 true待审核 false审核完成
     */
    @TableField(value = "is_sub")
    private Boolean sub;

    /**
     * 请假/补卡/销假 ID
     */
    private Long leaveId;

    /**
     * 量子回传id
     */
    private String batchId;

    /**
     * 审核状态（审核中/审核成功/审核失败）
     */
    private String leaveType;

    /**
     * 请假人工号
     */
    private String leaveCode;

    /**
     * OA新true/旧false
     */
    @TableField(value = "oa_type")
    private Boolean oaType;

    /**
     * 审批人工号
     */
    private String approvalCode;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;

    /**
     * 创建人
     */
    private Integer createdUser;
}
