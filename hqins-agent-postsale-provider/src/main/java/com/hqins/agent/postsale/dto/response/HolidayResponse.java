package com.hqins.agent.postsale.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class HolidayResponse {
    private Integer year;
    private String region;
    private List<DateInfo> dates;

    @Data
    public static class DateInfo {
        private String date;
        private String name;
        private String name_cn;
        private String name_en;
        private String type;
    }
}
