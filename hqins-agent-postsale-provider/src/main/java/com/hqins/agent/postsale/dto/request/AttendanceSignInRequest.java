package com.hqins.agent.postsale.dto.request;

import com.hqins.agent.postsale.dto.enums.AppointType;
import com.hqins.agent.postsale.dto.enums.SignInType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("签到对象")
public class AttendanceSignInRequest implements Serializable {

    @ApiModelProperty("打卡地点代码")
    private String locationCode;

    @ApiModelProperty("打卡地点名称")
    private String locationName;

    @ApiModelProperty("打卡位置(经度)")
    private String  longitude;

    @ApiModelProperty("打卡位置(纬度)")
    private String  latitude;

    @ApiModelProperty("指定/非指定")
    private AppointType appointType;

    @ApiModelProperty("打卡类型 正常签到，超出范围签到")
    private List<SignInType> attendanceType;

    @ApiModelProperty("打卡地点 COMPANY(公司), MERCHANT(渠道商)")
    private String addressType;

    @ApiModelProperty("打卡图片")
    private String imageUrl;

    @ApiModelProperty("银保标识")
    private String orgTypeStr;

    @ApiModelProperty("二级机构")
    private String companyInsCode;


}
