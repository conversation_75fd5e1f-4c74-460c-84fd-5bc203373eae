package com.hqins.agent.postsale.service.impl;

import com.alibaba.nacos.api.utils.StringUtils;
import com.alicp.jetcache.Cache;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.CreateCache;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hqins.agent.org.model.api.EmployeeApi;
import com.hqins.agent.org.model.api.EmployeeOrgApi;
import com.hqins.agent.org.model.api.OrgApi;
import com.hqins.agent.org.model.api.SupervisorEmployeeApi;
import com.hqins.agent.org.model.request.SelectSupervisorEmployeeListPostRequest;
import com.hqins.agent.org.model.vo.EmployeeOrgVO;
import com.hqins.agent.org.model.vo.EmployeeVO;
import com.hqins.agent.org.model.vo.SupervisorEmployeeVO;
import com.hqins.agent.postsale.api.OrgApiWrapper;
import com.hqins.agent.postsale.api.PolicyWrapper;
import com.hqins.agent.postsale.api.UmApiWrapper;
import com.hqins.agent.postsale.config.*;
import com.hqins.agent.postsale.constants.AppConsts;
import com.hqins.agent.postsale.dao.entity.Message;
import com.hqins.agent.postsale.dao.entity.MessageSendTask;
import com.hqins.agent.postsale.dao.entity.MessageSystem;
import com.hqins.agent.postsale.dao.mapper.MessageMapper;
import com.hqins.agent.postsale.dao.mapper.MessageSendTaskMapper;
import com.hqins.agent.postsale.dao.mapper.MessageSystemMapper;
import com.hqins.agent.postsale.dto.response.AggMessageVO;
import com.hqins.agent.postsale.model.enums.MessageContentType;
import com.hqins.agent.postsale.model.enums.MessageSendChannel;
import com.hqins.agent.postsale.model.enums.MessageSource;
import com.hqins.agent.postsale.model.enums.MessageType;
import com.hqins.agent.postsale.model.request.*;
import com.hqins.agent.postsale.model.vo.*;
import com.hqins.agent.postsale.service.JPushService;
import com.hqins.agent.postsale.service.MessageService;
import com.hqins.basic.api.WechatApi;
import com.hqins.basic.model.enums.WechatAppType;
import com.hqins.common.base.constants.DatePatterns;
import com.hqins.common.base.enums.AgentOrgType;
import com.hqins.common.base.errors.ApiException;
import com.hqins.common.base.page.PageInfo;
import com.hqins.common.base.utils.AssertUtil;
import com.hqins.common.helper.BeanCopier;
import com.hqins.common.helper.BeanFiller;
import com.hqins.common.utils.HttpUtils;
import com.hqins.common.utils.JsonUtil;
import com.hqins.common.utils.PageUtil;
import com.hqins.common.utils.StringUtil;
import com.hqins.common.web.RequestContextHolder;
import com.hqins.policy.model.request.PolicyEsQueryRequestVo;
import com.hqins.policy.model.vo.es.EsQueryResponseVo;
import com.hqins.policy.model.vo.es.PolicyEsVo;
import com.hqins.um.model.dto.AgentDTO;
import com.hqins.um.model.dto.VisitorInfoDTO;
import lombok.extern.slf4j.Slf4j;
import one.util.streamex.StreamEx;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 消息表业务逻辑实现
 */
@Service
@Slf4j
public class MessageServiceImpl extends ServiceImpl<MessageMapper, Message>  implements MessageService {

    private final MessageMapper messageMapper;
    private final MessageConfig messageConfig;
    private final WechatApi wechatApi;
    private final UmApiWrapper umApiWrapper;
    private final MessageSystemMapper messageSystemMapper;
    private final EmployeeApi employeeApi;
    private final TemplateConfig messageTemplateConfig;
    private final JPushService jPushService;
    private final AppMessageConfig appMessageConfig;
    private final SupervisorEmployeeApi supervisorEmployeeApi;

    @Resource
    private PolicyWrapper policyWrapper;
    @Resource
    private MessageSendTaskMapper messageSendTaskMapper;
    //添加缓存KEY是工号，保存30天
    @CreateCache(expire=30, name = "actEmpXgFlag", timeUnit = TimeUnit.DAYS, cacheType = CacheType.REMOTE)
    private Cache<String, List<PopUpWindowVO>> actEmpXgFlag;



    @Value("${environment-url}")
    private String environmentUrl;

    @Value("${environment}")
    private String environment;

    @CreateCache(name = "messageSystemRead:", expire = 366, timeUnit = TimeUnit.DAYS, cacheType = CacheType.REMOTE)
    Cache<String, Set<String>> readCache;

    @CreateCache(name = "messageReadFlag:", expire = 366, timeUnit = TimeUnit.DAYS, cacheType = CacheType.REMOTE)
    Cache<String, String> readFlagCache;
    private final OrgApiWrapper orgApiWrapper;
    private final OrgApi orgApi;
    private final EmployeeOrgApi employeeOrgApi;

    public MessageServiceImpl(MessageSystemMapper messageSystemMapper, MessageConfig messageConfig, MessageMapper messageMapper, WechatApi wechatApi, UmApiWrapper umApiWrapper, EmployeeApi employeeApi, TemplateConfig messageTemplateConfig, OrgApi orgApi, JPushService jPushService, AppMessageConfig appMessageConfig, EmployeeOrgApi employeeOrgApi, SupervisorEmployeeApi supervisorEmployeeApi, OrgApiWrapper orgApiWrapper) {
        this.messageSystemMapper = messageSystemMapper;
        this.messageConfig = messageConfig;
        this.messageMapper = messageMapper;
        this.wechatApi = wechatApi;
        this.umApiWrapper = umApiWrapper;
        this.employeeApi = employeeApi;
        this.messageTemplateConfig = messageTemplateConfig;
        this.orgApi = orgApi;
        this.jPushService = jPushService;
        this.appMessageConfig = appMessageConfig;
        this.employeeOrgApi = employeeOrgApi;
        this.supervisorEmployeeApi = supervisorEmployeeApi;
        this.orgApiWrapper = orgApiWrapper;
    }

    @Override
    public PageInfo<MessageVO> list(MessageQueryRequest messageQueryRequest) {
        log.info("getUserId:" + RequestContextHolder.getAgentId());
        String employeeCode = RequestContextHolder.getEmployeeCode();
        EmployeeVO employeeInfo = employeeApi.getEmployeeInfo(AgentOrgType.valueOf(RequestContextHolder.getOrgType()), employeeCode);
        //系统消息和服务消息取另外一个表
        if (MessageType.SYSTEM.equals(messageQueryRequest.getMessageType()) ||
                MessageType.SERVICE.equals(messageQueryRequest.getMessageType())){
            String row = AgentOrgType.CHANNEL.name().equals(RequestContextHolder.getOrgType())?"channels":"partners";
            List<MessageSystem> messageSystemList = messageSystemMapper.selectList(
                    new LambdaQueryWrapper<MessageSystem>()
                            .select(MessageSystem::getId, MessageSystem::getTitle, MessageSystem::getMessageBody, MessageSystem::getSendTime, MessageSystem::getWxUrl,
                                    MessageSystem::getH5Url, MessageSystem::getUrlTxt, MessageSystem::getMessageStatus)
                            .eq(MessageSystem::getDeleted, Boolean.FALSE)
                            .eq(MessageSystem::getEntire, Boolean.FALSE)
                            .eq(MessageSystem::getMessageStatus, messageQueryRequest.getMessageType().name())
                            .le(MessageSystem::getSendTime, LocalDateTime.now())
                            .last(" and (FIND_IN_SET('" + employeeCode + "', " + row + ") > 0 or FIND_IN_SET('" + employeeInfo.getOrgCode() + "', " + row + ")>0) order by send_time desc"));
            //加上所有可读消息
            List<MessageSystem> messageSystemList1 = messageSystemMapper.selectList(new LambdaQueryWrapper<MessageSystem>()
                    .select(MessageSystem::getId, MessageSystem::getTitle, MessageSystem::getMessageBody, MessageSystem::getSendTime, MessageSystem::getWxUrl,
                            MessageSystem::getH5Url, MessageSystem::getUrlTxt, MessageSystem::getMessageStatus)
                    .eq(MessageSystem::getDeleted, Boolean.FALSE)
                    .eq(MessageSystem::getEntire, Boolean.TRUE)
                    .eq(MessageSystem::getMessageStatus, messageQueryRequest.getMessageType().name())
                    .le(MessageSystem::getSendTime, LocalDateTime.now()));
            //查询已读消息
            List<MessageVO> messages = new ArrayList<>();
            Set<String> ids = readCache.get(employeeCode);
            if (CollectionUtils.isNotEmpty(messageSystemList1)){
                if (CollectionUtils.isEmpty(messageSystemList)){
                    messageSystemList = new ArrayList<>();
                }
                messageSystemList.addAll(messageSystemList1);
            }
            for (MessageSystem info : messageSystemList) {
                messages.add(MessageVO.builder()
                        .id(info.getId())
                        .viewed(CollectionUtils.isNotEmpty(ids) && ids.contains(info.getId().toString()) ? Boolean.TRUE : Boolean.FALSE)
                        .title(info.getTitle())
                        .content(info.getMessageBody())
                        .publishTime(info.getSendTime())
                        .jumpUrl(MessageSource.MINIPROGRAMPAGE.equals(messageQueryRequest.getSource()) ? info.getWxUrl() : info.getH5Url())
                        .jumpTxt(info.getUrlTxt())
                        .source(MessageContentType.valueOf(info.getMessageStatus()))
                        .build());
            }
            //服务通知包含生日提醒  小程序不查
            if (MessageType.SERVICE.equals(messageQueryRequest.getMessageType())){
                List<Message> messageList = messageMapper.selectList(new LambdaQueryWrapper<Message>()
                        .eq(Message::getUserId, RequestContextHolder.getAgentId())
                        .in(Message::getSource,extracted(MessageType.SERVICE,messageQueryRequest.getSource()))
                        .eq(Message::getDeleted, Boolean.FALSE).orderByDesc(Message::getPublishTime));

                messageList.forEach(info -> messages.add(MessageVO.builder()
                        .id(info.getId())
                        .viewed(info.getViewed())
                        .title(info.getTitle())
                        .content(info.getContent())
                        .publishTime(info.getPublishTime())
                        .jumpUrl(info.getUrl())
                        .jumpTxt(info.getUrlName())
                        .source(MessageContentType.valueOf(info.getSource()))
                        .businessId(info.getBusinessId())
                        .build()));
            }
            //对总和的数据进行排序
            List<MessageVO> collect = messages.stream().sorted(Comparator.comparing(MessageVO::getPublishTime).reversed()).collect(Collectors.toList());
            //已读业务标识
            readFlagCache.put(employeeCode,messageQueryRequest.getMessageType().name());
            return PageUtil.getPageInfo(collect, messageQueryRequest.getCurrent(), messageQueryRequest.getSize());
        }
        Page<Message> page = messageMapper.selectPage(new Page<>(messageQueryRequest.getCurrent(), messageQueryRequest.getSize()),
                new LambdaQueryWrapper<Message>()
                        .eq(Message::getUserId, RequestContextHolder.getAgentId())
                        .eq(Message::getDeleted, Boolean.FALSE)
                        .in(Message::getSource,extracted(messageQueryRequest.getMessageType(),messageQueryRequest.getSource()))
                        .orderByDesc(Message::getPublishTime)
        );

        //已读业务标识
        readFlagCache.put(employeeCode, messageQueryRequest.getMessageType().name());


        return PageUtil.convert(page,message -> MessageVO.builder()
                .id(message.getId())
                .viewed(message.getViewed())
                .title(message.getTitle())
                .content(message.getContent())
                .publishTime(message.getPublishTime())
                .jumpUrl(message.getUrl())
                .jumpTxt(message.getUrlName())
                .source(MessageContentType.valueOf(message.getSource()))
                .businessId(message.getBusinessId())
                .build());
    }

    @Override
    public Integer getViewedCount(MessageSource source) {
        LambdaQueryWrapper<Message> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Message::getUserId, RequestContextHolder.getAgentId());
        queryWrapper.eq(Message::getViewed, Boolean.FALSE);
        queryWrapper.eq(Message::getDeleted, Boolean.FALSE);
        //小程序过过滤生日提醒
        if (MessageSource.MINIPROGRAMPAGE == source) {
            queryWrapper.ne(Message::getSource, MessageContentType.CUSTOMER_BIRTHDAY.name());
            queryWrapper.ne(Message::getSource, MessageContentType.RECEIPT_SUCCESS.name());
            queryWrapper.ne(Message::getSource, MessageContentType.RECEIPT_HEAVEN.name());
            queryWrapper.ne(Message::getSource, MessageContentType.RENEW_RESULT_SUCCESS.name());
            queryWrapper.ne(Message::getSource, MessageContentType.RENEW_RESULT_FAIL.name());
            queryWrapper.ne(Message::getSource, MessageContentType.RENEW_NON_PAYMENT.name());
        }
        queryWrapper.last(" limit 1");
        return messageMapper.selectCount(queryWrapper);
    }

    @Override
    public void add(MessageAddRequest request) {
        log.info("add消息入参,request:{}",JsonUtil.toJSON(request));
        if (inspection(request)) {
            return;
        }
        //多类型智能投顾需要做处理
        String name = request.getSource().name();
        if (request.getSource().name().contains("INTELLIGENT_") &&
            !request.getSource().equals(MessageContentType.INTELLIGENT_CONSULTANT_CARD)){
            name = MessageContentType.INTELLIGENT_CONSULTANT.name();
        }
        MessageObjConfig messageObjConfig = messageConfig.getMessage().get(name);
        if (null == messageObjConfig) {
            return;
        }
        Long visitorId = request.getVisitorId() == null ? RequestContextHolder.getVisitorId() : request.getVisitorId();
        String nickName = StringUtils.isEmpty(request.getNickName()) ? "" : request.getNickName();
        if ("".equals(nickName) && null != visitorId) {
            VisitorInfoDTO userInfoByVisitorId = umApiWrapper.getVisitorInfo(visitorId);
            nickName = null == userInfoByVisitorId ? "" : userInfoByVisitorId.getWeChatInfo().getNickName();
        }

        String content = content(messageObjConfig, nickName, request.getApplicant(), request.getBusinessName(), request.getBusinessContent());
        if (StringUtil.isNotEmpty(request.getMessageBody())){
            content = request.getMessageBody();
        }
        String title = title(messageObjConfig, request.getBusinessName());
        messageMapper.insert(Message.builder()
                .title(title)
                .content(content)
                .urlName(messageObjConfig.getUrlName())
                .userId(request.getUserId())
                .visitorId(visitorId)
                .source(request.getSource().name())
                .businessId(request.getBusinessId())
                .publishTime(LocalDateTime.now())
                .deleted(Boolean.FALSE)
                .viewed(Boolean.FALSE)
                .batched(Boolean.FALSE)
                .batchId(0L)
                .url(request.getUrl())
                .type("")
                .build());

        //发送微信消息
        sentWxMessage(request, nickName);
    }

    private void sentWxMessage(MessageAddRequest request, String nickName) {
        //多类型智能投顾需要做处理
        String name = request.getSource().name();
        if (request.getSource().name().contains("INTELLIGENT_") &&
            !request.getSource().equals(MessageContentType.INTELLIGENT_CONSULTANT_CARD)
        ){
            name = MessageContentType.INTELLIGENT_CONSULTANT.name();
        }
        if (request.getSource().name().contains("INTELLIGENT_HL_")) {
            name = "INTELLIGENT_HL";
        }
        MessageTemplateConfig messageConfig = this.messageTemplateConfig.getMessage().get(name);
        AppObj appMap = appMessageConfig.getMessage().get(name);

        String first = "";
        String key1 = "";
        String key2 = "";
        String key3 = "";
        String key4 = "";
        String remark = "";
        String url = "";
        String templateId = "";
        //将agentId转换employeeCode
        AgentDTO agent = umApiWrapper.getAgentUserInfo(request.getUserId());
        if (null == agent){
            log.info("UM返回数据不存在，消息逻辑结束，相关数据{}",JsonUtil.toJSON(request));
            return;
        }
        String employeeCode = agent.getEmployeeCode();
        switch (request.getSource()){
            case CARD:
                key1 = nickName;
                key2 = messageConfig.getKey2();
                key3 = messageConfig.getKey3();
                key4 = LocalDateTime.now().format(DateTimeFormatter.ofPattern(DatePatterns.YYYY_MM_DD_HH_MM_SS));
                url= environmentUrl +messageConfig.getUrl();
                templateId = "UAT".equals(environment)? messageConfig.getUTemplateId():messageConfig.getPTemplateId();
                break;
            case INVITATION_VIEW:
                key1 = nickName;
                key2 = messageConfig.getKey2();
                key3 = String.format(messageConfig.getKey3(),request.getBusinessName());
                key4 = LocalDateTime.now().format(DateTimeFormatter.ofPattern(DatePatterns.YYYY_MM_DD_HH_MM_SS));
                url= environmentUrl +messageConfig.getUrl();
                templateId = "UAT".equals(environment)? messageConfig.getUTemplateId():messageConfig.getPTemplateId();
                break;
            case INTELLIGENT_CONSULTANT:
            case INTELLIGENT_ANNUITY:
            case INTELLIGENT_BBB:
                key1 = nickName;
                key2 = messageConfig.getKey2();
                key3 = messageConfig.getKey3();
                key4 = LocalDateTime.now().format(DateTimeFormatter.ofPattern(DatePatterns.YYYY_MM_DD_HH_MM_SS));
                url = environmentUrl + messageConfig.getUrl() + "?agentId=" + request.getUserId();
                templateId = "UAT".equals(environment) ? messageConfig.getUTemplateId() : messageConfig.getPTemplateId();

                //发送极光
                jPushService.pushMessage(PushMessageVO.builder()
                        .employeeCode(employeeCode)
                        .pushTitle(appMap.getPushTitle())
                        .pushAlert(String.format(appMap.getPushAlert(),key1))
                        .urlData(appMap.getUrl()+ "?agentId=" + request.getUserId())
                        .newView(appMap.getNewView())
                        .build());
                break;
            case INTELLIGENT_HL_YJK:
            case INTELLIGENT_HL_RX:
            case INTELLIGENT_HL_SRSG:
                key1 = nickName;
                key2 = request.getApplicant();
                key3 = String.format(messageConfig.getKey3(), request.getApplicant());
                key4 = request.getBusinessContent();
                url = environmentUrl + String.format(messageConfig.getUrl(), request.getUserId());
                templateId = "UAT".equals(environment) ? messageConfig.getUTemplateId() : messageConfig.getPTemplateId();
                break;
            case INTELLIGENT_HL_FM:
                key1 = nickName;
                key2 = request.getApplicant();
                key3 = String.format(messageConfig.getKey5(), request.getApplicant());
                key4 = request.getBusinessContent();
                url = environmentUrl + String.format(messageConfig.getUrl(), request.getUserId());
                templateId = "UAT".equals(environment) ? messageConfig.getUTemplateId() : messageConfig.getPTemplateId();
                break;

            case INVITATION_SIGN_UP:
                key1 = nickName;
                key2 = messageConfig.getKey2();
                key3 = String.format(messageConfig.getKey3(), request.getBusinessName());
                key4 = LocalDateTime.now().format(DateTimeFormatter.ofPattern(DatePatterns.YYYY_MM_DD_HH_MM_SS));
                remark = messageConfig.getRemark();
                url = environmentUrl + messageConfig.getUrl() + "?id=" + request.getBusinessId();
                templateId = "UAT".equals(environment) ? messageConfig.getUTemplateId() : messageConfig.getPTemplateId();
                break;
            case INSURE_PLAN:
                key1 = nickName;
                key2 = messageConfig.getKey2();
                key3 = String.format(messageConfig.getKey3(), request.getBusinessName());
                key4 = LocalDateTime.now().format(DateTimeFormatter.ofPattern(DatePatterns.YYYY_MM_DD_HH_MM_SS));
                url = environmentUrl + messageConfig.getUrl();
                templateId = "UAT".equals(environment) ? messageConfig.getUTemplateId() : messageConfig.getPTemplateId();
                break;
            case POLICY_CARD:
                key1 = nickName;
                key2 = messageConfig.getKey2();
                key3 = messageConfig.getKey3();
                key4 = LocalDateTime.now().format(DateTimeFormatter.ofPattern(DatePatterns.YYYY_MM_DD_HH_MM_SS));
                url = environmentUrl + messageConfig.getUrl();
                templateId = "UAT".equals(environment) ? messageConfig.getUTemplateId() : messageConfig.getPTemplateId();

                jPushService.pushMessage(PushMessageVO.builder()
                    .employeeCode(employeeCode)
                    .orgType(agent.getOrgType())
                    .pushTitle(appMap.getPushTitle())
                    .pushAlert(String.format(appMap.getPushAlert(),key1))
                    .urlData(appMap.getUrl())
                    .newView(appMap.getNewView())
                    .build());
                break;
            case ARTICLE_CARD:
                key1 = nickName;
                key2 = messageConfig.getKey2();
                key3 = messageConfig.getKey3();
                key4 = LocalDateTime.now().format(DateTimeFormatter.ofPattern(DatePatterns.YYYY_MM_DD_HH_MM_SS));
                url = environmentUrl + messageConfig.getUrl();
                templateId = "UAT".equals(environment) ? messageConfig.getUTemplateId() : messageConfig.getPTemplateId();

//                jPushService.pushMessage(PushMessageVO.builder()
//                        .employeeCode(employeeCode)
//                        .pushTitle(appMap.getPushTitle())
//                        .pushAlert(String.format(appMap.getPushAlert(),key1))
//                        .urlData(appMap.getUrl())
//                        .newView(appMap.getNewView())
//                        .build());
                break;
//            case INTELLIGENT_CONSULTANT_CARD:
//                key1 = nickName;
//                key2 = messageConfig.getKey2();
//                key3 = messageConfig.getKey3();
//                key4 = LocalDateTime.now().format(DateTimeFormatter.ofPattern(DatePatterns.YYYY_MM_DD_HH_MM_SS));
//                url = environmentUrl + messageConfig.getUrl();
//                templateId = "UAT".equals(environment) ? messageConfig.getUTemplateId() : messageConfig.getPTemplateId();
//
//                jPushService.pushMessage(PushMessageVO.builder()
//                        .employeeCode(employeeCode)
//                        .orgType(agent.getOrgType())
//                        .pushTitle(appMap.getPushTitle())
//                        .pushAlert(String.format(appMap.getPushAlert(),key1))
//                        .urlData(appMap.getUrl())
//                        .newView(appMap.getNewView())
//                        .build());
//                break;
            case INSURE_PLAN_CARD:
                key1 = nickName;
                key2 = messageConfig.getKey2();
                key3 = messageConfig.getKey3();
                key4 = LocalDateTime.now().format(DateTimeFormatter.ofPattern(DatePatterns.YYYY_MM_DD_HH_MM_SS));
                url = environmentUrl + messageConfig.getUrl();
                templateId = "UAT".equals(environment) ? messageConfig.getUTemplateId() : messageConfig.getPTemplateId();
                break;
        }
        wxMessage(Collections.singletonList(WxMessageRequest.builder()
                .toUser(employeeCode)
                .url(url)
                .data(sendWxMessage(first, key1, key2, key3, key4, null, remark))
                .templateId(templateId)
                .build()));
    }

    @Override
    public void batchAdd(BatchMessageAddRequest request) {
        if (CollectionUtils.isNotEmpty(request.getMessageList())) {
            Map<String, MessageObjConfig> configMap = messageConfig.getMessage();
            List<Message> messageList = StreamEx.of(request.getMessageList()).map(message -> {
                MessageObjConfig messageObjConfig = configMap.get(message.getSource().name());
                String content = content(messageObjConfig, message.getNickName(), message.getApplicant(), message.getBusinessName(), message.getBusinessContent());
                if (StringUtil.isNotEmpty(message.getMessageBody())){
                    content = message.getMessageBody();
                }
                String title = title(messageObjConfig, message.getBusinessName());
                return Message.builder()
                        .title(title)
                        .content(content)
                        .urlName(messageObjConfig.getUrlName())
                        .userId(message.getUserId())
                        .visitorId(message.getVisitorId())
                        .source(message.getSource().name())
                        .businessId(message.getBusinessId())
                        .publishTime(LocalDateTime.now())
                        .deleted(Boolean.FALSE)
                        .viewed(Boolean.FALSE)
                        .batched(Boolean.FALSE)
                        .batchId(0L)
                        .url(message.getUrl())
                        .type("")
                        .build();
            }).toList();
            messageMapper.insertBatchSomeColumn(messageList);
        }
    }

    private String content(MessageObjConfig messageObjConfig, String nickName, String applicant, String businessName, String businessContent) {
        if (null == messageObjConfig){
            return "";
        }
        String content = messageObjConfig.getContent();
        if (content.contains("{nickName}"))
            content = content.replace("{nickName}", nickName != null ? nickName : "");
        if (content.contains("{applicant}"))
            content = content.replace("{applicant}", applicant != null ? applicant : "");
        if (content.contains("{businessName}"))
            content = content.replace("{businessName}", businessName != null ? businessName : "");
        if (content.contains("{businessContent}"))
            content = content.replace("{businessContent}", businessContent != null ? businessContent : "");
        if (content.contains("<businessContent>"))
            content = content.replace("<businessContent>", businessContent != null ? businessContent : "");

        return content;
    }

    private String title(MessageObjConfig messageObjConfig, String title) {
        if (null == messageObjConfig){
            return title;
        }
        String titleContent = messageObjConfig.getTitle();
        if (titleContent.contains("${businessName}"))
            titleContent = titleContent.replace("${businessName}", title != null ? title : "");
        return titleContent;
    }

    private boolean inspection(MessageAddRequest request) {
        if (!MessageContentType.CARD.equals(request.getSource())
                && !MessageContentType.INVITATION_VIEW.equals(request.getSource())
                && !MessageContentType.INSURE_PLAN.equals(request.getSource())
                && !MessageContentType.RECEIPT_HEAVEN.equals(request.getSource())) {
            return Boolean.FALSE;
        }
        Page<Message> messagePage = messageMapper.selectPage(PageUtil.oneRecordPage(Message.class), new LambdaQueryWrapper<Message>()
                .eq(Message::getSource, request.getSource().name())
                .eq(Message::getUserId, request.getUserId())
                .eq(Message::getBusinessId, request.getBusinessId())
                .eq(Message::getVisitorId, request.getVisitorId()));
        return CollectionUtils.isNotEmpty(messagePage.getRecords());

    }

    @Override
    public Long deleted(MessageDeletedRequest request) {
        Message message = getById(request.getId());
        BeanFiller.fill(message, request);
        saveOrUpdate(message);
        return message.getId();
    }

    @Override
    public void wxMessage(List<WxMessageRequest> wxMessageRequest) {
        if (CollectionUtils.isEmpty(wxMessageRequest)) {
            return;
        }
        //调用UM获取openId
        Map<String, String> openIdMap = getOpenIdMap(wxMessageRequest);
        //调用微信API
        String accessToken = wechatApi.getWechatAccessToken(WechatAppType.OFFICIAL.getAppCode()).getAccessToken();
        for (WxMessageRequest messageRequest : wxMessageRequest) {
            String s = openIdMap.get(messageRequest.getToUser());
            if (StringUtil.isEmpty(s)){
                continue;
            }
            try {
                //发送消息
                String url = "https://api.weixin.qq.com/cgi-bin/message/template/send?access_token=" + accessToken;
                Map<String, Object> request = new HashMap<>();
                request.put("touser", s);
                request.put("template_id", messageRequest.getTemplateId());
                request.put("url", messageRequest.getUrl());
                request.put("data", messageRequest.getData());
                log.info("请求微信封装参数:{}",JsonUtil.toJSON(request));
                String sendResponse = HttpUtils.postJSON(url, JsonUtil.toJSON(request));
                log.info("发送消息返回结果:{}", sendResponse);
            } catch (IOException e) {
                log.error("发送微信公众号信息失败", e);
            }
        }
    }

    @Override
    public Long addTaskMsg(MessageSendTaskAddRequest request) {
        MessageSendTask messageSendTask = BeanCopier.copyObject(request, MessageSendTask.class);
        messageSendTask.setSourceType(request.getSourceType().name());
        messageSendTask.setType(request.getMessageContentType());
        messageSendTask.setCreateTime(LocalDateTime.now());
        messageSendTask.setUpdateTime(LocalDateTime.now());
        messageSendTaskMapper.insertSelective(messageSendTask);
        return messageSendTask.getId();
    }

    @Override
    public void sendTaskMessage() {
        List<MessageSendTask> messageSendTasks = messageSendTaskMapper.selectList(Wrappers.lambdaQuery(MessageSendTask.class)
                .le(MessageSendTask::getPublishTime, LocalDateTime.now())
                .eq(MessageSendTask::getSend, Boolean.FALSE));
        if (CollectionUtils.isNotEmpty(messageSendTasks)){
            for (MessageSendTask sendTask : messageSendTasks) {
                String content = sendTask.getContent();
                switch (sendTask.getSourceType()){
                    case "PUSH":
                        try {
                            sendTask.setSend(Boolean.TRUE);
                            messageSendTaskMapper.updateById(sendTask);
                            PushMessageVO pushMessageVO = JsonUtil.fromJSON(content, PushMessageVO.class);
                            jPushService.pushMessage(pushMessageVO);
                        }catch (Exception e){
                            log.info("sendTaskMessage发送消息error:::json:{}",JsonUtil.toJSON(sendTask),e);
                        }
                        break;
                    case "LOCAL":
                        try {
                            sendTask.setSend(Boolean.TRUE);
                            messageSendTaskMapper.updateById(sendTask);
                            Message message = JsonUtil.fromJSON(content, Message.class);
                            messageMapper.insert(message);
                        }catch (Exception e){
                            log.info("sendTaskMessage发送消息error:::json:{}",JsonUtil.toJSON(sendTask),e);
                        }
                        break;
                    case "WX":
                        try {
                            sendTask.setSend(Boolean.TRUE);
                            messageSendTaskMapper.updateById(sendTask);
                            List<WxMessageRequest> wxMessageRequests = JsonUtil.toList(content, List.class, WxMessageRequest.class);
                            wxMessage(wxMessageRequests);
                        }catch (Exception e){
                            log.info("sendTaskMessage发送消息error:::json:{}",JsonUtil.toJSON(sendTask),e);
                        }
                        break;
                    default:
                        break;
                }
            }
        }

    }


    private Map<String, String> getOpenIdMap(List<WxMessageRequest> wxMessageRequest) {
        List<String> employeeCode = wxMessageRequest.stream().map(WxMessageRequest::getToUser).collect(Collectors.toList());
        Map<String,String> openIdMap = umApiWrapper.getOpenIdByEmployeeCodeList(employeeCode, AppConsts.APP_ID_WECHAT);
        log.info("调用um返回Open结果集：{}",JsonUtil.toJSON(openIdMap));
        return openIdMap;
    }

    @Override
    public MessageTitleMainVO messageGroup(Long agentId, String employeeCode, AgentOrgType orgType, MessageSource messageSource) {
        List<MessageTitleVO> response = new ArrayList<>();
        EmployeeVO employeeInfo = employeeApi.getEmployeeInfo(AgentOrgType.valueOf(RequestContextHolder.getOrgType()), employeeCode);
        String row = AgentOrgType.CHANNEL.name().equals(RequestContextHolder.getOrgType())?"channels":"partners";
        //获取当前人的系统已读ID
        Set<String> ids = readCache.get(employeeCode);
        //查询各个业务层的未读消息数量，将未读消息分组到各个组内
        for (MessageType value : MessageType.values()) {
            //小程序不遍历投保相关内容
            if (MessageSource.MINIPROGRAMPAGE.equals(messageSource) && MessageType.INSURANCE_POLICY.equals(value)){
                continue;
            }
            int integer = 0;
            String messageValue;
            LocalDateTime createTime;
            //系统消息通过缓存筛选未读数量
            if (value.equals(MessageType.SYSTEM)) {
                MessageSystem messageSystem = messageSystemMapper.selectOne(new LambdaQueryWrapper<MessageSystem>()
                        .eq(MessageSystem::getDeleted, Boolean.FALSE)
                        .eq(MessageSystem::getMessageStatus,value.name())
                        .le(MessageSystem::getSendTime, LocalDateTime.now())
                        .last(" and (is_all = 1 or (FIND_IN_SET('" + employeeCode + "', " + row + ") > 0 or FIND_IN_SET('"+employeeInfo.getOrgCode()+"', " + row + ")>0)) order by create_time desc limit 1"));

                integer = messageSystemMapper.selectCount(new LambdaQueryWrapper<MessageSystem>()
                        .eq(MessageSystem::getDeleted, Boolean.FALSE)
                        .eq(MessageSystem::getMessageStatus, value.name())
                        .le(MessageSystem::getSendTime, LocalDateTime.now())
                        .notIn(CollectionUtils.isNotEmpty(ids), MessageSystem::getId, ids)
                        .last(" and (is_all = 1 or (FIND_IN_SET('" + employeeCode + "', " + row + ") > 0 or FIND_IN_SET('" + employeeInfo.getOrgCode() + "', " + row + ")>0))"));
                if (null != messageSystem){
                    messageValue =  messageSystem.getMessageBody();
                    createTime =  messageSystem.getCreateTime();
                }else{
                    messageValue = null;
                    createTime = null;
                }
            }else if (value.equals(MessageType.SERVICE)) {
                MessageSystem messageSystem = messageSystemMapper.selectOne(new LambdaQueryWrapper<MessageSystem>()
                        .eq(MessageSystem::getDeleted, Boolean.FALSE)
                        .eq(MessageSystem::getMessageStatus,MessageType.SERVICE.name())
                        .le(MessageSystem::getSendTime, LocalDateTime.now())
                        .last(" and (is_all = 1 or (FIND_IN_SET('" + employeeCode + "', " + row + ") > 0 or FIND_IN_SET('"+employeeInfo.getOrgCode()+"', " + row + ")>0)) order by create_time desc limit 1"));
                Message message = messageMapper.selectOne(new LambdaQueryWrapper<Message>()
                        .eq(Message::getDeleted, Boolean.FALSE)
                        .eq(Message::getUserId, agentId)
                        .in(Message::getSource, extracted(value, messageSource))
                        .orderByDesc(Message::getPublishTime)
                        .last("limit 1"));
                if (null != message && null !=messageSystem){
                    messageValue = message.getPublishTime().isAfter(messageSystem.getSendTime()) ?message.getContent() : messageSystem.getMessageBody();
                    createTime = message.getPublishTime().isAfter(messageSystem.getSendTime()) ? message.getPublishTime() : messageSystem.getSendTime();
                }else if (null == message && null != messageSystem){
                    messageValue =  messageSystem.getMessageBody();
                    createTime =  messageSystem.getCreateTime();
                }else if (null != message){
                    messageValue = message.getContent();
                    createTime =  message.getPublishTime();
                }else{
                    messageValue = null;
                    createTime = null;
                }
                integer = messageSystemMapper.selectCount(new LambdaQueryWrapper<MessageSystem>()
                        .eq(MessageSystem::getDeleted, Boolean.FALSE)
                        .eq(MessageSystem::getMessageStatus,MessageType.SERVICE.name())
                        .le(MessageSystem::getSendTime, LocalDateTime.now())
                        .notIn(CollectionUtils.isNotEmpty(ids), MessageSystem::getId, ids)
                        .last(" and (is_all = 1 or (FIND_IN_SET('" + employeeCode + "', " + row + ") > 0 or FIND_IN_SET('" + employeeInfo.getOrgCode() + "', " + row + ")>0))"));
                integer += messageMapper.selectCount(new LambdaQueryWrapper<Message>()
                        .eq(Message::getDeleted, Boolean.FALSE)
                        .eq(Message::getUserId, agentId)
                        .eq(Message::getViewed, Boolean.FALSE)
                        .in(Message::getSource, extracted(value, messageSource)));
            } else {
                Message message = messageMapper.selectOne(new LambdaQueryWrapper<Message>()
                        .eq(Message::getDeleted, Boolean.FALSE)
                        .eq(Message::getUserId, agentId)
                        .in(Message::getSource, extracted(value, messageSource))
                        .orderByDesc(Message::getPublishTime)
                        .last("limit 1"));

                integer = messageMapper.selectCount(new LambdaQueryWrapper<Message>()
                        .eq(Message::getDeleted, Boolean.FALSE)
                        .eq(Message::getUserId, agentId)
                        .eq(Message::getViewed, Boolean.FALSE)
                        .in(Message::getSource, extracted(value, messageSource)));
                messageValue = null == message ? "" : message.getContent();
                createTime = null == message ?null : message.getCreateTime();
            }
            response.add(MessageTitleVO.builder()
                    .messageType(value)
                    .count(integer)
                    .createTime(createTime)
                    .messageValue(messageValue)
                    .build());
        }

        return MessageTitleMainVO.builder().messageTitleVOS(response)
                .readMessageType(null == readFlagCache.get(employeeCode)?"":readFlagCache.get(employeeCode))
                .build();
    }

    @Override
    public void viewed(MessageViewedRequest request) {
        log.info("已读消息接口入参{}",JsonUtil.toJSON(request));
        String employeeCode = RequestContextHolder.getEmployeeCode();
        String ids = request.getIds();
        String sysIds = request.getSysIds();
        Set<String> redisId = readCache.get(employeeCode);
        //系统消息标识在Redis  普通消息已读标识在数据库，服务消息特殊两个表都有涉及
        switch (request.getMessageType()){
            case SERVICE:
                if (!StringUtils.isEmpty(request.getIds())){
                    messageMapper.messageUpdateViewedByIds(ids);
                }
            case SYSTEM:
                if (!StringUtils.isEmpty(request.getSysIds())){
                    redisId =CollectionUtils.isEmpty(redisId)? new HashSet<>():redisId;
                    redisId.addAll(new HashSet<>(Arrays.asList(sysIds.split(","))));
                    readCache.put(employeeCode,redisId);
                }
                break;
            default:
                if (!StringUtils.isEmpty(request.getIds())){
                    messageMapper.messageUpdateViewedByIds(ids);
                }
                break;
        }
    }

    @Override
    public void viewedAllByType(MessageType messageType,MessageSource source) {
        log.info("已读消息接口入参{}",JsonUtil.toJSON(messageType));
        String employeeCode = RequestContextHolder.getEmployeeCode();
        Long agentId = RequestContextHolder.getAgentId();
        Set<String> redisId = readCache.get(employeeCode);

        switch (messageType){
            case SERVICE:
                messageMapper.messageUpdateViewedByTypeAndUser(extracted(messageType,source).stream().map(String::valueOf).collect(Collectors.toList()),agentId);
            case SYSTEM:
                redisId =CollectionUtils.isEmpty(redisId)? new HashSet<>():redisId;
                MessageType param = messageType.equals(MessageType.SERVICE)? MessageType.SERVICE:MessageType.SYSTEM;

                List<MessageSystem> messageSystemList = messageSystemMapper.selectList(new LambdaQueryWrapper<MessageSystem>()
                        .eq(MessageSystem::getMessageStatus, param).eq(MessageSystem::getDeleted, Boolean.FALSE));

                if (CollectionUtils.isNotEmpty(messageSystemList)){
                    List<String> collect = messageSystemList.stream().map(MessageSystem::getId).map(String::valueOf).collect(Collectors.toList());
                    redisId.addAll(new HashSet<>(collect));
                    readCache.put(employeeCode,redisId);
                }
                break;
            default:
                messageMapper.messageUpdateViewedByTypeAndUser(extracted(messageType,source).stream().map(String::valueOf).collect(Collectors.toList()),agentId);
                break;
        }
        //清理已读规则标识
        readFlagCache.remove(employeeCode);
    }

    @Override
    public ViewedTitleVO viewedTitle(String employeeCode, Long agentId,MessageSource messageSource) {
        //先查询reds获取当前人已读的ID 查询机构表查找未读的消息--------------------
        Set<String> strings = readCache.get(employeeCode);
        String row = AgentOrgType.CHANNEL.name().equals(RequestContextHolder.getOrgType())?"channels":"partners";
        EmployeeVO employeeInfo = employeeApi.getEmployeeInfo(AgentOrgType.valueOf(RequestContextHolder.getOrgType()), employeeCode);

        //先查询所有人可读的消息，如果没有，在查询权限内的消息
        MessageSystem messageSystem = messageSystemMapper.selectOne(new LambdaQueryWrapper<MessageSystem>()
                .eq(MessageSystem::getDeleted, Boolean.FALSE)
                .le(MessageSystem::getSendTime, LocalDateTime.now())
                .notIn(CollectionUtils.isNotEmpty(strings), MessageSystem::getId, strings)
                .last(" and ((FIND_IN_SET('" + employeeCode + "', " + row + ") > 0 " +
                        "or FIND_IN_SET('"+employeeInfo.getOrgCode()+"', " + row + ")>0)or is_all = true) "+
                        "order by send_time desc limit 1 "));
        //先查询reds获取当前人已读的ID 查询机构表查找未读的消息--------------------end

        //在查询普通消息表拿到当前人未读的消息------------------------------------
        Message message = messageMapper.selectOne(new LambdaQueryWrapper<Message>()
                .eq(Message::getDeleted, Boolean.FALSE)
                .eq(Message::getViewed, Boolean.FALSE)
                .eq(Message::getUserId, agentId)
                .notIn(MessageSource.MINIPROGRAMPAGE.equals(messageSource),
                        Message::getSource,MessageContentType.CUSTOMER_BIRTHDAY.name(),
                        MessageContentType.RECEIPT_SUCCESS.name(),
                        MessageContentType.TODAY_CUSTOMER_BIRTHDAY.name(),
                        MessageContentType.CUSTOMER_MONTH_BIRTHDAY.name(),
                        MessageContentType.RECEIPT_HEAVEN.name(),
                        MessageContentType.RENEW_RESULT_SUCCESS.name(),
                        MessageContentType.RENEW_RESULT_FAIL.name(),
                        MessageContentType.RENEW_NON_PAYMENT.name(),
                        MessageContentType.AGENT_ORDER_SIGN.name(),
                        MessageContentType.AGENT_ORDER_SIGN_SUCCESS.name(),
                        MessageContentType.PAY_FILED.name(),
                        MessageContentType.URGE.name(),
                        MessageContentType.CHECK_FILED.name(),
                        MessageContentType.TRANSFER_TO_MANUAL_UNDERWRITING.name(),
                        MessageContentType.TIME_TRANSFER.name(),
                        MessageContentType.UNDERWRITING_CONCLUSION.name(),
                        MessageContentType.WITHDRAWAL_MESSAGE.name(),
                        MessageContentType.DEDUCTION_FAILED.name(),
                        MessageContentType.SUCCESSFUL_UNDERWRITING.name(),
                        MessageContentType.INTELLIGENT_UNDERWRITING.name(),
                        MessageContentType.HANDLE_LETTER.name(),
                        MessageContentType.HANDLE_LETTER_SUBMIT.name(),
                        MessageContentType.QKB_CLOCK_IN_ALERT.name(),
                        MessageContentType.RENEW_COLLECTION_ALERT.name(),
                        MessageContentType.ENDORSE_POLICY_INVALID.name(),
                        MessageContentType.REAL_NAME_FAIL.name(),
                        MessageContentType.REAL_NAME_SUCCESS.name(),
                        MessageContentType.MANUAL_REVIEW.name(),
                        MessageContentType.NOT_ID_REAL_NAME_FAIL.name(),
                        MessageContentType.AUDIT_FAIL.name(),
                        MessageContentType.AUDIT_SUCCESSFUL.name(),
                        MessageContentType.HOME_POLICY_PAY.name(),
                        MessageContentType.REAL_NAME_FAILURE.name(),
                        MessageContentType.ESCROW_SUCCESS_FIRST.name(),
                        MessageContentType.ESCROW_CLEAN.name(),
                        MessageContentType.ESCROW_FAILURE.name(),
                        MessageContentType.GOAL_APPROVE_MESSAGE.name(),
                        MessageContentType.GOAL_INVALID_MESSAGE.name(),
                        MessageContentType.GOAL_SUBMIT_INVALID_MESSAGE.name(),
                        MessageContentType.GOAL_APPROVE_REMIND.name(),
                        MessageContentType.CANDIDATE_INFORMATION.name(),
                        MessageContentType.PENDING_FIRST_INTERVIEW_EVALUATION.name(),
                        MessageContentType.FIRST_INTERVIEW_PASSED.name(),
                        MessageContentType.FIRST_INTERVIEW_REJECTED.name(),
                        MessageContentType.SECOND_INTERVIEW_PASSED.name(),
                        MessageContentType.SECOND_INTERVIEW_REJECTED.name(),
                        MessageContentType.CANDIDATE_PROFILE_SUBMITTED.name(),
                        MessageContentType.INITIAL_REVIEW_REJECTED.name(),
                        MessageContentType.FINAL_REVIEW_PASSED.name(),
                        MessageContentType.FINAL_REVIEW_REJECTED.name(),
                        MessageContentType.CONTRACT_TERMINATION.name(),
                        MessageContentType.FEEDBACK.name(),
                        MessageContentType.REACH_THE_STANDARD_OF_HONOR.name(),
                        MessageContentType.INSURE_PLAN_ANEW.name(),
                        MessageContentType.CONTRACT_TERMINATION_FAIL.name(),
                        MessageContentType.FEEDBACK.name(),
                        MessageContentType.AI_DAY_ARTICLE.name()

                )
                .orderByDesc(Message::getCreateTime)
                .last("limit 1"));
        if (null == messageSystem && null == message) {
            return ViewedTitleVO.builder().mark(Boolean.FALSE).build();
        }
        //在查询普通消息表拿到当前人未读的消息------------------------------------end

        //获取访客名称---------------------------------------------------------
        String visitorName = "";
        if (null != message && null!=message.getVisitorId()){
            try {
                VisitorInfoDTO visitorInfoDTO = umApiWrapper.getVisitorInfo(message.getVisitorId());
                if (null != visitorInfoDTO){
                    visitorName = visitorInfoDTO.getWeChatInfo().getNickName();
                }
            }catch (Exception e){
                log.error("调用center获取客户信息异常，可能当前visitorId不存在:{}",message.getVisitorId());
            }
        }
        //获取访客名称---------------------------------------------------------end

        if (null != messageSystem && null != message){
            //对比啷个数据时间前后，只展示最近的一条数据信息
            boolean show = message.getCreateTime().isBefore(messageSystem.getSendTime());
            //计算时间差 相差的分钟数
            Duration duration = Duration.between(show?messageSystem.getCreateTime():message.getCreateTime(),LocalDateTime.now());
            return ViewedTitleVO.builder()
                    .mark(Boolean.TRUE)
                    .createTime(duration.toMinutes())
                    .messageType(show ? messageSystem.getMessageStatus() : reverseElection(MessageContentType.valueOf(message.getSource())))
                    .visitorName(show ? null : visitorName)
                    .content(show ? messageSystem.getMessageBody() : message.getContent())
                    .build();
        }else if (null != messageSystem){
            return ViewedTitleVO.builder()
                    .mark(Boolean.TRUE)
                    .messageType(messageSystem.getMessageStatus())
                    .createTime(Duration.between(messageSystem.getCreateTime(),LocalDateTime.now()).toMinutes())
                    .visitorName(null)
                    .content(messageSystem.getMessageBody())
                    .build();
        }else {
            return ViewedTitleVO.builder()
                    .mark(Boolean.TRUE)
                    .messageType(reverseElection(MessageContentType.valueOf(message.getSource())))
                    .createTime(Duration.between(message.getCreateTime(),LocalDateTime.now()).toMinutes())
                    .visitorName(visitorName)
                    .content(message.getContent())
                    .build();
        }

    }

    public  Map<String,Map<String,String>> sendWxMessage(String first,String k1,String k2,String k3,String k4,String k5,String remark) {
        Map<String,Map<String,String>> map = new HashMap<>();
        if (StringUtil.isNotEmpty(first)){
            Map<String,String> childMap = new HashMap<>();
            childMap.put("value",first);
            map.put("first",childMap);
        }

        if (StringUtil.isNotEmpty(k1)){
            Map<String,String> childMap = new HashMap<>();
            childMap.put("value", k1);
            map.put("keyword1",childMap);
        }

        if (StringUtil.isNotEmpty(k2)) {
            Map<String,String> childMap = new HashMap<>();
            childMap.put("value", k2);
            map.put("keyword2", childMap);
        }

        if (StringUtil.isNotEmpty(k3)) {
            Map<String,String> childMap = new HashMap<>();
            childMap.put("value", k3);
            map.put("keyword3", childMap);
        }

        if (StringUtil.isNotEmpty(k4)) {
            Map<String,String> childMap = new HashMap<>();
            childMap.put("value", k4);
            map.put("keyword4", childMap);
        }

        if (StringUtil.isNotEmpty(k5)) {
            Map<String,String> childMap = new HashMap<>();
            childMap.put("value", k5);
            map.put("keyword5", childMap);
        }

        if (StringUtil.isNotEmpty(remark)) {
            Map<String,String> childMap = new HashMap<>();
            childMap.put("value", remark);
            map.put("remark", childMap);
        }
        return map;
    }

    private  List<MessageContentType> extracted(MessageType value,MessageSource messageSource) {
        //小程序过过滤生日提醒、空中回执提醒、回执成功提醒、续期缴费成功提醒、续交缴费失败提醒、续期交费提醒
        List<MessageContentType> params = new ArrayList<>();
        switch (value){
            //保单通知
            case INSURANCE_POLICY:
                if (MessageSource.H5.equals(messageSource) || messageSource.equals(MessageSource.APP)){
                    params.add(MessageContentType.RENEW_COLLECTION_ALERT);
                    params.add(MessageContentType.ENDORSE_POLICY_INVALID);
                    params.add(MessageContentType.AGENT_ORDER_SIGN);
                    params.add(MessageContentType.AGENT_ORDER_SIGN_SUCCESS);
                    params.add(MessageContentType.PAY_FILED);
                    params.add(MessageContentType.URGE);
                    params.add(MessageContentType.CHECK_FILED);
                    params.add(MessageContentType.TRANSFER_TO_MANUAL_UNDERWRITING);
                    params.add(MessageContentType.TIME_TRANSFER);
                    params.add(MessageContentType.UNDERWRITING_CONCLUSION);
                    params.add(MessageContentType.WITHDRAWAL_MESSAGE);
                    params.add(MessageContentType.DEDUCTION_FAILED);
                    params.add(MessageContentType.SUCCESSFUL_UNDERWRITING);
                    params.add(MessageContentType.INTELLIGENT_UNDERWRITING);
                    params.add(MessageContentType.HANDLE_LETTER);
                    params.add(MessageContentType.HANDLE_LETTER_SUBMIT);
                    params.add(MessageContentType.HANDLE_LETTER_CHANGE);

                    params.add(MessageContentType.RENEW_NON_PAYMENT);
                    params.add(MessageContentType.RENEW_RESULT_SUCCESS);
                    params.add(MessageContentType.RENEW_RESULT_FAIL);
                    params.add(MessageContentType.RECEIPT_SUCCESS);
                    params.add(MessageContentType.RECEIPT_HEAVEN);
                    params.add(MessageContentType.SUCCESSFUL_UNDERWRITING);
                    params.add(MessageContentType.SUCCESSFUL_UNDERWRITING_TEAM);
                    params.add(MessageContentType.SUCCESSFUL_UNDERWRITING_SUPERVISOR);
                    params.add(MessageContentType.SUCCESSFUL_UNDERWRITING_SUPERVISOR_ZONGBU);
                    params.add(MessageContentType.STATEMENT_UNDERWRITING);
                    params.add(MessageContentType.STATEMENT_UNDERWRITING_TEAM);
                    params.add(MessageContentType.STATEMENT_UNDERWRITING_SUPERVISOR);
                    params.add(MessageContentType.STATEMENT_UNDERWRITING_SUPERVISOR_ZONGBU);
                    params.add(MessageContentType.REAL_NAME_FAIL);
                    params.add(MessageContentType.REAL_NAME_SUCCESS);
                    params.add(MessageContentType.MANUAL_REVIEW);
                    params.add(MessageContentType.NOT_ID_REAL_NAME_FAIL);
                    params.add(MessageContentType.AUDIT_FAIL);
                    params.add(MessageContentType.AUDIT_SUCCESSFUL);

                    params.add(MessageContentType.HOME_POLICY_PAY);
                    params.add(MessageContentType.REAL_NAME_FAILURE);
                    params.add(MessageContentType.ESCROW_SUCCESS_FIRST);
                    params.add(MessageContentType.ESCROW_CLEAN);
                    params.add(MessageContentType.ESCROW_FAILURE);
                    params.add(MessageContentType.REAL_NAME_MANUAL_CHECK);
                    params.add(MessageContentType.REAL_NAME_MANUAL_CHECK_FAILURE);

                    params.add(MessageContentType.RENEW_REINSTATE);
                    params.add(MessageContentType.POLICY_REPORT_PRODUCE);
                    params.add(MessageContentType.CHECK_PROTECT_RIGHT_REPORT_FAILURE);
                    params.add(MessageContentType.CHECK_HOME_PROTECT_REPORT_FAILURE);
                    params.add(MessageContentType.CUSTOMER_CHECK_PRODUCT);
                    params.add(MessageContentType.CUSTOMER_UPLOAD_EXTERNAL_POLICY);
                    params.add(MessageContentType.Dai_Hui_Fang);
                    params.add(MessageContentType.Bao_Dan_Di_Song);
                    params.add(MessageContentType.Hui_Zhi_Dai_Qian_Shou);
                    params.add(MessageContentType.Bao_Quan_Shou_Li);
                    params.add(MessageContentType.Bao_Quan_Sheng_Xiao);
                    params.add(MessageContentType.Li_Pei_Shou_Li);
                    params.add(MessageContentType.Li_Pei_Jie_An);
                    params.add(MessageContentType.RENEW_TO_BE_CONTINUED);
                    params.add(MessageContentType.RENEW_TRANS_RENEWS);
                    params.add(MessageContentType.SEND_THE_LETTER);
                    params.add(MessageContentType.AGENT_INFORM_CONTENT_NOTICE);
                    params.add(MessageContentType.DUAN_XIAN_XU_BAO_QIAN20);
                    params.add(MessageContentType.DUAN_XIAN_XU_BAO_TODAY);
                    params.add(MessageContentType.DUAN_XIAN_XU_BAO_HOU50);
                    break;
                }
                params.add(MessageContentType.OTHER);
                break;
            //访客动态提醒
            case VISITOR:
                params.add(MessageContentType.INSURE_PLAN_CARD);
                params.add(MessageContentType.INTELLIGENT_CONSULTANT_CARD);
                params.add(MessageContentType.ARTICLE_CARD);
                params.add(MessageContentType.POLICY_CARD);
                params.add(MessageContentType.INSURE_PLAN);
                params.add(MessageContentType.CARD);
                params.add(MessageContentType.INTELLIGENT_CONSULTANT);
                params.add(MessageContentType.INTELLIGENT_HL_YJK);
                params.add(MessageContentType.INTELLIGENT_HL_RX);
                params.add(MessageContentType.INTELLIGENT_HL_FM);
                params.add(MessageContentType.INTELLIGENT_BBB);
                params.add(MessageContentType.INTELLIGENT_ANNUITY);
                params.add(MessageContentType.INVITATION_VIEW);
                params.add(MessageContentType.INTELLIGENT_HL_SRSG);
                params.add(MessageContentType.TaiLi_Wait_Send);
                params.add(MessageContentType.TaiLi_CanYu_INVALID);
                break;
            case REPORT:
                params.add(MessageContentType.REPORT);
                params.add(MessageContentType.INTELLIGENT_CONSULTANT);
                params.add(MessageContentType.HEALTH_SHOT_NOTICE);
                break;
            case SERVICE:
                if (messageSource.equals(MessageSource.H5) || messageSource.equals(MessageSource.APP)){
                    params.add(MessageContentType.CUSTOMER_BIRTHDAY);
                    params.add(MessageContentType.TODAY_CUSTOMER_BIRTHDAY);
                    params.add(MessageContentType.CUSTOMER_MONTH_BIRTHDAY);
                    params.add(MessageContentType.REISSUE_PASS_THE_AUDIT);
                    params.add(MessageContentType.REISSUE_AUDIT_FAILURE);
                    params.add(MessageContentType.EXCEPTION_RECORD_PASS_THE_AUDIT);
                    params.add(MessageContentType.EXCEPTION_RECORD_AUDIT_FAILURE);
                    params.add(MessageContentType.SPOT_CHECK_AUDIT_FAILURE);
                    params.add(MessageContentType.QKB_CLOCK_IN_ALERT);
                    params.add(MessageContentType.LEAVE_PASS_THE_AUDIT);
                    params.add(MessageContentType.LEAVE_AUDIT_FAILURE);
                    params.add(MessageContentType.LEAVE_REVOKE_AUDIT_FAILURE);
                    params.add(MessageContentType.LEAVE_REVOKE_PASS_THE_AUDIT);
                    params.add(MessageContentType.WORK_ROOM_VIEW);
                    params.add(MessageContentType.WORK_ROOM_PRODUCT_VIEW);
                    params.add(MessageContentType.WORK_ROOM_PARTICIPATE);
                    params.add(MessageContentType.NEW_CLUE);
                    params.add(MessageContentType.INVITATION_SIGN_UP);
                    params.add(MessageContentType.CUSTOMER_FOLLOW);
                    params.add(MessageContentType.Digital_Persion_Card_Completed);
                    params.add(MessageContentType.Digital_Persion_Plan_Completed);
                    params.add(MessageContentType.Digital_Persion_Plan_FAILURE);
                    params.add(MessageContentType.SCHEDULE_CUSTOMIZATION);
                    params.add(MessageContentType.Digital_Persion_Video_Completed);
                    params.add(MessageContentType.ORPHAN_ADDING_NOTICES);
                    params.add(MessageContentType.Team_Partner_Birthday);
                    params.add(MessageContentType.Ke_Hu_Can_Yu_Activity);
                    params.add(MessageContentType.Digital_Persion_Real_Video_Completed);
                    params.add(MessageContentType.Digital_Persion_Real_Video_Failure);
                    params.add(MessageContentType.WORK_GOOD);
                    params.add(MessageContentType.WORK_READOVER);

                    params.add(MessageContentType.CANDIDATE_INFORMATION);
                    params.add(MessageContentType.PENDING_FIRST_INTERVIEW_EVALUATION);
                    params.add(MessageContentType.FIRST_INTERVIEW_PASSED);
                    params.add(MessageContentType.FIRST_INTERVIEW_REJECTED);
                    params.add(MessageContentType.SECOND_INTERVIEW_PASSED);
                    params.add(MessageContentType.SECOND_INTERVIEW_REJECTED);
                    params.add(MessageContentType.CANDIDATE_PROFILE_SUBMITTED);
                    params.add(MessageContentType.INITIAL_REVIEW_REJECTED);
                    params.add(MessageContentType.FINAL_REVIEW_PASSED);
                    params.add(MessageContentType.FINAL_REVIEW_REJECTED);
                    params.add(MessageContentType.CONTRACT_TERMINATION);
                    params.add(MessageContentType.CONTRACT_TERMINATION_FAIL);


                    params.add(MessageContentType.GOAL_APPROVE_MESSAGE);
                    params.add(MessageContentType.GOAL_INVALID_MESSAGE);
                    params.add(MessageContentType.GOAL_SUBMIT_INVALID_MESSAGE);
                    params.add(MessageContentType.GOAL_APPROVE_REMIND);
                    params.add(MessageContentType.FEEDBACK);
                    params.add(MessageContentType.AI_DAY_ARTICLE);
                    params.add(MessageContentType.REACH_THE_STANDARD_OF_HONOR);
                    params.add(MessageContentType.INSURE_PLAN_ANEW);

                }
                params.add(MessageContentType.CAR_T_FAIL);
                params.add(MessageContentType.CAR_T_THROUGH);
                params.add(MessageContentType.CUSTOMER_ASSESSMENT);


                break;
            default:
                break;
        }
        return params;
    }

    private  String reverseElection(MessageContentType contentType){
        String response = "";
        switch (contentType){
            case QKB_CLOCK_IN_ALERT:
            case CUSTOMER_MONTH_BIRTHDAY:
            case CUSTOMER_BIRTHDAY:
            case TODAY_CUSTOMER_BIRTHDAY:
            case DAY_ARTICLE:
            case CAR_T_FAIL:
            case CAR_T_THROUGH:
            case REISSUE_PASS_THE_AUDIT:
            case REISSUE_AUDIT_FAILURE:
            case EXCEPTION_RECORD_PASS_THE_AUDIT:
            case EXCEPTION_RECORD_AUDIT_FAILURE:
            case SPOT_CHECK_AUDIT_FAILURE:
            case LEAVE_AUDIT_FAILURE:
            case LEAVE_PASS_THE_AUDIT:
            case LEAVE_REVOKE_AUDIT_FAILURE:
            case LEAVE_REVOKE_PASS_THE_AUDIT:
            case WORK_ROOM_VIEW:
            case WORK_ROOM_PRODUCT_VIEW:
            case WORK_ROOM_PARTICIPATE:
            case NEW_CLUE:
            case INVITATION_SIGN_UP:
            case CUSTOMER_FOLLOW:
            case Digital_Persion_Card_Completed:
            case Digital_Persion_Plan_Completed:
            case Digital_Persion_Plan_FAILURE:
            case SCHEDULE_CUSTOMIZATION:
            case Digital_Persion_Video_Completed:
            case ORPHAN_ADDING_NOTICES:
            case Team_Partner_Birthday:
            case Ke_Hu_Can_Yu_Activity:
            case Digital_Persion_Real_Video_Completed:
            case Digital_Persion_Real_Video_Failure:
            case WORK_GOOD:
            case WORK_READOVER:
            case FEEDBACK:
            case AI_DAY_ARTICLE:
            case CANDIDATE_INFORMATION:
            case PENDING_FIRST_INTERVIEW_EVALUATION:
            case FIRST_INTERVIEW_PASSED:
            case FIRST_INTERVIEW_REJECTED:
            case SECOND_INTERVIEW_PASSED:
            case SECOND_INTERVIEW_REJECTED:
            case CANDIDATE_PROFILE_SUBMITTED:
            case INITIAL_REVIEW_REJECTED:
            case FINAL_REVIEW_PASSED:
            case FINAL_REVIEW_REJECTED:
            case CONTRACT_TERMINATION:
            case REACH_THE_STANDARD_OF_HONOR:
            case INSURE_PLAN_ANEW:
            case CONTRACT_TERMINATION_FAIL:
                response =  MessageType.SERVICE.name();
                break;
            case REPORT:
            case INTELLIGENT_CONSULTANT:
            case HEALTH_SHOT_NOTICE:
                response =  MessageType.REPORT.name();
                break;
            case CARD:
            case INSURE_PLAN:
            case POLICY_CARD:
            case ARTICLE_CARD:
            case INVITATION_VIEW:
            case INTELLIGENT_BBB:
            case INSURE_PLAN_CARD:
            case INTELLIGENT_HL_RX:
            case INTELLIGENT_HL_FM:
            case INTELLIGENT_HL_YJK:
            case INTELLIGENT_HL_SRSG:
            case INTELLIGENT_ANNUITY:
            case INTELLIGENT_CONSULTANT_CARD:
            case TaiLi_Wait_Send:
            case TaiLi_CanYu_INVALID:
                response = MessageType.VISITOR.name();
                break;
            case AGENT_ORDER_SIGN:
            case AGENT_ORDER_SIGN_SUCCESS:
            case PAY_FILED:
            case URGE:
            case CHECK_FILED:
            case TRANSFER_TO_MANUAL_UNDERWRITING:
            case TIME_TRANSFER:
            case UNDERWRITING_CONCLUSION:
            case WITHDRAWAL_MESSAGE:
            case DEDUCTION_FAILED:
            case SUCCESSFUL_UNDERWRITING:
            case SUCCESSFUL_UNDERWRITING_TEAM:
            case SUCCESSFUL_UNDERWRITING_SUPERVISOR:
            case SUCCESSFUL_UNDERWRITING_SUPERVISOR_ZONGBU:
            case STATEMENT_UNDERWRITING:
            case STATEMENT_UNDERWRITING_TEAM:
            case STATEMENT_UNDERWRITING_SUPERVISOR:
            case STATEMENT_UNDERWRITING_SUPERVISOR_ZONGBU:
            case INTELLIGENT_UNDERWRITING:
            case HANDLE_LETTER:
            case HANDLE_LETTER_SUBMIT:
            case HANDLE_LETTER_CHANGE:
            case RENEW_NON_PAYMENT:
            case RENEW_RESULT_SUCCESS:
            case RENEW_RESULT_FAIL:
            case RECEIPT_SUCCESS:
            case RECEIPT_HEAVEN:
            case RENEW_COLLECTION_ALERT:
            case ENDORSE_POLICY_INVALID:
            case HOME_POLICY_PAY:
            case REAL_NAME_FAILURE:
            case POLICY_REPORT_PRODUCE:
            case CHECK_PROTECT_RIGHT_REPORT_FAILURE:
            case CHECK_HOME_PROTECT_REPORT_FAILURE:
            case CUSTOMER_CHECK_PRODUCT:
            case CUSTOMER_UPLOAD_EXTERNAL_POLICY:
            case ESCROW_FAILURE:
            case ESCROW_CLEAN:
            case Dai_Hui_Fang:
            case Bao_Dan_Di_Song:
            case Hui_Zhi_Dai_Qian_Shou:
            case Bao_Quan_Shou_Li:
            case Bao_Quan_Sheng_Xiao:
            case Li_Pei_Shou_Li:
            case Li_Pei_Jie_An:
            case ESCROW_SUCCESS_FIRST:
            case REAL_NAME_MANUAL_CHECK:
            case REAL_NAME_MANUAL_CHECK_FAILURE:
            case RENEW_TO_BE_CONTINUED:
            case RENEW_TRANS_RENEWS:
            case RENEW_REINSTATE:
            case SEND_THE_LETTER:
            case AGENT_INFORM_CONTENT_NOTICE:
            case DUAN_XIAN_XU_BAO_QIAN20:
            case DUAN_XIAN_XU_BAO_TODAY:
            case DUAN_XIAN_XU_BAO_HOU50:
                response = MessageType.INSURANCE_POLICY.name();
                break;
            default:
                break;
        }
        return response;
    }

    @Override
    public String aggrSendMsg(AggrSendMessageRequest aggrSendMessageRequest){
        log.info("聚合消息发送,开始,aggrSendMessageRequest:{}",JsonUtil.toJSON(aggrSendMessageRequest));
        String employeeCode = aggrSendMessageRequest.getEmployeeCode();
        String messageContentType = aggrSendMessageRequest.getMessageContentType();
        List<MessageSendChannel> messageSendChannelList = aggrSendMessageRequest.getMessageSendChannelList();
        AssertUtil.isTrue(StringUtil.isNotEmpty(employeeCode) ,new ApiException(400,"工号不能为空"));
        AssertUtil.isTrue(StringUtil.isNotEmpty(messageContentType) ,new ApiException(400,"业务类型不能为空"));
        //定时发送时间
        LocalDateTime publishTime = aggrSendMessageRequest.getPublishTime();
        AgentDTO agentDTO = umApiWrapper.getAgentsByEmployee(employeeCode);
        if (agentDTO == null){
            log.info("聚合消息发送,aggrSendMessageRequest,无效代理人,employeeCode:{}",employeeCode);
            return "无效代理人";
        }
        Long agentId = agentDTO.getAgentId();
        AssertUtil.isTrue(agentId != null ,new ApiException(400,"代理人不存在agentId"));
        String employeeName = agentDTO.getAgentName();

        //站内消息模板
        MessageObjConfig localConfig = messageConfig.getMessage().get(messageContentType);
        //公众号消息模板
        MessageTemplateConfig wxConfig = messageTemplateConfig.getMessage().get(messageContentType);
        //PUSH消息模板
        AppObj pushConfig = appMessageConfig.getMessage().get(messageContentType);
        int contentIndex = aggrSendMessageRequest.getContentIndex() == null ? 1 : aggrSendMessageRequest.getContentIndex();
        int urlIndex = aggrSendMessageRequest.getUrlIndex() == null ? 1 : aggrSendMessageRequest.getUrlIndex();
        if (messageSendChannelList.contains(MessageSendChannel.LOCAL)){
            String title = localConfig.getTitle();
            String content = localConfig.getContent();
            String content2 = localConfig.getContent2();
            String url = localConfig.getUrl();
            String url2 = localConfig.getUrl2();
            String urlName = localConfig.getUrlName();
            Message message = Message.builder()
                    .title(paramConvert(title,aggrSendMessageRequest))
                    .content(paramConvert(contentIndex == 1 ? content : content2,aggrSendMessageRequest))
                    .urlName(urlName)
                    .userId(agentId)
                    .visitorId(null)
                    .source(messageContentType)
                    .businessId(null)
                    .publishTime(LocalDateTime.now())
                    .deleted(Boolean.FALSE)
                    .viewed(Boolean.FALSE)
                    .batched(Boolean.FALSE)
                    .batchId(0L)
                    .url(StringUtil.isEmpty(aggrSendMessageRequest.getUrl()) ?
                            paramConvert(urlIndex == 1 ? url : url2,aggrSendMessageRequest) : aggrSendMessageRequest.getUrl())
                    .type("")
                    .build();
            if (publishTime == null){
                messageMapper.insert(message);
            }else {
                //定时发送
                message.setPublishTime(publishTime);
                MessageSendTaskAddRequest messageSendTaskAddRequest = new MessageSendTaskAddRequest();
                messageSendTaskAddRequest.setEmployeeCode(employeeCode);
                messageSendTaskAddRequest.setEmployeeName(employeeName);
                messageSendTaskAddRequest.setContent(JsonUtil.toJSON(message));
                messageSendTaskAddRequest.setMessageContentType(MessageContentType.get(messageContentType));
                messageSendTaskAddRequest.setPublishTime(publishTime);
                messageSendTaskAddRequest.setSourceType(MessageSendChannel.LOCAL);
                addTaskMsg(messageSendTaskAddRequest);
            }
            log.info("聚合消息发送,站内消息,message:{}",JsonUtil.toJSON(message));
        }
        if (messageSendChannelList.contains(MessageSendChannel.WX)){
            String templateId = "UAT".equals(environment) ? wxConfig.getUTemplateId() : wxConfig.getPTemplateId();
            String url = wxConfig.getUrl();
            String url2 = wxConfig.getUrl2();
            List<WxMessageRequest> wxMessageRequest = Collections.singletonList(WxMessageRequest.builder()
                    .toUser(employeeCode)
                    .url(
                            MessageContentType.INSURE_PLAN_ANEW.name().equals(aggrSendMessageRequest.getMessageContentType())?
                            aggrSendMessageRequest.getUrl() :
                                    environmentUrl + (StringUtil.isEmpty(aggrSendMessageRequest.getUrl()) ?
                            paramConvert(urlIndex == 1 ? url : url2,aggrSendMessageRequest) : aggrSendMessageRequest.getUrl())
                    )
                    .data(sendWxMessage("",
                            paramConvert(wxConfig.getKey1(),aggrSendMessageRequest),
                            paramConvert(wxConfig.getKey2(),aggrSendMessageRequest),
                            paramConvert(wxConfig.getKey3(),aggrSendMessageRequest),
                            paramConvert(wxConfig.getKey4(),aggrSendMessageRequest),
                            paramConvert(wxConfig.getKey5(),aggrSendMessageRequest),
                            ""))
                    .templateId(templateId)
                    .build());
            if (publishTime == null){
                wxMessage(wxMessageRequest);
            }else {
                //定时发送
                MessageSendTaskAddRequest messageSendTaskAddRequest = new MessageSendTaskAddRequest();
                messageSendTaskAddRequest.setEmployeeCode(employeeCode);
                messageSendTaskAddRequest.setEmployeeName(employeeName);
                messageSendTaskAddRequest.setContent(JsonUtil.toJSON(wxMessageRequest));
                messageSendTaskAddRequest.setMessageContentType(MessageContentType.get(messageContentType));
                messageSendTaskAddRequest.setPublishTime(publishTime);
                messageSendTaskAddRequest.setSourceType(MessageSendChannel.WX);
                addTaskMsg(messageSendTaskAddRequest);
            }
        }
        if (messageSendChannelList.contains(MessageSendChannel.PUSH)){
            String title = pushConfig.getPushTitle();
            String alert = pushConfig.getPushAlert();
            String alert2 = pushConfig.getPushAlert2();
            String url = pushConfig.getUrl();
            String url2 = pushConfig.getUrl2();
            PushMessageVO pushReport = new PushMessageVO();
            pushReport.setPushTitle(paramConvert(title,aggrSendMessageRequest));
            pushReport.setPushAlert(paramConvert(contentIndex == 1 ? alert : alert2,aggrSendMessageRequest));
            pushReport.setNewView(pushConfig.getNewView());
            pushReport.setUrlData(StringUtil.isEmpty(aggrSendMessageRequest.getUrl()) ?
                    paramConvert(urlIndex == 1 ? url : url2,aggrSendMessageRequest) : aggrSendMessageRequest.getUrl());
            pushReport.setEmployeeCode(employeeCode);
            if (publishTime == null){
                jPushService.pushMessage(pushReport);
            }else {
                //定时发送
                MessageSendTaskAddRequest messageSendTaskAddRequest = new MessageSendTaskAddRequest();
                messageSendTaskAddRequest.setEmployeeCode(employeeCode);
                messageSendTaskAddRequest.setEmployeeName(employeeName);
                messageSendTaskAddRequest.setContent(JsonUtil.toJSON(pushReport));
                messageSendTaskAddRequest.setMessageContentType(MessageContentType.get(messageContentType));
                messageSendTaskAddRequest.setPublishTime(publishTime);
                messageSendTaskAddRequest.setSourceType(MessageSendChannel.PUSH);
                addTaskMsg(messageSendTaskAddRequest);
            }
        }
        return "SUCCESS";
    }

    private String paramConvert(String content,AggrSendMessageRequest request) {
        if (StringUtil.isEmpty(content)){
            return "";
        }
        String key1 = request.getKey1();
        String key2 = request.getKey2();
        String key3 = request.getKey3();
        String key4 = request.getKey4();
        String key5 = request.getKey5();
        String key6 = request.getKey6();
        String key7 = request.getKey7();
        String key8 = request.getKey8();
        String key9 = request.getKey9();

        if (content.contains("<key1>"))
            content = content.replace("<key1>", key1 != null ? key1 : "");
        if (content.contains("<key2>"))
            content = content.replace("<key2>", key2 != null ? key2 : "");
        if (content.contains("<key3>"))
            content = content.replace("<key3>", key3 != null ? key3 : "");
        if (content.contains("<key4>"))
            content = content.replace("<key4>", key4 != null ? key4 : "");
        if (content.contains("<key5>"))
            content = content.replace("<key5>", key5 != null ? key5 : "");
        if (content.contains("<key6>"))
            content = content.replace("<key6>", key6 != null ? key6 : "");
        if (content.contains("<key7>"))
            content = content.replace("<key7>", key7 != null ? key7 : "");
        if (content.contains("<key8>"))
            content = content.replace("<key8>", key8 != null ? key8 : "");
        if (content.contains("<key9>"))
            content = content.replace("<key9>", key9 != null ? key9 : "");
        if (content.contains("<datatime>"))
            content = content.replace("<datatime>", LocalDateTime.now().format(DateTimeFormatter.ofPattern(DatePatterns.YYYY_MM_DD_HH_MM)));
        return content;
    }

    @Override
    public void orphanMessage(OrphanMessageRequest request) {
        log.info("孤儿单消息发送,入参request:{}",JsonUtil.toJSON(request));
        if (CollectionUtils.isEmpty(request.getPolicyNoList())){
            log.info("孤儿单消息发送,入参保单列表为空");
            return;
        }
        EsQueryResponseVo esQueryResponseVo = policyWrapper.queryPolicyList(PolicyEsQueryRequestVo.builder()
                .contNoList(request.getPolicyNoList()).build());
        if (esQueryResponseVo == null || CollectionUtils.isEmpty(esQueryResponseVo.getDataList())){
            log.info("孤儿单消息发送,ES未查到保单:{}",JsonUtil.toJSON(request.getPolicyNoList()));
            return;
        }
        Map<String, List<PolicyEsVo>> map = esQueryResponseVo.getDataList().stream().collect(Collectors.groupingBy(x -> x.getAppntEsVo().getCustomerNo()));
        //投保人去重数量
        int size = map.size();
        //投保人姓名
        String customerNo = map.keySet().iterator().next();
        String name = map.get(customerNo).get(0).getAppntEsVo().getName();
        //保单号
        String policyNo = request.getPolicyNoList().get(0);

        AggrSendMessageRequest aggrSendMessageRequest = new AggrSendMessageRequest();
        aggrSendMessageRequest.setEmployeeCode(request.getEmployeeCode());
        aggrSendMessageRequest.setMessageContentType(MessageContentType.ORPHAN_ADDING_NOTICES.name());
        List<MessageSendChannel> messageSendChannelList = new ArrayList<>();
        messageSendChannelList.add(MessageSendChannel.WX);
        messageSendChannelList.add(MessageSendChannel.LOCAL);
        messageSendChannelList.add(MessageSendChannel.PUSH);
        aggrSendMessageRequest.setMessageSendChannelList(messageSendChannelList);
        aggrSendMessageRequest.setKey1(String.valueOf(request.getPolicyNoList().size()));
        aggrSendMessageRequest.setKey2(name);
        aggrSendMessageRequest.setKey3(String.valueOf(size));
        aggrSendMessageRequest.setKey4(policyNo);
        aggrSendMsg(aggrSendMessageRequest);
    }

    @Override
    public Map<MessageType,AggMessageVO> aggMessages(){
        Map<MessageType,AggMessageVO> aggMessageVOMap = new HashMap<>();
        String employeeCode = RequestContextHolder.getEmployeeCode();
        Long agentId = RequestContextHolder.getAgentId();
        long startTime = System.currentTimeMillis();
        //先查询reds获取当前人已读的ID 查询机构表查找未读的消息--------------------
        Set<String> yiDuIds = readCache.get(employeeCode);
        String row = AgentOrgType.CHANNEL.name().equals(RequestContextHolder.getOrgType())?"channels":"partners";
        EmployeeVO employeeInfo = employeeApi.getEmployeeInfo(AgentOrgType.valueOf(RequestContextHolder.getOrgType()), employeeCode);
        long queryEndTime_1 = System.currentTimeMillis();
//        log.info("aggMessages耗时,_1:{}",queryEndTime_1 - startTime);
        //先查询所有人可读的消息，如果没有，在查询权限内的消息
        MessageSystem messageSystem = messageSystemMapper.selectOne(new LambdaQueryWrapper<MessageSystem>()
                .eq(MessageSystem::getDeleted, Boolean.FALSE)
                .le(MessageSystem::getSendTime, LocalDateTime.now())
                .notIn(CollectionUtils.isNotEmpty(yiDuIds), MessageSystem::getId, yiDuIds)
                .eq(MessageSystem::getMessageStatus,MessageType.SYSTEM)
                .last(" and ((FIND_IN_SET('" + employeeCode + "', " + row + ") > 0 " +
                        "or FIND_IN_SET('"+employeeInfo.getOrgCode()+"', " + row + ")>0)or is_all = true) "+
                        "order by send_time desc limit 1 "));
        long queryEndTime_2 = System.currentTimeMillis();
//        log.info("aggMessages耗时,_2:{}",queryEndTime_2 - queryEndTime_1);
        if (messageSystem != null){
            //系统消息
            AggMessageVO aggMessageVO = new AggMessageVO();
            aggMessageVO.setMessageType(MessageType.SERVICE);
            aggMessageVO.setMessageId(messageSystem.getId());
            aggMessageVO.setMessageValue(messageSystem.getMessageBody());
            aggMessageVOMap.put(MessageType.SYSTEM,aggMessageVO);
        }
        List<Message> messageList = messageMapper.aggMessages(agentId);
        long queryEndTime_3 = System.currentTimeMillis();
//        log.info("aggMessages耗时,_3:{}",queryEndTime_3 - queryEndTime_2);
        if (CollectionUtils.isNotEmpty(messageList)){
            for (Message message : messageList){
                String source = message.getSource();
                if (MessageContentType.get(source) == null){
                    log.error("消息聚合查询aggMessages,无效source,source:{}",source);
                    continue;
                }
                MessageType messageType = MessageType.get(reverseElection(MessageContentType.get(source)));
                if (messageType == null){
                    log.error("消息聚合查询aggMessages,MessageContentType未归类,source:{}",source);
                    continue;
                }
                if (aggMessageVOMap.get(messageType) == null){
                    AggMessageVO aggMessageVO = new AggMessageVO();
                    aggMessageVO.setMessageType(messageType);
                    aggMessageVO.setMessageId(message.getId());
                    aggMessageVO.setMessageValue(message.getContent());
                    aggMessageVOMap.put(messageType,aggMessageVO);
                }
            }
        }
        long queryEndTime_4 = System.currentTimeMillis();
//        log.info("aggMessages耗时,_4:{}",queryEndTime_4 - queryEndTime_3);
        if (aggMessageVOMap.isEmpty()){
            Message message = messageMapper.selectOne(new LambdaQueryWrapper<Message>()
                    .eq(Message::getDeleted, Boolean.FALSE)
                    .eq(Message::getUserId, agentId)
                    .le(Message::getPublishTime,LocalDateTime.now())
                    .orderByDesc(Message::getPublishTime)
                    .last("limit 1"));
            long queryEndTime_5 = System.currentTimeMillis();
//            log.info("aggMessages耗时,_5:{}",queryEndTime_5 - queryEndTime_4);
            if (message != null){
                String source = message.getSource();
                MessageType messageType = MessageType.get(reverseElection(MessageContentType.get(source)));
                if (messageType != null){
                    AggMessageVO aggMessageVO = new AggMessageVO();
                    aggMessageVO.setMessageType(messageType);
                    aggMessageVO.setMessageId(message.getId());
                    aggMessageVO.setMessageValue(message.getContent());
                    aggMessageVOMap.put(messageType,aggMessageVO);
                }
            }
            messageSystem = messageSystemMapper.selectOne(new LambdaQueryWrapper<MessageSystem>()
                    .eq(MessageSystem::getDeleted, Boolean.FALSE)
                    .le(MessageSystem::getSendTime, LocalDateTime.now())
                    .last(" and ((FIND_IN_SET('" + employeeCode + "', " + row + ") > 0 " +
                            "or FIND_IN_SET('"+employeeInfo.getOrgCode()+"', " + row + ")>0) or is_all = true) "+
                            "order by send_time desc limit 1 "));
            long queryEndTime_6 = System.currentTimeMillis();
//            log.info("aggMessages耗时,_6:{}",queryEndTime_6 - queryEndTime_5);
            if (messageSystem != null){
                AggMessageVO aggMessageVO = new AggMessageVO();
                aggMessageVO.setMessageType(MessageType.get(messageSystem.getMessageStatus()));
                aggMessageVO.setMessageId(messageSystem.getId());
                aggMessageVO.setMessageValue(messageSystem.getMessageBody());
                aggMessageVOMap.put(MessageType.get(messageSystem.getMessageStatus()),aggMessageVO);
            }
        }
        long endTime = System.currentTimeMillis();
        log.info("aggMessages耗时,_总耗时:{}",endTime - startTime);
        return aggMessageVOMap;
    }

    @Override
    public List<String> qyeryMessageTemp(String tempKey){
        //站内消息模板
        MessageObjConfig localConfig = messageConfig.getMessage().get(tempKey);
        //公众号消息模板
        MessageTemplateConfig wxConfig = messageTemplateConfig.getMessage().get(tempKey);
        //PUSH消息模板
        AppObj pushConfig = appMessageConfig.getMessage().get(tempKey);
        List<String> resList = new ArrayList<>();
        if (localConfig != null){
            resList.add(JsonUtil.toJSON(localConfig));
        }
        if (wxConfig != null){
            resList.add(JsonUtil.toJSON(wxConfig));
        }
        if (pushConfig != null){
            resList.add(JsonUtil.toJSON(pushConfig));
        }
        return resList;
    }

    @Override
    public void recruitMessage(RecruitMessageRequest request) {
        log.info("销管招募状态请求request：{}",JsonUtil.toJSON(request));
        String messageContentType = request.getMessageContentType();
        List<String> contentTypeList = Arrays.asList("SECOND_INTERVIEW_PASSED", "SECOND_INTERVIEW_REJECTED", "INITIAL_REVIEW_REJECTED", "FINAL_REVIEW_PASSED", "FINAL_REVIEW_REJECTED");
        if (contentTypeList.contains(messageContentType)){
            AggrSendMessageRequest aggrSendMessageRequest = BeanCopier.copyObject(request, AggrSendMessageRequest.class);
            aggrSendMessageRequest.setMessageSendChannelList(Arrays.asList(
                    MessageSendChannel.WX,
                    MessageSendChannel.LOCAL,
                    MessageSendChannel.PUSH
            ));
            aggrSendMsg(aggrSendMessageRequest);
        }
    }

    @Override
    public String aggrSendMsgXg(AggrSendMessageRequest aggrSendMessageRequest) {
        //增加缓存
        if (actEmpXgFlag.GET(aggrSendMessageRequest.getEmployeeCode()).isSuccess()) {
            log.info("缓存中有值");
            List<PopUpWindowVO> popUpWindowVOS = actEmpXgFlag.get(aggrSendMessageRequest.getEmployeeCode());
            String s = aggrSendMessageRequest.getKey4().split(",")[0];
            //如果缓存的值中有s,则不需要插入缓存
            if (popUpWindowVOS.stream().anyMatch(x -> x.getId().equals(s))) {
                log.info("缓存中有值但值重复，不需要添加缓存");
                aggrSendMessageRequest.setKey4(null);
                return null;
            }else{
                //如果没有，则插入缓存
                List<PopUpWindowVO> popUpWindowVOS1 = actEmpXgFlag.get(aggrSendMessageRequest.getEmployeeCode());
                List<PopUpWindowVO> asb = new ArrayList<>(popUpWindowVOS1);
                asb.add(PopUpWindowVO.builder()
                        .flag(false)
                        .id(aggrSendMessageRequest.getKey4().split(",")[0])
                        .table(aggrSendMessageRequest.getKey4().split(",")[1])
                        .subTable(aggrSendMessageRequest.getKey4().split(",")[2])
                        .build());
                log.info("缓存中有对应KEY但无相同的值，需要添加缓存;{}",JsonUtil.toJSON(asb));
                actEmpXgFlag.remove(aggrSendMessageRequest.getEmployeeCode());
                actEmpXgFlag.put(aggrSendMessageRequest.getEmployeeCode(),asb);
            }
        } else {
            actEmpXgFlag.put(aggrSendMessageRequest.getEmployeeCode(), Collections.singletonList(PopUpWindowVO.builder()
                    .flag(false)
                    .id(aggrSendMessageRequest.getKey4().split(",")[0])
                    .table(aggrSendMessageRequest.getKey4().split(",")[1])
                    .subTable(aggrSendMessageRequest.getKey4().split(",")[2])
                    .build()));
        }
        //查询ORG数据获取代理人数据
        EmployeeOrgVO employeeOrgVO = employeeOrgApi.queryEmployeeOrgInfo(aggrSendMessageRequest.getEmployeeCode());
        if (null == employeeOrgVO) {
            log.info("org.queryEmployeeOrgInfo 返回属性为空");
            return null;
        }
        //判断当前人员
        if (!aggrSendMessageRequest.getEmployeeCode().equals(employeeOrgVO.getBusinessTeamLeaderCode())
                && !aggrSendMessageRequest.getEmployeeCode().equals(employeeOrgVO.getBusinessAreaLeaderCode())
                && !aggrSendMessageRequest.getEmployeeCode().equals(employeeOrgVO.getBusinessDeptLeaderCode())) {
            sendUser(aggrSendMessageRequest, employeeOrgVO,"给领导发");
        }else{
            sendUser(aggrSendMessageRequest, employeeOrgVO,"不给领导发");
        }
        return null;
    }

    private void sendUser(AggrSendMessageRequest aggrSendMessageRequest, EmployeeOrgVO employeeOrgVO,String geiBuGei) {
        //查询督导
        List<SupervisorEmployeeVO> supervisorEmployeeVOS = supervisorEmployeeApi.selectSupervisorEmployeeListPost(SelectSupervisorEmployeeListPostRequest.builder()
                .teamCode(employeeOrgVO.getBusinessAreaCode())
                .build());

        String s = aggrSendMessageRequest.getKey4().split(",")[1];
        String urlPrefix;
        switch (s) {
            case "琴海吉尼斯":
                urlPrefix = "qinHai";
                break;
            case "琴星精英会":
                urlPrefix = "qinXing";
                break;
            case "琴韵博识轩":
                urlPrefix = "qinYun";
                break;
            case "琴辉荣耀堂":
                urlPrefix = "qinHui";
                break;
            case "MDRT百万圆桌年会":
                urlPrefix = "bwyz";
                break;
            default:
                urlPrefix = "qinHai";
                break;
        }

        aggrSendMessageRequest.setUrl("/honor?honorCode=" + urlPrefix + "&empCode=" + aggrSendMessageRequest.getEmployeeCode());
        aggrSendMessageRequest.setKey2("本人");
        aggrSendMessageRequest.setKey5("您");
        aggrSendMessageRequest.setKey6("");
        aggrSendMessageRequest.setKey7("快去查看吧！");
        aggrSendMsg(aggrSendMessageRequest);


      if ("给领导发".equals(geiBuGei)){
          aggrSendMessageRequest.setEmployeeCode(employeeOrgVO.getBusinessTeamLeaderCode());
          aggrSendMessageRequest.setUrl("/merit-detail?honorId=" + aggrSendMessageRequest.getKey4().split(",")[0]);
          aggrSendMessageRequest.setKey2("伙伴");
          aggrSendMessageRequest.setKey5("伙伴");
          aggrSendMessageRequest.setKey6(employeeOrgVO.getEmployeeName());
          aggrSendMessageRequest.setKey7("快去祝贺TA吧！");
          aggrSendMessageRequest.setKey6(employeeOrgVO.getEmployeeName());
          aggrSendMsg(aggrSendMessageRequest);
      }

        supervisorEmployeeVOS.forEach(info -> {
            aggrSendMessageRequest.setEmployeeCode(info.getEmployeeCode());
            aggrSendMessageRequest.setUrl("/merit-detail?honorId=" + aggrSendMessageRequest.getKey4().split(",")[0]);
            aggrSendMessageRequest.setKey2("伙伴");
            aggrSendMessageRequest.setKey5("伙伴");
            aggrSendMessageRequest.setKey6(employeeOrgVO.getEmployeeName());
            aggrSendMessageRequest.setKey7("快去祝贺TA吧！");
            aggrSendMsg(aggrSendMessageRequest);
        });
    }

    @Override
    public List<PopUpWindowVO> popUpWindow(String employeeCode) {
        if (actEmpXgFlag.GET(employeeCode).isSuccess()){
            List<PopUpWindowVO> popUpWindowVOS = actEmpXgFlag.get(employeeCode);
            //删除缓存
            actEmpXgFlag.remove(employeeCode);
            return popUpWindowVOS;
        }
        return null;
    }

}
