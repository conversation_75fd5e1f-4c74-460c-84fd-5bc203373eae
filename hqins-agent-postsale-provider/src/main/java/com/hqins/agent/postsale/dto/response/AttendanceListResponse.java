package com.hqins.agent.postsale.dto.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AttendanceListResponse implements Serializable {
    @ApiModelProperty("上午规定打卡时间")
    private String  morningStipulateCheckInTime;

    @ApiModelProperty("下午规定打卡时间")
    private String  afterStipulateCheckInTime;

    @ApiModelProperty("签到 签退，最小时间间隔")
    private Integer timeMinInterval;

    @ApiModelProperty("配置分割新老时间")
    private Date newTime;

    private List<ResponseList> responseListList;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ResponseList implements Serializable{
        @ApiModelProperty("签到ID")
        private Long id;

        @ApiModelProperty("上/下午")
        private String period;

        @ApiModelProperty("打卡地点名称")
        private String locationName;

        @ApiModelProperty("打卡地点")
        private String locationCode;

        @ApiModelProperty("签到时间")
        private LocalDateTime checkInTime;

        @ApiModelProperty("签退时间")
        private LocalDateTime checkOutTime;

        @ApiModelProperty(" 打卡地点类型(COMPANY(\"公司\"), MERCHANT(\"渠道商\"))")
        private String addressType;

        @ApiModelProperty("经度")
        private String longitude;

        @ApiModelProperty("纬度")
        private String latitude;

        @ApiModelProperty("目标经度")
        private String targetLongitude;

        @ApiModelProperty("目标纬度")
        private String targetLatitude;

        @ApiModelProperty("签到状态")
        private String auditType;

        @ApiModelProperty("抽查状态状态")
        private String spotCheckType;

        @ApiModelProperty("二级机构")
        private String companyInsCode;

        @ApiModelProperty("网点认证")
        private Boolean authentication;
    }

}
