package com.hqins.agent.postsale.service.impl;

import com.alibaba.fastjson.JSON;
import com.alicp.jetcache.Cache;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.CreateCache;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hqins.agent.postsale.dao.entity.HolidayData;
import com.hqins.agent.postsale.dao.mapper.HolidayDataMapper;
import com.hqins.agent.postsale.dto.response.HolidayResponse;
import com.hqins.agent.postsale.service.UtilsService;
import com.hqins.agent.postsale.utils.DateUtils;
import com.hqins.common.utils.JsonUtil;
import com.hqins.common.utils.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class UtilsServiceImpl implements UtilsService {
    @Resource
    private HolidayDataMapper holidayDataMapper;

    @CreateCache(name = "WorkDays:", expire = 10, timeUnit = TimeUnit.MINUTES, cacheType = CacheType.LOCAL)
    Cache<String, String> workDaysCache;
    /**
     * 获取工作日，除国家法定节假日
     */
    @Override
    public String workDays(String year,String month){
        String cacheYearMonthKey = year + "_" + month;
        String workDaysCacheStr = workDaysCache.get(cacheYearMonthKey);
        List<HolidayData> holidayDataList;
        if (StringUtil.isEmpty(workDaysCacheStr)){
            holidayDataList = holidayDataMapper.selectList(new LambdaQueryWrapper<HolidayData>()
                    .eq(HolidayData::getHolidayYear, year)
                    .eq(HolidayData::getHolidayMonth, month));
            workDaysCache.put(cacheYearMonthKey, JsonUtil.toJSON(holidayDataList));
        }else{
            holidayDataList = JSON.parseArray(workDaysCacheStr, HolidayData.class);
        }
        // 工作MAP
        Map<String, String> transferWorkdayMap = holidayDataList.stream()
                .filter(holidayData -> "transfer_workday".equals(holidayData.getHolidayType()))
                .collect(Collectors.toMap(HolidayData::getHolidayDay, HolidayData::getHolidayType));

        //假期MAP
        Map<String, String> publicHolidayMap = holidayDataList.stream()
                .filter(holidayData -> "public_holiday".equals(holidayData.getHolidayType()))
                .collect(Collectors.toMap(HolidayData::getHolidayDay, HolidayData::getHolidayType));

        List<Integer> workdayList = DateUtils.getWorkdays(Integer.parseInt(year),  Integer.parseInt(month));
        Map<Integer, Integer> workdayMap = workdayList.stream().collect(Collectors.toMap(Integer::valueOf, Integer::valueOf));
        //遍历workdayMap
        for (Integer day : workdayList){
            if (publicHolidayMap.containsKey(String.valueOf(day))){
                workdayMap.remove(day);
            }
        }
        //transferWorkdayMap遍历
        for (Map.Entry<String, String> entry : transferWorkdayMap.entrySet()){
            workdayMap.put(Integer.parseInt(entry.getKey()),1);
        }
        //workdayMap key转list，并升序排序
        List<Integer> resList = workdayMap.keySet().stream().sorted().collect(Collectors.toList());
        return JsonUtil.toJSON(resList);
    }

    /**
     * 获取年度工作日，除国家法定节假日
     */
    @Override
    public String yearWorkDays(String year){
        String workDaysCacheStr = workDaysCache.get(year);
        List<HolidayData> holidayDataList;
        if (StringUtil.isEmpty(workDaysCacheStr)){
            holidayDataList = holidayDataMapper.selectList(new LambdaQueryWrapper<HolidayData>()
                    .eq(HolidayData::getHolidayYear, year));
            workDaysCache.put(year, JsonUtil.toJSON(holidayDataList));
        }else{
            holidayDataList = JSON.parseArray(workDaysCacheStr, HolidayData.class);
        }
        // 工作MAP
        Map<String, String> transferWorkdayMap = holidayDataList.stream()
                .filter(holidayData -> "transfer_workday".equals(holidayData.getHolidayType()))
                .collect(Collectors.toMap(
                        holidayData -> (holidayData.getHolidayMonth() + "_" + holidayData.getHolidayDay()),
                        HolidayData::getHolidayType,
                        (existing, replacement) -> existing
                ));
        //假期MAP
        Map<String, String> publicHolidayMap = holidayDataList.stream()
                .filter(holidayData -> "public_holiday".equals(holidayData.getHolidayType()))
                .collect(Collectors.toMap(
                        holidayData -> (holidayData.getHolidayMonth() + "_" + holidayData.getHolidayDay()),
                        HolidayData::getHolidayType,
                        (existing, replacement) -> existing // 如果有重复 key，保留第一个出现的
                ));
        Map<String,List<Integer>> resMap = new HashMap<>();
        for (int month = 1;month <= 12; month++){
            List<Integer> workdayList = DateUtils.getWorkdays(Integer.parseInt(year),  month);
            Map<Integer, Integer> workdayMap = workdayList.stream().collect(Collectors.toMap(Integer::valueOf, Integer::valueOf));
            //遍历workdayMap
            for (Integer day : workdayList){
                if (publicHolidayMap.containsKey(month + "_" + day)){
                    workdayMap.remove(day);
                }
            }
            //工作MAP遍历
            for (Map.Entry<String, String> entry : transferWorkdayMap.entrySet()){
                String monthStr = entry.getKey().split("_")[0];
                if (monthStr.equals(String.valueOf(month))){
                    String day = entry.getKey().split("_")[1];
                    workdayMap.put(Integer.parseInt(day),1);
                }
            }
            //假期MAP遍历
            List<Integer> resList = workdayMap.keySet().stream().sorted().collect(Collectors.toList());
            resMap.put(String.valueOf(month),resList);
        }
        return JsonUtil.toJSON(resMap);
    }

    /**
     * 同步国家法定假日
     */
    @Override
    public void syncHolidayCalendar(String year){
        log.info("开始同步国家法定假日,syncHolidayCalendar");
        String url = "https://unpkg.com/holiday-calendar@1.1.6/data/CN/" + year + ".json";
        LocalDateTime nowTime = LocalDateTime.now();
        RestTemplate restTemplate = new RestTemplate();
        HolidayResponse response = restTemplate.getForObject(url, HolidayResponse.class);
        log.info("获取国家法定假日数据,syncHolidayCalendar,response:{}",JsonUtil.toJSON(response));
        if (response != null) {
            List<HolidayData> holidayDataList = response.getDates().stream().map(dateInfo -> {
                String[] parts = dateInfo.getDate().split("-");
                return HolidayData.builder()
                    .holidayYear(parts[0])
                    .holidayMonth(String.valueOf(Integer.parseInt(parts[1])))
                    .holidayDay(String.valueOf(Integer.parseInt(parts[2])))
                    .holidayName(dateInfo.getName_cn())
                    .holidayType(dateInfo.getType())
                    .createTime(nowTime)
                    .build();
            }).collect(Collectors.toList());
            log.info("获取国家法定假日数据,syncHolidayCalendar,holidayDataList:{}",JsonUtil.toJSON(holidayDataList));
            // 批量插入数据库
            if (!holidayDataList.isEmpty()) {
                // 清除旧数据
                holidayDataMapper.delete(new LambdaQueryWrapper<HolidayData>().eq(HolidayData::getHolidayYear, year));
                // 插入新数据
                holidayDataMapper.insertBatchSomeColumn(holidayDataList);
            }
        }
        log.info("结束同步国家法定假日,syncHolidayCalendar");
    }

}
