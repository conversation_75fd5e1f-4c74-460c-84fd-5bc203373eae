package com.hqins.agent.postsale.dto.request;

import com.hqins.agent.postsale.dto.enums.SignInType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("补签对象")
public class ReYinBaoSignRequest implements Serializable {
    @ApiModelProperty("ID")
    private Long id;

    @ApiModelProperty("补签原因")
    private String auditRemark;

    @ApiModelProperty("签到时间")
    private LocalDateTime signInTime;

    @ApiModelProperty("签退时间")
    private LocalDateTime signOutTime;

    @ApiModelProperty("打卡图片")
    private List<String> imageUrls;

    @ApiModelProperty("指定/非指定")
    private String appointType;

    @ApiModelProperty("打卡地点名称")
    private String locationName;

    @ApiModelProperty("打卡地点code")
    private String locationCode;

    @ApiModelProperty("打卡位置(经度)")
    private String  longitude;

    @ApiModelProperty("打卡位置(纬度)")
    private String  latitude;

    @ApiModelProperty("补签类型")
    private SignInType signInType;

    @ApiModelProperty("补签上午/下午")
    private String timeType;

    @ApiModelProperty("leader？")
    private Boolean leader;


}
