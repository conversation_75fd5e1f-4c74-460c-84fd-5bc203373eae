package com.hqins.agent.postsale.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Map;

@Configuration
@RefreshScope
@Data
@ConfigurationProperties(prefix = "attendance")
@Component
public class AttendanceConfig {
    private Map<String, AttendanceObjConfig> advertisement;
    //银保补签
    private int bg;

    //团险 个险 补签
    private int gi;

    /**
     * 新银保起始日期
     */
    private Date yinBaoStartTime;
}
