package com.hqins.agent.postsale.dto.response;

import com.hqins.agent.postsale.model.vo.LeaveApplicationsVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("数据查询对象")
public class DataStatisticsResponse implements Serializable {
    @ApiModelProperty("是否为银保")
    private Boolean isBank;

    @ApiModelProperty("当月打卡网点数量")
    private Integer monthPunchInCount = 0;

    @ApiModelProperty("是否为主管")
    private Boolean isLeader;

    @ApiModelProperty("正常考勤天数")
    private double normalAttendance = 0;

    @ApiModelProperty("异常打卡次数")
    private double unusualAttendance = 0;

    @ApiModelProperty("请假天数")
    private double leaveAttendance = 0;

    @ApiModelProperty("是否有补签资格")
    private Boolean isBk;

    @ApiModelProperty("剩余补卡次数")
    private Integer residueCount;

    @ApiModelProperty("成功打卡天数")
    private double successDaKaDays = 0;

    @ApiModelProperty("工作日")
    private String ybMonthJson;

    @ApiModelProperty("应出勤天数")
    private String needAttendanceDays;

    @ApiModelProperty("签到 签退，最小时间间隔")
    private Integer timeMinInterval;

    @ApiModelProperty("当月打卡数据 KEY=日 VALUE=签到数据")
    private Map<Integer, List<MonthPunchIn>> monthPunchIns;

    @ApiModelProperty("当月非银保请假数据 KEY=日 VALUE=请假数据")
    private Map<Integer, LeaveApplicationsVO> monthLeaveIns;

    @ApiModelProperty("当月银保请假数据 KEY=日 VALUE=请假数据")
    private Map<Integer, List<LeaveApplicationsVO>> monthBankLeaveIns;

    @ApiModelProperty("当月打卡配置 KEY=日 VALUE=打卡配置")
    private Map<Integer, DataStatisticsTimerVO> dataStatisticsTimerMap;

    @ApiModelProperty("配置分割新老时间")
    private Date newTime;

}
