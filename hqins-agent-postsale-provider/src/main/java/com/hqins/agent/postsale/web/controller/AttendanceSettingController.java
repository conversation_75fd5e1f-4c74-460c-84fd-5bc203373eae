package com.hqins.agent.postsale.web.controller;

import cn.hutool.json.JSONUtil;
import com.hqins.agent.postsale.dao.entity.AgentAttendanceTime;
import com.hqins.agent.postsale.dao.entity.AttendanceLeaveCheck;
import com.hqins.agent.postsale.dao.entity.LeaveCheckConfig;
import com.hqins.agent.postsale.dto.enums.LeaveApplyType;
import com.hqins.agent.postsale.dto.request.ApplyOARequestBean;
import com.hqins.agent.postsale.dto.request.KmReviewParamterFormPublicRequest;
import com.hqins.agent.postsale.dto.request.LeaveCheckConfigRequest;
import com.hqins.agent.postsale.dto.response.AllowOrNotResponse;
import com.hqins.agent.postsale.dto.response.AuditResponse;
import com.hqins.agent.postsale.dto.response.KmReviewParamterFormPublicResponse;
import com.hqins.agent.postsale.dto.response.ProcessCompletedReturnRequestBean;
import com.hqins.agent.postsale.model.enums.AddressType;
import com.hqins.agent.postsale.model.request.*;
import com.hqins.agent.postsale.model.vo.*;
import com.hqins.agent.postsale.service.AttendanceSettingService;
import com.hqins.common.base.ApiResult;
import com.hqins.common.base.constants.Strings;
import com.hqins.common.base.page.PageInfo;
import com.hqins.common.web.RequestContextHolder;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 出勤控制器
 * <AUTHOR>
 */
@Api(tags = "考勤打卡管理")
@RestController
@RequestMapping("/AttendanceSetting")
@Slf4j
public class AttendanceSettingController {
    @Autowired
    private AttendanceSettingService attendanceSettingService;

    @ApiOperation("打卡地点设置查询")
    @GetMapping("/query")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<PageInfo<AttendanceSettingVO>> query(
            @ApiParam("归属合伙人组织Code") @RequestParam(value = "companyCode", required = false) String companyCode,
            @ApiParam("打卡地点(检索条件)") @RequestParam(value = "addressType", required = false) AddressType addressType,
            @ApiParam("归属合伙人机构Code") @RequestParam(value = "companyInstCode", required = false) String companyInstCode,
            @ApiParam("归属渠道商Code") @RequestParam(value = "merchantCode", required = false) String merchantCode,
            @ApiParam("渠道商机构Code") @RequestParam(value = "merchantOrgCode", required = false) String merchantOrgCode,
            @ApiParam("是否添加到打卡网点") @RequestParam(value = "status", required = false) Boolean status,
            @ApiParam("是否已认证") @RequestParam(value = "authenticationOrNot", required = false) Boolean authenticationOrNot,
            @ApiParam(Strings.CURRENT_PAGE_MSG) @RequestParam("current") long current,
            @ApiParam(Strings.SIZE_PAGE_MSG) @RequestParam("size") long size
            ) {
        return ApiResult.ok(attendanceSettingService.queryList(companyCode,companyInstCode,addressType,merchantCode,merchantOrgCode,status,authenticationOrNot,current,size));
    }

    @ApiOperation("打卡地点设置添加")
    @PostMapping("/save")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<Boolean> save(@Valid @RequestBody AttendanceSettingRequest request) {
        return ApiResult.ok(attendanceSettingService.saveSetting(request));
    }

    @ApiOperation("打卡地点设置添加/删除/编辑")
    @PostMapping("/update")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<Boolean> update(@Valid @RequestBody AttendanceSettingRequest request) {
        return ApiResult.ok(attendanceSettingService.updateSetting(request));
    }

    @ApiOperation("打卡查询及审批")
    @GetMapping("/examineQuery")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<PageInfo<AttendanceAuditQueryVO>> examineQuery(
            @ApiParam("归属合伙人组织Code") @RequestParam(value = "companyCode", required = false) String companyCode,
            @ApiParam("打卡地点(检索条件)") @RequestParam(value = "addressType", required = false) AddressType addressType,
            @ApiParam("归属合伙人机构Code") @RequestParam(value = "companyInstCode", required = false) String companyInstCode,
            @ApiParam("归属渠道商Code") @RequestParam(value = "merchantCode", required = false) String merchantCode,
            @ApiParam("渠道商机构Code") @RequestParam(value = "merchantOrgCode", required = false) String merchantOrgCode,
            @ApiParam("销售人员姓名") @RequestParam(value = "employeeName", required = false) String employeeName,
            @ApiParam("销售人员工号") @RequestParam(value = "employeeCode", required = false) String employeeCode,
            @ApiParam("打卡日期开始时间") @RequestParam(value = "startTime", required = false) String startTime,
            @ApiParam("打卡日期结束时间") @RequestParam(value = "endTime", required = false) String endTime,
            @ApiParam("审核状态") @RequestParam(value = "auditStatus", required = false) String auditStatus,
            @ApiParam("打卡状态") @RequestParam(value = "auditType", required = false) String auditType,
            @ApiParam("打卡类型") @RequestParam(value = "signInType", required = false) String signInType,
            @ApiParam("抽查状态") @RequestParam(value = "spotCheckType", required = false) String spotCheckType,

            @ApiParam(Strings.CURRENT_PAGE_MSG) @RequestParam("current") long current,
            @ApiParam(Strings.SIZE_PAGE_MSG) @RequestParam("size") long size
    ) {
        AttendanceQueryRequest attendanceQueryRequest = AttendanceQueryRequest.builder()
                .companyCode(merchantCode)
                .addressType(addressType)
                .orgCode(companyInstCode)
                .topCode(companyCode)
                .employeeCode(employeeCode)
                .employeeName(employeeName)
                .locationCode(merchantOrgCode)
                .checkInTime(startTime)
                .checkOutTime(endTime)
                .audit(auditStatus)
                .auditType(auditType)
                .signInType(signInType)
                .spotCheckType(spotCheckType).build();
        return ApiResult.ok(attendanceSettingService.examineQuery(attendanceQueryRequest,current,size,RequestContextHolder.getAdminAppId(),RequestContextHolder.getStaffId()));
    }
    @ApiOperation("出勤数据明细查询")
    @GetMapping("/detailQuery")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<PageInfo<AttendanceDetailQueryVO>> detailQuery(
            @ApiParam("归属合伙人组织Code") @RequestParam(value = "topCode", required = false) String topCode,
            @ApiParam("归属合伙人机构Code") @RequestParam(value = "orgCode", required = false) String orgCode,
            @ApiParam("归属渠道商Code") @RequestParam(value = "merchantCode", required = false) String merchantCode,
            @ApiParam("渠道商机构Code") @RequestParam(value = "merchantOrgCode", required = false) String merchantOrgCode,
            @ApiParam("销售人员姓名") @RequestParam(value = "employeeName", required = false) String employeeName,
            @ApiParam("销售人员工号") @RequestParam(value = "employeeCode", required = false) String employeeCode,
            @ApiParam("打卡日期开始时间") @RequestParam(value = "startTime", required = false) String startTime,
            @ApiParam("打卡日期结束时间") @RequestParam(value = "endTime", required = false) String endTime,
            @ApiParam("审核状态") @RequestParam(value = "auditStatus", required = false) String auditStatus,
            @ApiParam("出勤类型") @RequestParam(value = "type", required = false) String type,
            @ApiParam(Strings.CURRENT_PAGE_MSG) @RequestParam("current") long current,
            @ApiParam(Strings.SIZE_PAGE_MSG) @RequestParam("size") long size
    ) {
        AttendanceDetailQueryRequest request = AttendanceDetailQueryRequest.builder()
                .topCode(topCode)
                .orgCode(orgCode)
                .merchantCode(merchantCode)
                .merchantOrgCode(merchantOrgCode)
                .employeeName(employeeName)
                .employeeCode(employeeCode)
                .startTime(startTime)
                .endTime(endTime)
                .audit(auditStatus)
                .type(type)
                .build();
        return ApiResult.ok(attendanceSettingService.detailQuery(request,current,size, RequestContextHolder.getAdminAppId(), RequestContextHolder.getStaffId()));
    }
    @ApiOperation("审核通过/不通过")
    @PostMapping("/audit")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<AuditResponse> audit(@Valid @RequestBody AuditRequest request) {
        return ApiResult.ok(attendanceSettingService.auditAttendance(request));
    }
    @ApiOperation("导出")
    @GetMapping("/export")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<Void>  export(
            @ApiParam("导出文件ID") @RequestParam(value = "exportId", required = false) String exportId,
            @ApiParam("导出文件开始时间") @RequestParam(value = "startTime", required = false) String startTime,
            @ApiParam("导出文件结束时间") @RequestParam(value = "endTime", required = false) String endTime,
            @ApiParam("归属合伙人组织Code") @RequestParam(value = "topCode", required = false) String topCode,
            @ApiParam("归属合伙人机构Code") @RequestParam(value = "orgCode", required = false) String orgCode,
            @ApiParam("销售人员姓名") @RequestParam(value = "employeeName", required = false) String employeeName,
            @ApiParam("销售人员工号") @RequestParam(value = "employeeCode", required = false) String employeeCode,
            @ApiParam("归属渠道商Code") @RequestParam(value = "merchantCode", required = false) String merchantCode,
            @ApiParam("渠道商机构Code") @RequestParam(value = "merchantOrgCode", required = false) String merchantOrgCode,
            @ApiParam("审核状态") @RequestParam(value = "auditStatus", required = false) String auditStatus,
            @ApiParam("出勤类型") @RequestParam(value = "type", required = false) String type
            ){
        AttendanceDetailQueryRequest request = AttendanceDetailQueryRequest.builder()
                .topCode(topCode)
                .orgCode(orgCode)
                .merchantCode(merchantCode)
                .merchantOrgCode(merchantOrgCode)
                .employeeName(employeeName)
                .employeeCode(employeeCode)
                .startTime(startTime)
                .endTime(endTime)
                .audit(auditStatus)
                .type(type)
                .build();
        attendanceSettingService.export(exportId,request, RequestContextHolder.getAdminAppId(), RequestContextHolder.getStaffId());
        return ApiResult.ok();
    }

    @ApiOperation("下载")
    @GetMapping("/download")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<Map<String, Object>>  download(@ApiParam("导出文件ID") @RequestParam(value = "exportId", required = false) String exportId,
                                                    @ApiParam("导出文件查询") @RequestParam(value = "status", required = false) Boolean status){
        return ApiResult.ok(attendanceSettingService.download(exportId,status));
    }
    @ApiOperation("打卡抽查Job")
    @GetMapping("/spotCheck")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<Void>spotCheck(){
        attendanceSettingService.spotCheck();
        return ApiResult.ok();
    }
    @ApiOperation("打卡地点更新job")
    @GetMapping("/checkAddress")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<Void>checkAddress(){
        attendanceSettingService.checkAddress();
        return ApiResult.ok();
    }

    @ApiOperation("签到时间限制查询")
    @GetMapping("/query-attendance-time")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<AgentAttendanceTime>queryAgentAttendanceTime(@ApiParam("年") @RequestParam(value = "year") String year,
                                                                  @ApiParam("归属合伙人机构") @RequestParam(value = "orgIns") String orgIns,
                                                                  @ApiParam("归属合伙人机构") @RequestParam(value = "ybFlag") Boolean ybFlag){
        return ApiResult.ok(attendanceSettingService.queryAgentAttendanceTime(year,orgIns,ybFlag));
    }

    @ApiOperation("签到时间限制新增或更新")
    @PostMapping("/add-attendance-time")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<Void>addAttendanceTime(@RequestBody AgentAttendanceTime request){
        attendanceSettingService.addAttendanceTime(request);
        return ApiResult.ok();
    }

    @ApiOperation("团队管理-查询团队出勤")
    @PostMapping("/team")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<AttendanceTeamVO>team(@RequestBody AttendanceTeamRequest request){
        return ApiResult.ok(attendanceSettingService.team(request));
    }

    @ApiOperation("团队管理查询团队出勤-为销管提供接口")
    @PostMapping("/attendance/agents")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<Map<String,AttendanceAgentVO>> attendanceAgents(@RequestBody AttendanceAgentsRequest request){
        return ApiResult.ok(attendanceSettingService.attendanceAgents(request));
    }
    @ApiOperation("量子回调接口")
    @PostMapping("/callBack")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<Void> callBack(@Valid @RequestBody ProcessCompletedReturnRequestBean request) {
        log.info("LeaveApplicationsController callBack 入参 {}  ", JSONUtil.toJsonStr(request));
        attendanceSettingService.callBack(request);
        return ApiResult.ok();
    }

    @ApiOperation("量子申请接口")
    @PostMapping("/postProcessCompleted")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<KmReviewParamterFormPublicResponse> postProcessCompleted(@RequestBody KmReviewParamterFormPublicRequest request) {
        log.info("LeaveApplicationsController postProcessCompleted request {} ",JSONUtil.toJsonStr(request));
        return ApiResult.ok(attendanceSettingService.postProcessCompleted(request));
    }

    @ApiOperation("新OA申请接口")
    @PostMapping("/postProcessCompletedNew")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<KmReviewParamterFormPublicResponse> postProcessCompletedNew(@RequestBody ApplyOARequestBean request) {
        log.info("LeaveApplicationsController postProcessCompletedNew request {} ",JSONUtil.toJsonStr(request));
        return ApiResult.ok(attendanceSettingService.postProcessCompletedNew(request));
    }


    @ApiOperation("新增能否请假和打卡规则")
    @PostMapping("/add-update-check")
    @ResponseStatus(HttpStatus.CREATED)
    public ApiResult<Void> addCheck(@Valid @RequestBody AttendanceLeaveCheck request) {
        log.info("LeaveApplicationsController addCheck request {} ",JSONUtil.toJsonStr(request));
        attendanceSettingService.addCheck(request);
        return ApiResult.ok();
    }

    @ApiOperation("查询能否请假和打卡规则")
    @GetMapping("/query-check")
    @ResponseStatus(HttpStatus.CREATED)
    public ApiResult<AttendanceLeaveCheck> queryCheck(@ApiParam("机构编码") @RequestParam(value = "orgCode")String orgCode) {
        log.info("LeaveApplicationsController queryCheck request {} ",JSONUtil.toJsonStr(orgCode));
        return ApiResult.ok(attendanceSettingService.queryCheck(orgCode));
    }

    @ApiOperation("校验能否请假和打卡")
    @PostMapping("/allowOrNot")
    @ResponseStatus(HttpStatus.CREATED)
    public ApiResult<AllowOrNotResponse> allowOrNot(String employeeCode) {
        log.info("LeaveApplicationsController allowOrNot request {} ",JSONUtil.toJsonStr(employeeCode));
        return ApiResult.ok(attendanceSettingService.allowOrNot(employeeCode));
    }

    @ApiOperation("量子申请接口111111111111111")
    @PostMapping("/postProcessCompleted1")
    @ResponseStatus(HttpStatus.CREATED)
    public ApiResult<KmReviewParamterFormPublicResponse> postProcessCompleted1(Long id , LeaveApplyType typ) {
        return ApiResult.ok(attendanceSettingService.applicationForm(id, typ));
    }

    @ApiOperation("请假限制查询")
    @GetMapping("/query-leave-config")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<List<LeaveCheckConfig>>queryLeaveConfig(@ApiParam("年") @RequestParam(value = "year") String year,
                                                             @ApiParam("归属合伙人机构") @RequestParam(value = "orgIns") String orgCode){
        return ApiResult.ok(attendanceSettingService.queryLeaveConfig(year,orgCode));
    }

    @ApiOperation("请假限制新增或更新")
    @PostMapping("/add-leave-config")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<Void>addLeaveConfig(@RequestBody List<LeaveCheckConfigRequest> request){
        attendanceSettingService.addLeaveConfig(request);
        return ApiResult.ok();
    }


    @ApiOperation("导出")
    @GetMapping("/export-attendance")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<Void>  exportAttendance(
            @ApiParam("导出文件ID") @RequestParam(value = "exportId", required = false) String exportId,
            @ApiParam("归属合伙人组织Code") @RequestParam(value = "companyCode", required = false) String companyCode,
            @ApiParam("打卡地点(检索条件)") @RequestParam(value = "addressType", required = false) AddressType addressType,
            @ApiParam("归属合伙人机构Code") @RequestParam(value = "companyInstCode", required = false) String companyInstCode,
            @ApiParam("归属渠道商Code") @RequestParam(value = "merchantCode", required = false) String merchantCode,
            @ApiParam("渠道商机构Code") @RequestParam(value = "merchantOrgCode", required = false) String merchantOrgCode,
            @ApiParam("销售人员姓名") @RequestParam(value = "employeeName", required = false) String employeeName,
            @ApiParam("销售人员工号") @RequestParam(value = "employeeCode", required = false) String employeeCode,
            @ApiParam("打卡日期开始时间") @RequestParam(value = "startTime", required = false) String startTime,
            @ApiParam("打卡日期结束时间") @RequestParam(value = "endTime", required = false) String endTime,
            @ApiParam("审核状态") @RequestParam(value = "auditStatus", required = false) String auditStatus,
            @ApiParam("打卡状态") @RequestParam(value = "auditType", required = false) String auditType,
            @ApiParam("打卡类型") @RequestParam(value = "signInType", required = false) String signInType,
            @ApiParam("抽查状态") @RequestParam(value = "spotCheckType", required = false) String spotCheckType
    ){
        AttendanceQueryRequest request = AttendanceQueryRequest.builder()
                .companyCode(merchantCode)
                .addressType(addressType)
                .orgCode(companyInstCode)
                .topCode(companyCode)
                .employeeCode(employeeCode)
                .employeeName(employeeName)
                .locationCode(merchantOrgCode)
                .checkInTime(startTime)
                .checkOutTime(endTime)
                .audit(auditStatus)
                .auditType(auditType)
                .signInType(signInType)
                .spotCheckType(spotCheckType).build();
        attendanceSettingService.exportAttendance(exportId,request, RequestContextHolder.getAdminAppId(), RequestContextHolder.getStaffId());
        return ApiResult.ok();
    }

    @ApiOperation("漏签退定时任务")
    @GetMapping("/missed/task")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<Void>addLeaveConfig(@RequestParam(value = "orgCode",required = false) String orgCode){
        attendanceSettingService.missedSignatureMark(orgCode);
        return ApiResult.ok();
    }

}
