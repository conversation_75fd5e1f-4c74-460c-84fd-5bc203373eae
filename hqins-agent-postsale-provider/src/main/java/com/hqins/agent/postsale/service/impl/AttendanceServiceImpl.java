package com.hqins.agent.postsale.service.impl;


import com.alibaba.fastjson.JSONObject;
import com.aliyun.openservices.shade.org.apache.commons.lang3.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.hqins.agent.org.model.api.*;
import com.hqins.agent.org.model.vo.ChannelOrgVO;
import com.hqins.agent.org.model.vo.EmployeeOrgVO;
import com.hqins.agent.org.model.vo.EmployeeVO;
import com.hqins.agent.org.model.vo.PartassignmanagerVO;
import com.hqins.agent.postsale.config.AttendanceConfig;
import com.hqins.agent.postsale.config.AttendanceObjConfig;
import com.hqins.agent.postsale.dao.entity.AttendanceActivity;
import com.hqins.agent.postsale.dao.entity.AttendanceCustomer;
import com.hqins.agent.postsale.dao.entity.*;
import com.hqins.agent.postsale.dao.mapper.*;
import com.hqins.agent.postsale.dto.DropDownModel;
import com.hqins.agent.postsale.dto.enums.*;
import com.hqins.agent.postsale.dto.request.*;
import com.hqins.agent.postsale.dto.response.*;
import com.hqins.agent.postsale.model.enums.AddressType;
import com.hqins.agent.postsale.model.enums.LeaveType;
import com.hqins.agent.postsale.model.enums.MessageContentType;
import com.hqins.agent.postsale.model.enums.PermissionType;
import com.hqins.agent.postsale.model.vo.LeaveApplicationsVO;
import com.hqins.agent.postsale.service.AttendanceService;
import com.hqins.agent.postsale.service.AttendanceSettingService;
import com.hqins.agent.postsale.service.AttendanceYinBaoService;
import com.hqins.agent.postsale.utils.DateUtils;
import com.hqins.basic.api.WechatApi;
import com.hqins.basic.model.enums.WechatAppType;
import com.hqins.common.base.enums.AgentOrgType;
import com.hqins.common.base.errors.BadRequestException;
import com.hqins.common.helper.BeanCopier;
import com.hqins.common.utils.JsonUtil;
import com.hqins.common.utils.StringUtil;
import com.hqins.common.utils.UUIDUtils;
import com.hqins.common.web.RequestContextHolder;
import com.hqins.file.service.api.FileApi;
import com.hqins.file.service.model.request.FileBase64Request;
import com.hqins.file.service.model.vo.FileGetUrlsVO;
import com.hqins.file.service.model.vo.FileUrlVO;
import lombok.extern.slf4j.Slf4j;
import one.util.streamex.StreamEx;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.net.URL;
import java.net.URLConnection;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.List;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


@Service
@Slf4j
public class AttendanceServiceImpl implements AttendanceService {
    private final FileApi fileApi;
    private final AttendanceLogMapper attendanceLogMapper;
    private final AttendanceAddressMapper attendanceAddressMapper;
    private final EmployeeApi employeeApi;
    private final PartnerChannelRelationApi partnerChannelRelationApi;
    private final AgentAttendanceMapper agentAttendanceMapper;
    private final AttendanceCustomerMapper attendanceCustomerMapper;
    private final AttendanceActivityMapper attendanceActivityMapper;
    private final AgentImageMapper agentImageMapper;
    private final AttendanceConfig attendanceSignInImageConfig;
    private final AgentAttendanceExtMapper agentAttendanceExtMapper;
    private final LeaveApplicationsMapper leaveApplicationsMapper;
    private final AttendanceLeaveCheckMapper attendanceLeaveCheckMapper;
    private final OrgApi orgApi;

    private final EmployeeOrgApi employeeOrgApi;

    private final WechatApi wechatApi;

    private final AttendanceSettingService attendanceSettingService;

    private static final String DATE_FORMAT = "yyyy-MM-dd EEEE";
    private static final String TIME_FORMAT = "HH:mm";
    private static final int X_OFFSET = 10;
    private static final int Y_OFFSET = 10;


    private final ChannelApi channelApi;
    private final AgentAttendanceTimeMapper agentAttendanceTimeMapper;
    private final AttendanceYinBaoService attendanceYinBaoService;
    @Autowired
    private RepairLeaveApprovalMapper repairLeaveApprovalMapper;
    @Autowired
    private AttendanceConfig attendanceConfig;

    public AttendanceServiceImpl(FileApi fileApi, AttendanceLogMapper attendanceLogMapper, AttendanceAddressMapper attendanceAddressMapper,
                                 EmployeeApi employeeApi, PartnerChannelRelationApi partnerChannelRelationApi,
                                 AgentAttendanceMapper agentAttendanceMapper, AttendanceCustomerMapper attendanceCustomerMapper,
                                 AttendanceActivityMapper attendanceActivityMapper, AgentImageMapper agentImageMapper, AttendanceConfig attendanceSignInImageConfig,
                                 AgentAttendanceExtMapper agentAttendanceExtMapper, LeaveApplicationsMapper leaveApplicationsMapper,
                                 AttendanceLeaveCheckMapper attendanceLeaveCheckMapper, OrgApi orgApi, EmployeeOrgApi employeeOrgApi, WechatApi wechatApi,
                                 AttendanceSettingService attendanceSettingService, ChannelApi channelApi,
                                 AgentAttendanceTimeMapper agentAttendanceTimeMapper,@Lazy AttendanceYinBaoService attendanceYinBaoService) {
        this.fileApi = fileApi;
        this.attendanceLogMapper = attendanceLogMapper;
        this.attendanceAddressMapper = attendanceAddressMapper;
        this.employeeApi = employeeApi;
        this.partnerChannelRelationApi = partnerChannelRelationApi;
        this.agentAttendanceMapper = agentAttendanceMapper;
        this.attendanceCustomerMapper = attendanceCustomerMapper;
        this.attendanceActivityMapper = attendanceActivityMapper;
        this.agentImageMapper = agentImageMapper;
        this.attendanceSignInImageConfig = attendanceSignInImageConfig;
        this.agentAttendanceExtMapper = agentAttendanceExtMapper;
        this.leaveApplicationsMapper = leaveApplicationsMapper;
        this.attendanceLeaveCheckMapper = attendanceLeaveCheckMapper;
        this.orgApi = orgApi;
        this.employeeOrgApi = employeeOrgApi;
        this.wechatApi = wechatApi;
        this.attendanceSettingService = attendanceSettingService;
        this.channelApi = channelApi;
        this.agentAttendanceTimeMapper = agentAttendanceTimeMapper;
        this.attendanceYinBaoService = attendanceYinBaoService;
    }

    @Override
    public List<AttendanceImageResponse> imageWatermark(List<AttendanceImageRequest> request) {
        List<FileGetUrlsVO> filLis = new ArrayList<>();
        try {
            for (AttendanceImageRequest info : request) {
                FileGetUrlsVO addWatermark;
                //如果是补签类型则不需要添加水印
                if (ImageType.APPOINT_IMG.equals(info.getImageType())) {
                    addWatermark = addWatermark(info.getBase64Img(), info.getImageType());
                } else {
                    addWatermark = addWatermark(info.getBase64Img(), info.getCompany(), info.getImageType());
                }
                filLis.add(addWatermark);
            }
        } catch (Exception e) {
            log.error("异常：{}",e.getMessage(),e);
            logData(JsonUtil.toJSON(request), RequestContextHolder.getEmployeeCode(), AttendanceNodeType.SIGN_IN, Boolean.FALSE,"图片水印处理异常，已将本次签到数据记录，请联系管理员");
            throw new BadRequestException("图片处理异常，已将本次签到数据记录，请联系管理员");
        }

        if (CollectionUtils.isEmpty(filLis)) {
            logData(JsonUtil.toJSON(request), RequestContextHolder.getEmployeeCode(), AttendanceNodeType.SIGN_IN, Boolean.FALSE,"图片处理异常，已将本次签到数据记录，请联系管理员");
            throw new BadRequestException("图片处理异常，已将本次签到数据记录，请联系管理员");
        }
        return StreamEx.of(filLis).map(fileGetUrlsVO -> {
            final FileUrlVO fileUrlVO = fileGetUrlsVO.getFileUrlList().get(0);
            final String substring = fileUrlVO.getOriginalFileName().substring(0, fileUrlVO.getOriginalFileName().indexOf("&"));
            return AttendanceImageResponse.builder()
                    .imgUrl(fileUrlVO.getOuterUrl())
                    .imageType(ImageType.valueOf(substring))
                    .build();
        }).toList();
    }

    @Override
    public DropDownResponse dropDown(String longitude,String latitude) {
        String employeeCode = RequestContextHolder.getEmployeeCode();
        String orgType = RequestContextHolder.getOrgType();
/*        String employeeCode = "1441000158";
        String orgType = AgentOrgType.PARTNER.name();*/
        final EmployeeVO emp = employeeApi.getEmployeeByEmployeeCode(AgentOrgType.valueOf(orgType)
                , employeeCode);

        //查询销管数据
        List<DropDownModel> dropList = new ArrayList<>();
        //银保
        List<PartassignmanagerVO> merchantOrgList;


        if ("P00001".equals(emp.getTopCode())) {
            final EmployeeOrgVO employeeOrgVO = employeeOrgApi.queryEmployeeOrgInfo(employeeCode);
            if (null != employeeOrgVO &&
                    (employeeCode.equals(employeeOrgVO.getBusinessTeamLeaderCode()) ||
                                    employeeCode.equals(employeeOrgVO.getBusinessDeptLeaderCode()) ||
                                    employeeCode.equals(employeeOrgVO.getBusinessAreaLeaderCode()))) {
                List<ChannelOrgVO> channelOrgVOS = channelApi.listSubEmpChannel(employeeCode, Boolean.TRUE);
                merchantOrgList = StreamEx.of(channelOrgVOS).map((Function<ChannelOrgVO, PartassignmanagerVO>) channelOrgVO -> PartassignmanagerVO.builder()
                        .merchantOrgCode(channelOrgVO.getCode())
                        .merchantOrgName(channelOrgVO.getName())
                        .build()).toList();
                log.info("销管数据：{}", JsonUtil.toJSON(merchantOrgList));
            } else {
                merchantOrgList = partnerChannelRelationApi.getMerchantOrgList(employeeCode);
            }
        } else {
            merchantOrgList = partnerChannelRelationApi.getMerchantOrgList(employeeCode);
            log.info("销管数据：{}", JsonUtil.toJSON(merchantOrgList));
        }

        if (CollectionUtils.isNotEmpty(merchantOrgList)) {
            List<String> merchantOrgCode = merchantOrgList.stream().map(PartassignmanagerVO::getMerchantOrgCode).collect(Collectors.toList());
            //匹配销管于本地的数据
            List<AttendanceAddress> list = attendanceAddressMapper.selectListByOrder(merchantOrgCode, Boolean.TRUE,null,longitude,latitude,null);
            log.info("本地数据attendance：{}", JsonUtil.toJSON(list));
            //经纬度赋值
            list.forEach(info -> dropList.add(DropDownModel.builder()
                    .locationCode(info.getMerchantOrgCode())
                    .location(info.getMerchantOrgName())
                    .longitude(info.getLongitude())
                    .latitude(info.getLatitude())
                    .authenticationOrNot(info.getAuthenticationOrNot())
                    .addressType(info.getAddressType())
                    .scope(info.getScope())
                    .companyInsCode(info.getCompanyInstCode())
                    .build()));
        }
        //查询自定义网点
        List<AttendanceAddress> list2 = attendanceAddressMapper.selectListByOrder(null, Boolean.TRUE,AddressType.COMPANY.name(),longitude,latitude,emp.getOrgCode());


        //赋值自定义公司网点
        if (CollectionUtils.isNotEmpty(list2)) {
            dropList.addAll(StreamEx.of(list2).map(attendanceAddress -> DropDownModel
                    .builder()
                    .longitude(attendanceAddress.getLongitude())
                    .latitude(attendanceAddress.getLatitude())
                    .locationCode(attendanceAddress.getMerchantOrgCode())
                    .location(attendanceAddress.getMerchantOrgName())
                    .authenticationOrNot(attendanceAddress.getAuthenticationOrNot())
                    .addressType(attendanceAddress.getAddressType())
                    .scope(attendanceAddress.getScope())
                    .companyInsCode(attendanceAddress.getCompanyInstCode())
                    .build()).toList());
        }
        return DropDownResponse.builder()
                .dropList(dropList)
                .build();
    }

    @Override
    public DropDownResponse downChild(LocationCodeRequest request) {
        //获取当前登录人信息
        final EmployeeVO emp = employeeApi.getEmployeeByEmployeeCode(AgentOrgType.valueOf(RequestContextHolder.getOrgType())
                , RequestContextHolder.getEmployeeCode());
        final List<PartassignmanagerVO> managerVOS = orgApi.queryCompanyInst(emp.getOrgCode());
        List<DropDownModel> dropList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(managerVOS)) {
            final List<String> merchantOrgCode = managerVOS.stream().map(PartassignmanagerVO::getMerchantOrgCode).collect(Collectors.toList());
            //查询签到地址
            List<AttendanceAddress> list = attendanceAddressMapper.selectListByOrder(merchantOrgCode, Boolean.TRUE,null,request.getLongitude(),request.getLatitude(),null);

            //经纬度赋值
            for (AttendanceAddress info : list) {
                dropList.add(DropDownModel.builder()
                        .locationCode(info.getMerchantOrgCode())
                        .location(info.getMerchantOrgName())
                        .longitude(info.getLongitude())
                        .latitude(info.getLatitude())
                        .authenticationOrNot(info.getAuthenticationOrNot())
                        .addressType(info.getAddressType())
                        .scope(info.getScope())
                        .companyInsCode(info.getCompanyInstCode())
                        .build());
            }
        }

        if (CollectionUtils.isNotEmpty(request.getLocationCode())){
            //筛选出locationCode不在入参中的数据
            dropList = dropList.stream().filter(info -> !request.getLocationCode().contains(info.getLocationCode())).collect(Collectors.toList());
        }
        return DropDownResponse.builder()
                .dropList(dropList)
                .build();
    }

    @Override
    public Long signIn(AttendanceSignInRequest request) {

        final EmployeeVO emp = employeeApi.getEmployeeByEmployeeCode(AgentOrgType.valueOf(RequestContextHolder.getOrgType())
                , RequestContextHolder.getEmployeeCode());
        if (emp == null) {
            logData(JsonUtil.toJSON(request), RequestContextHolder.getEmployeeCode(), AttendanceNodeType.SIGN_IN, Boolean.FALSE,"人员信息不存在，请联系管理员");
            throw new BadRequestException("人员信息不存在，请联系管理员");
        }
        if (PermissionType.P00001.name().equals(emp.getTopCode())){
            Date yinBaoStartTime = attendanceConfig.getYinBaoStartTime();
            //如果当前时间在新银保的签到时间之后，不允许签到,不要用parseDate方法
            if (null != yinBaoStartTime && LocalDateTime.now().isAfter(yinBaoStartTime.toInstant().atZone(ZoneId.of("UTC")).toLocalDateTime())) {
                throw new BadRequestException("当前时间在新银保的签到时间之后，请移步到新银保签到操作");
            }

            AttendanceLeaveCheck attendanceLeaveCheck = attendanceLeaveCheckMapper.selectOne(Wrappers.lambdaQuery(AttendanceLeaveCheck.class)
                    .eq(AttendanceLeaveCheck::getDeleted, Boolean.FALSE)
                    .last(" and FIND_IN_SET('" + emp.getCode() + "', attendance_codes)>0  limit 1"));
            if (attendanceLeaveCheck != null){
                throw new BadRequestException("当前功能已禁用，有问题请联系机构内勤");
            }
        }

        //查询归属渠道商
        String companyName = "";
        String companyCode = "";
        if (request.getAppointType().equals(AppointType.APPOINT)
                && AddressType.MERCHANT.name().equals(request.getAddressType())){
            AttendanceAddress attendanceAddress = new LambdaQueryChainWrapper<>(attendanceAddressMapper)
                    .eq(AttendanceAddress::getMerchantOrgCode, request.getLocationCode())
                    .last("limit 1")
                    .one();
            companyName = attendanceAddress.getMerchantName();
            companyCode = attendanceAddress.getMerchantCode();
        }

        //保存打卡数据
        AgentAttendance agentAttendance = AgentAttendance.builder()
                .employeeCode(RequestContextHolder.getEmployeeCode())
                .orgType(RequestContextHolder.getOrgType())
                .orgCode(emp.getOrgCode())
                .employeeName(emp.getName())
                .addressType(request.getAddressType())
                .orgName(emp.getOrgName())
                .topCode(emp.getTopCode())
                .topName(emp.getTopName())
                .companyName(companyName)
                .companyCode(companyCode)
                .locationCode(request.getLocationCode())
                .locationName(request.getLocationName())
                .latitude(request.getLatitude())
                .longitude(request.getLongitude())
                .signInType(request.getAppointType().name())
                .spotCheckType(SpotCheckType.UNTESTED.name())
                .checkInTime(LocalDateTime.now())
                .updateTime(LocalDateTime.now())
                .build();

        if ("BG".equals(request.getOrgTypeStr())){
            agentAttendance.setAudit(AttendanceCheckType.AUTO_BYPASS.name());
        }

        //判断是否需要审核
        if (request.getAttendanceType().contains(SignInType.NO_ADDRESS)
                || request.getAttendanceType().contains(SignInType.NON_ASSIGNED)
                || request.getAttendanceType().contains(SignInType.TIME_ERROR)
            //    || request.getAddressType().equals(AddressType.COMPANY.name())
                || request.getAttendanceType().contains(SignInType.NON_SPECIFIED)) {
            agentAttendance.setAuditType(request.getAttendanceType().stream().map(SignInType::toString)
                    .collect(Collectors.joining(",")));
           if ("BG".equals(request.getOrgTypeStr())){
               agentAttendance.setAudit(AttendanceCheckType.TO_BE_REVIEWED.name());
           }
        } else {
            //保存认证数据
            AttendanceAddress one = new LambdaQueryChainWrapper<>(attendanceAddressMapper)
                    .eq(AttendanceAddress::getMerchantOrgCode, request.getLocationCode())
                    .eq(AttendanceAddress::getStatus, Boolean.TRUE)
                    .eq(AttendanceAddress::getAuthenticationOrNot, Boolean.FALSE)
                    .one();
            if (null != one) {
                attendanceAddressMapper.updateByPrimaryKeySelective(AttendanceAddress.builder()
                        .id(one.getId())
                        .authenticationOrNot(Boolean.TRUE)
                        .authenticationEmployeeCode(emp.getCode())
                        .authenticationEmployeeName(emp.getName())
                        .authenticationTime(LocalDateTime.now())
                        .updateTime(LocalDateTime.now())
                        .build());
            }
            //范围内签到的数据为签到状态类型
            agentAttendance.setAuditType(SignInType.SIGN_IN.name());
        }
        agentAttendanceMapper.insertSelective(agentAttendance);

        //保存影像
        agentImageMapper.insertSelective(AgentImage.builder()
                .employeeCode(RequestContextHolder.getEmployeeCode())
                .agentId(RequestContextHolder.getAgentId().toString())
                .imageUrl(request.getImageUrl())
                .imageType(ImageType.SIGN_IN_IMG.name())
                .imageKey(agentAttendance.getId().toString())
                .build());
        //保存日志
        logData(JsonUtil.toJSON(request), RequestContextHolder.getEmployeeCode(), AttendanceNodeType.SIGN_IN, Boolean.TRUE,"");
        return agentAttendance.getId();
    }

    @Override
    public List<AgentAttendance> attendanceType(String locationCode) {
        // 获取今天的日期时间
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String today = now.format(formatter);

        final String employeeCode = RequestContextHolder.getEmployeeCode();
        final List<AgentAttendance> list = new LambdaQueryChainWrapper<>(agentAttendanceMapper)
                .eq(AgentAttendance::getEmployeeCode, employeeCode)
                .eq(AgentAttendance::getLocationCode, locationCode)
                .eq(AgentAttendance::getDeleted, Boolean.FALSE)
                .apply("DATE(check_in_time) = '" + today + "'")
                .list();
        if (CollectionUtils.isEmpty(list)){
            return new ArrayList<>();
        }
        return list;
    }

    @Override
    public SignOutResponse signOut(CheckOutRequest request) {
        if (request.getId() == null){
            throw new BadRequestException("签退失败，缺少唯一ID");
        }

        final LocalDateTime now = LocalDateTime.now();
        final String employeeCode = RequestContextHolder.getEmployeeCode();
        //查询签退状态
        AgentAttendance one = new LambdaQueryChainWrapper<>(agentAttendanceMapper)
                .eq(AgentAttendance::getEmployeeCode, employeeCode)
                .eq(AgentAttendance::getId, request.getId())
                .one();

        //更新签退时间
        AgentAttendance build = AgentAttendance.builder()
                .id(request.getId())
                .checkOutTime(now)
                .activityLabel(request.getActivityLabel())
                .businessLabel(String.join(",", request.getBusinessLabel()))
                .updateTime(LocalDateTime.now())
                .outLatitude(request.getLatitude())
                .outLongitude(request.getLongitude())
                .build();
        //如果签到是正常签到 审核情况赋值自动审核通过
        List<String> strings = Arrays.asList(one.getAuditType().split(","));
        if (strings.contains(SignInType.SIGN_IN.name())
                && strings.size() == 1) {
            build.setAudit(AttendanceCheckType.AUTO_BYPASS.name());
        }

        if (strings.contains(SignInType.NO_ADDRESS.name())
                || strings.contains(SignInType.NON_ASSIGNED.name())
                || strings.contains(SignInType.NON_SPECIFIED.name())) {
            build.setAudit(AttendanceCheckType.TO_BE_REVIEWED.name());
        }

        int i = agentAttendanceMapper.updateByPrimaryKeySelective(build);
        if (i<=0){
            throw new BadRequestException("签退失败，没有对应的签到记录");
        }

        //保存客户信息
        if (CollectionUtils.isNotEmpty(request.getCustomerData())) {

            request.getCustomerData().forEach(info -> {
                AttendanceCustomer attendanceCustomer = BeanCopier.copyObject(info, AttendanceCustomer.class);
                attendanceCustomer.setAttendanceId(request.getId());
                attendanceCustomer.setCreatedTime(LocalDateTime.now());
                attendanceCustomer.setUpdatedTime(LocalDateTime.now());
                //前三项为空不存地点
                if (StringUtil.isEmpty(info.getCustomerName()) ||
                    StringUtil.isEmpty(info.getCustomerGender()) ||
                    null == info.getEstimatedPremium()){
                    attendanceCustomer.setOutlets(null);
                    attendanceCustomer.setOutletsCode(null);
                }
                attendanceCustomerMapper.insertSelective(attendanceCustomer);
            });
        }

        //保存活动信息
        if (CollectionUtils.isNotEmpty(request.getActivityData())) {
            request.getActivityData().forEach(info -> {
                AttendanceActivity attendanceActivity = BeanCopier.copyObject(info, AttendanceActivity.class);
                attendanceActivity.setAttendanceId(request.getId());
                attendanceActivity.setActivityOutletsCode(info.getActivityOutletsCode());
                attendanceActivityMapper.insertSelective(attendanceActivity);
            });
        }

        //保存日志
        logData(JsonUtil.toJSON(request), RequestContextHolder.getEmployeeCode(), AttendanceNodeType.SIGN_OUT, Boolean.TRUE,"");

        //整合标签数据
        if (StringUtil.isNotEmpty(request.getActivityLabel())){
            request.getBusinessLabel().add(request.getActivityLabel());
        }

        return SignOutResponse.builder()
                .signOutTime(now)
                .signInTime(one.getCheckInTime())
                .label(request.getBusinessLabel())
                .build();
    }

    @Override
    public Map<String, AttendanceObjConfig> advertising() {
        Map<String, AttendanceObjConfig> advertisement = attendanceSignInImageConfig.getAdvertisement();
        log.info("输出{}", JsonUtil.toJSON(attendanceSignInImageConfig.getAdvertisement()));
        return advertisement;
    }

    private Boolean checkLeader(String employeeCode){
        EmployeeOrgVO employeeOrgVO = employeeOrgApi.queryEmployeeOrgInfo(employeeCode);
        if (null == employeeOrgVO) {
            return false;
        }
        return employeeCode.equals(employeeOrgVO.getBusinessTeamLeaderCode())
                || employeeCode.equals(employeeOrgVO.getBusinessAreaLeaderCode())
                || employeeCode.equals(employeeOrgVO.getBusinessDeptLeaderCode());
    }

    private Map<Integer, DataStatisticsTimerVO> makeTimeResMap(Map<Integer,AgentAttendanceTime> agentAttendanceTimeMap){
        Map<Integer, DataStatisticsTimerVO> resMap = new HashMap<>();
        for (Integer day : agentAttendanceTimeMap.keySet()){
            DataStatisticsTimerVO dataStatisticsTimerVO = new DataStatisticsTimerVO();
            AgentAttendanceTime agentAttendanceTime = agentAttendanceTimeMap.get(day);
            dataStatisticsTimerVO.setAgentClockCount(agentAttendanceTime.getAgentClockCount());
            dataStatisticsTimerVO.setLeaderClockCount(agentAttendanceTime.getLeaderClockCount());
            resMap.put(day,dataStatisticsTimerVO);
        }
        return resMap;
    }

    @Override
    public DataStatisticsResponse daKaDataStatistics(DataStatisticsRequest request) {
        String employeeCode = StringUtils.isEmpty(RequestContextHolder.getEmployeeCode()) ? request.getEmployeeCode() : RequestContextHolder.getEmployeeCode();
        String orgType = StringUtils.isEmpty(request.getOrgTypeStr()) ? RequestContextHolder.getOrgType() : request.getOrgTypeStr();
        EmployeeVO employeeInfo = employeeApi.getEmployeeByEmployeeCode(AgentOrgType.valueOf(orgType), employeeCode);
        request.setEmployeeInfo(employeeInfo);
        DataStatisticsResponse dataStatisticsResponse;
        //区分个团+老银保 、 新银保
        if ("P00001".equals(employeeInfo.getTopCode())) {
            //银保
            LocalDate yinBaoStartDate = attendanceSignInImageConfig.getYinBaoStartTime().toInstant().atZone(ZoneId.of("UTC")).toLocalDate();
            LocalDate monthStartDate = LocalDate.of(request.getYears(),request.getMonth(),1);
            if (!monthStartDate.isBefore(yinBaoStartDate)){
                dataStatisticsResponse = attendanceYinBaoService.dataStatisticsYinBao(request);
            }else {
                dataStatisticsResponse = dataStatistics(request);
            }
        }else{
            //非银保
            dataStatisticsResponse = dataStatistics(request);
        }
        dataStatisticsResponse.setNewTime(attendanceConfig.getYinBaoStartTime());
        return dataStatisticsResponse;
    }

    @Override
    public DataStatisticsResponse dataStatistics(DataStatisticsRequest request) {
        String employeeCode = StringUtils.isEmpty(RequestContextHolder.getEmployeeCode()) ? request.getEmployeeCode() : RequestContextHolder.getEmployeeCode();
        DataStatisticsResponse dataStatisticsResponse = new DataStatisticsResponse();
        String orgType = StringUtils.isEmpty(request.getOrgTypeStr()) ? RequestContextHolder.getOrgType() : request.getOrgTypeStr();
        String orgCode = request.getOrgCode();
        String topCode = request.getTopCode();
        LocalDateTime monthStartTime = LocalDateTime.of(request.getYears(),request.getMonth(),1,0,0,0);
        LocalDateTime monthEndTime = LocalDateTime.of(request.getYears(),request.getMonth(),monthStartTime.with(TemporalAdjusters.lastDayOfMonth()).getDayOfMonth(),23,59,59);
        EmployeeVO employeeInfo;
        if (StringUtil.isEmpty(topCode) || StringUtil.isEmpty(orgCode)){
            if (request.getEmployeeInfo() == null){
                employeeInfo = employeeApi.getEmployeeByEmployeeCode(AgentOrgType.valueOf(orgType), employeeCode);
            }else {
                employeeInfo = request.getEmployeeInfo();
            }
            orgCode = StringUtil.isEmpty(orgCode) ? employeeInfo.getOrgCode() : orgCode;
            topCode = StringUtil.isEmpty(topCode) ? employeeInfo.getTopCode() : topCode;
        }
        Boolean isBank = "P00001".equals(topCode);
        dataStatisticsResponse.setIsBank(isBank);
        AgentAttendanceTime agentAttendanceTime;
        Map<Integer,AgentAttendanceTime> agentAttendanceTimeMap = request.getAgentAttendanceTimeMap();
        if (agentAttendanceTimeMap == null){
            agentAttendanceTimeMap = makeMonthTimerMap(request.getYears(),request.getMonth(),orgCode,isBank);
            agentAttendanceTime = agentAttendanceTimeMap.get(0);
        }else {
            agentAttendanceTime = request.getAgentAttendanceTimeMap().get(0);
        }
        //应出勤天数
        String monthJson = agentAttendanceTime.getMonthJson();
        Map<String,String> monthMap = JSONObject.parseObject(monthJson, Map.class);
        dataStatisticsResponse.setNeedAttendanceDays(String.valueOf(monthMap.get(String.valueOf(request.getMonth()))));
        //设置打卡配置map
        dataStatisticsResponse.setDataStatisticsTimerMap(makeTimeResMap(agentAttendanceTimeMap));
        // 查询打卡数据
        List<MonthPunchIn> monthPunchIns ;
        if (request.getMonthPunchInList() == null){
            monthPunchIns = agentAttendanceExtMapper.selectCalendarDataByMonth(request, employeeCode,monthStartTime,monthEndTime);
        }else {
            monthPunchIns = request.getMonthPunchInList();
        }
        // 查询当月成功打卡地点数
        Integer monthPunchInCount = 0;
        if (request.getIsNeedMonthPunchInCount() == null || request.getIsNeedMonthPunchInCount()){
            monthPunchInCount = agentAttendanceExtMapper.selectCalendarByMonth(monthStartTime,monthEndTime, employeeCode);
        }
        dataStatisticsResponse.setMonthPunchInCount(monthPunchInCount);
        dataStatisticsResponse.setIsBk(Boolean.TRUE);
        if (isBank){
            dataStatisticsResponse.setIsLeader(checkLeader(employeeCode));
            //查询补卡剩余次数
            final int agentRepairCount =
                    (agentAttendanceTime == null || agentAttendanceTime.getAgentRepairCount() == null) ? 10 :agentAttendanceTime.getAgentRepairCount();
            final int leaderRepairCount =
                    (agentAttendanceTime == null || agentAttendanceTime.getLeaderRepairCount() == null) ? 10 :agentAttendanceTime.getLeaderRepairCount();
            final List<MonthPunchIn> collect1 = monthPunchIns.stream().filter(info -> info.getAuditType().contains(SignInType.APPOINT_HALF.name())).collect(Collectors.toList());
            int count = agentRepairCount - collect1.size();
            if (dataStatisticsResponse.getIsLeader()){
                count = leaderRepairCount - collect1.size();
            }
            if (count <= 0){
                dataStatisticsResponse.setIsBk(Boolean.FALSE);
            }
        }else{
            int i = attendanceSignInImageConfig.getGi();
            List<String> str = monthPunchIns.stream().map(MonthPunchIn::getAuditType).collect(Collectors.toList());
            //计算剩余补签次数，半天-1天 全天-2天 0次不可补签
            for (String s : str) {
                if (s.contains(SignInType.APPOINT_HALF.name())){
                    //计算APPOINT_HALF在s中出现的次数
                    int count = StringUtils.countMatches(s, SignInType.APPOINT_HALF.name());
                    i = i - count;
                }else if (s.contains(SignInType.APPOINT_ALL.name())){
                    i = i - 2;
                }
            }
            int max = Math.max(i, 0);
            if (max == 0){
                dataStatisticsResponse.setIsBk(Boolean.FALSE);
            }
        }
        //根据日期分组
        Map<Integer, List<MonthPunchIn>> data = monthPunchIns.stream().collect(Collectors.groupingBy(MonthPunchIn::getThatDay, Collectors.toList()));
        //对每天的数据进行排序 排序规则：每天的签到数据根据locationCode分组，组内根据signInTime正序排序，组与组之间的排序根据signInTime最早的排序
        Map<Integer, List<MonthPunchIn>> sortedData = new LinkedHashMap<>();
        for (Map.Entry<Integer, List<MonthPunchIn>> entry : data.entrySet()) {
            List<MonthPunchIn> sortedGroup = sortGroup(entry.getValue());
            sortedData.put(entry.getKey(), sortedGroup);
        }
        //查询请假记录
        List<LeaveApplicationsVO> leaveApplications;
        if (request.getLeaveApplicationsVOList() == null){
            leaveApplications = leaveApplicationsMapper.selectCalendarByMonth(monthStartTime,monthEndTime,employeeCode);
        }else {
            leaveApplications = request.getLeaveApplicationsVOList();
        }
        List<LeaveApplicationsVO> addBankleaveApplications = new ArrayList<>();
        List<LeaveApplicationsVO> addLeaveApplications = new ArrayList<>();
        dataStatisticsResponse.setMonthBankLeaveIns(new HashMap<>());
        for (LeaveApplicationsVO leaveApplication : leaveApplications) {
            //处理跨天或半天请假，拆为多条记录
            if (!isBank){
                //非银保
                LocalDateTime beginTime = leaveApplication.getBeginTime();
                LocalDateTime endTime = leaveApplication.getEndTime();
                addLeaveApplications.addAll(decomposeTimeIntervalNoBank(beginTime,endTime,Double.parseDouble(leaveApplication.getDuration()),leaveApplication));
            }else{
                //银保
                LocalDateTime beginTime = leaveApplication.getBeginTime();
                LocalDateTime endTime = leaveApplication.getEndTime();
                addBankleaveApplications.addAll(decomposeTimeInterval(beginTime,endTime,Double.parseDouble(leaveApplication.getDuration()),leaveApplication));
            }
        }
        if (CollectionUtils.isNotEmpty(addBankleaveApplications)){
            Map<Integer, List<LeaveApplicationsVO>> bankLeaveMap = addBankleaveApplications.stream()
                    .filter(x->x.getBeginTime().getMonthValue() == request.getMonth())
                    .sorted(Comparator.comparing(LeaveApplicationsVO::getMorningOrAfternoon))
                    .collect(Collectors.groupingBy(o -> o.getBeginTime().getDayOfMonth()));
            dataStatisticsResponse.setMonthBankLeaveIns(bankLeaveMap);
        }else{
            dataStatisticsResponse.setMonthBankLeaveIns(new HashMap<>());
        }
        if (CollectionUtils.isNotEmpty(addLeaveApplications)){
            Map<Integer, LeaveApplicationsVO> noBankLeaveMap = new HashMap<>();
            for (LeaveApplicationsVO leaveApplicationsVO : addLeaveApplications){
                if(leaveApplicationsVO.getBeginTime().getMonthValue() == request.getMonth()){
                    noBankLeaveMap.put(leaveApplicationsVO.getBeginTime().getDayOfMonth(),leaveApplicationsVO);
                }
            }
            dataStatisticsResponse.setMonthLeaveIns(noBankLeaveMap);
        }else{
            dataStatisticsResponse.setMonthLeaveIns(new HashMap<>());
        }
        dataStatisticsResponse.setMonthPunchIns(sortedData);
        statisticsMonthAttendanceMonth(request,dataStatisticsResponse,agentAttendanceTimeMap);
        return dataStatisticsResponse;
    }

    private void statisticsMonthAttendanceMonth(DataStatisticsRequest request,DataStatisticsResponse dataStatisticsResponse,
                                                Map<Integer,AgentAttendanceTime> agentAttendanceTimeMap){
        Boolean isBank = dataStatisticsResponse.getIsBank();
        Boolean isLeader = dataStatisticsResponse.getIsLeader();
        //签到数据
        Map<Integer, List<MonthPunchIn>> monthPunchIns = dataStatisticsResponse.getMonthPunchIns();
        //非银保-请假数据
        Map<Integer, LeaveApplicationsVO> monthLeaveIns = dataStatisticsResponse.getMonthLeaveIns();
        //银保-请假数据
        Map<Integer, List<LeaveApplicationsVO>> monthBankLeaveIns = dataStatisticsResponse.getMonthBankLeaveIns();
        int lastDay;
        LocalDate today = LocalDate.now();
        if (request.getYears() > today.getYear() || (request.getYears() == today.getYear() && today.getMonthValue() < request.getMonth())){
            //未来月不统计
            dataStatisticsResponse.setNormalAttendance(0);
            dataStatisticsResponse.setUnusualAttendance(0);
            dataStatisticsResponse.setLeaveAttendance(0);
            return;
        }

        if (LocalDate.of(request.getYears(), request.getMonth(), 1).isEqual(today.with(TemporalAdjusters.firstDayOfMonth()))){
            //当前月
            lastDay = today.getDayOfMonth();
        }else{
            lastDay = LocalDate.of(request.getYears(),request.getMonth(),1).with(TemporalAdjusters.lastDayOfMonth()).getDayOfMonth();
        }
        double normalAttendance = 0;   //正常考勤次数
        double unusualAttendance = 0;  //异常打卡次数
        double leaveAttendance = 0;    //请假天数
        double successDaKaDays = 0;       //成功打卡天数
        AgentAttendanceTime agentAttendanceTime ;
        for (int day = 1; day <= lastDay ; day++){
            agentAttendanceTime = agentAttendanceTimeMap.get(day);
            //需要打卡次数
            int needClockCount;
            if (isBank){
                //客户经理打卡次数
                int agentClockCount = (agentAttendanceTime == null || agentAttendanceTime.getAgentClockCount()==null) ? 2 : agentAttendanceTime.getAgentClockCount();
                //主管打卡次数
                int leaderClockCount = (agentAttendanceTime == null || agentAttendanceTime.getLeaderClockCount() ==null)? 2 : agentAttendanceTime.getLeaderClockCount();
                needClockCount = isLeader ? leaderClockCount : agentClockCount;
            }else {
                needClockCount = 1;
            }
            //打卡数据
            List<MonthPunchIn> monthPunchInList = CollectionUtils.isEmpty(monthPunchIns.get(day)) ? new ArrayList<>(0) : monthPunchIns.get(day);
            //统计异常考勤
            for (MonthPunchIn monthPunchIn : monthPunchInList){
                if (today.getDayOfMonth() == day){
                    if (LocalDate.of(request.getYears(), request.getMonth(), day).isEqual(today)){
                        //异常打卡，不统计当天
                        continue;
                    }
                }
                //判断是否异常打卡
                String checkType = monthPunchIn.getCheckType();
                String spotCheckType = monthPunchIn.getSpotCheckType();
                if (!AttendanceCheckType.PASS_THE_AUDIT.name().equals(checkType)
                        && !AttendanceCheckType.AUTO_BYPASS.name().equals(checkType)
                        && !(AttendanceCheckType.TO_BE_REVIEWED.name().equals(checkType) && SpotCheckType.SPOT_CHECK.name().equals(spotCheckType))
                         ){
                    unusualAttendance++;
                }
            }

            if (!isBank){
                //非银保
                LeaveApplicationsVO leaveApplicationsVO = monthLeaveIns.get(day);
                if (leaveApplicationsVO != null && AttendanceCheckType.PASS_THE_AUDIT.name().equals(leaveApplicationsVO.getAudit())){
                    //请假，审核通过
                    normalAttendance++;
                    leaveAttendance++;
                    continue;
                }
                if (CollectionUtils.isEmpty(monthPunchInList)){
                    continue;
                }
                for (MonthPunchIn monthPunchIn : monthPunchInList){
                    String checkType = monthPunchIn.getCheckType();
                    String spotCheckType = monthPunchIn.getSpotCheckType();
                    //判断是否有正常打卡
                    if (StringUtils.isNotEmpty(monthPunchIn.getSignInTime()) && StringUtils.isNotEmpty(monthPunchIn.getSignOutTime())
                            && (AttendanceCheckType.PASS_THE_AUDIT.name().equals(checkType) || AttendanceCheckType.AUTO_BYPASS.name().equals(checkType))){
                        //打卡，审核通过
                        normalAttendance++;
                        successDaKaDays++;
                        break;
                    }
                    if (StringUtils.isNotEmpty(monthPunchIn.getSignInTime()) && StringUtils.isNotEmpty(monthPunchIn.getSignOutTime())
                            && (AttendanceCheckType.TO_BE_REVIEWED.name().equals(checkType) && SpotCheckType.SPOT_CHECK.name().equals(spotCheckType))){
                        //打卡，抽查、待审核
                        normalAttendance++;
                        successDaKaDays++;
                        break;
                    }
                }
            }else{
                //银保
                //上午是否正常打卡（考勤，算请假）
                boolean shangWuCheck = false;
                //下午是否正常打卡（考勤，算请假）
                boolean xiaWuCheck = false;

                //上午是否正常打卡（考勤，不算请假）
                boolean shangWuCheckDaka = false;
                //下午是否正常打卡（考勤，不算请假）
                boolean xiaWuCheckDaka = false;

                //银保请假列表
                List<LeaveApplicationsVO> leaveApplicationsVOList = CollectionUtils.isEmpty(monthBankLeaveIns.get(day)) ? new ArrayList<>(0) : monthBankLeaveIns.get(day);
                for (LeaveApplicationsVO leaveApplicationsVO : leaveApplicationsVOList){
                    if (AttendanceCheckType.PASS_THE_AUDIT.name().equals(leaveApplicationsVO.getAudit())){
                        leaveAttendance += 0.5;
                    }
                    if (LeaveType.PERSONAL_LEAVE.name().equals(leaveApplicationsVO.getLeaveType())){
                        continue;
                    }
                    if (AttendanceCheckType.PASS_THE_AUDIT.name().equals(leaveApplicationsVO.getAudit())){
                        //请假，审核通过
                        if (leaveApplicationsVO.getMorningOrAfternoon() == 0){
                            shangWuCheck = true;
                        }
                        if (leaveApplicationsVO.getMorningOrAfternoon() == 1){
                            xiaWuCheck = true;
                        }
                    }
                }
                //打卡上午范围12:00:59
                LocalTime noon = LocalTime.of(12,1,0);
                for (MonthPunchIn monthPunchIn : monthPunchInList){
                    String checkType = monthPunchIn.getCheckType();
                    String spotCheckType = monthPunchIn.getSpotCheckType();
                    //判断是否有正常打卡
                    if (AttendanceCheckType.PASS_THE_AUDIT.name().equals(checkType) || AttendanceCheckType.AUTO_BYPASS.name().equals(checkType)
                     || (AttendanceCheckType.TO_BE_REVIEWED.name().equals(checkType) && SpotCheckType.SPOT_CHECK.name().equals(spotCheckType))){
                        //打卡，审核通过
                        if (DateUtils.convertLocalDateTime(monthPunchIn.getSignInTime()).toLocalTime().isBefore(noon)) {
                            shangWuCheck = true;
                            shangWuCheckDaka = true;
                        } else {
                            xiaWuCheck = true;
                            xiaWuCheckDaka = true;
                        }
                    }
                }
                if(needClockCount == 1){
                    if (shangWuCheck || xiaWuCheck){
                        normalAttendance += 1;
                    }
                    if (shangWuCheckDaka || xiaWuCheckDaka){
                        successDaKaDays += 1;
                    }
                }
                if(needClockCount == 2){
                    if (shangWuCheck){
                        normalAttendance += 0.5;
                    }
                    if (xiaWuCheck){
                        normalAttendance += 0.5;
                    }

                    if (shangWuCheckDaka){
                        successDaKaDays += 0.5;
                    }
                    if (xiaWuCheckDaka){
                        successDaKaDays += 0.5;
                    }
                }
            }
        }
        dataStatisticsResponse.setNormalAttendance(normalAttendance);
        dataStatisticsResponse.setUnusualAttendance(unusualAttendance);
        dataStatisticsResponse.setLeaveAttendance(leaveAttendance);
        dataStatisticsResponse.setSuccessDaKaDays(successDaKaDays);
    }

    /**
     * 获取1个月每天的配置MAP，0：最新的配置
     */
    public Map<Integer,AgentAttendanceTime> makeMonthTimerMap(Integer year,Integer month,String orgCode,Boolean isBank){
        Map<Integer,AgentAttendanceTime> timeMap = new HashMap<>();
        LocalDateTime yinBaoStartTime = attendanceSignInImageConfig.getYinBaoStartTime().toInstant().atZone(ZoneId.of("UTC")).toLocalDateTime();
        List<AgentAttendanceTime> agentAttendanceTimeList = agentAttendanceTimeMapper.selectList(new LambdaQueryWrapper<AgentAttendanceTime>()
                .eq(AgentAttendanceTime::getOrgIns, orgCode)
                .le(AgentAttendanceTime::getYears, String.valueOf(year))
                .lt(isBank,AgentAttendanceTime::getCreatedTime,yinBaoStartTime)
                .orderByDesc(AgentAttendanceTime::getYears)
                .orderByDesc(AgentAttendanceTime::getCreatedTime)
        );
        LocalDate monthStartDate = LocalDate.of(year,month,1);
        LocalDate monthEndDate = LocalDate.of(year,month,monthStartDate.with(TemporalAdjusters.lastDayOfMonth()).getDayOfMonth());
        int days = monthEndDate.getDayOfMonth();

        if (CollectionUtils.isEmpty(agentAttendanceTimeList)){
            for (int day = 1; day <= days; day++){
                timeMap.put(day,AgentAttendanceTime.builder()
                        .agentClockCount(2)
                        .leaderClockCount(2)
                        .build());
            }
            return timeMap;
        }
        //agentAttendanceTimeList根据createdTime年月日分组成map
        timeMap.put(0,agentAttendanceTimeList.get(0));
        for (int day = 1; day <= days; day++){
            monthStartDate = LocalDate.of(year,month,day);
            AgentAttendanceTime timeVo = AgentAttendanceTime.builder()
                    .agentClockCount(2)
                    .leaderClockCount(2)
                    .build();
            for (AgentAttendanceTime attendanceTime : agentAttendanceTimeList){
                LocalDate createdTime = attendanceTime.getCreatedTime().toLocalDate();
                if (createdTime.isBefore(monthStartDate) || createdTime.equals(monthStartDate)){
                    timeVo = attendanceTime;
                    break;
                }
            }
            timeMap.put(day,timeVo);
        }
        return timeMap;
    }

    public static List<LeaveApplicationsVO> decomposeTimeIntervalNoBank(LocalDateTime start, LocalDateTime end,double duration,LeaveApplicationsVO leaveApplicationsVO) {
        List<LeaveApplicationsVO> leaveApplicationsVOList = new ArrayList<>();
        if (duration == 1){
            leaveApplicationsVOList.add(leaveApplicationsVO);
            return leaveApplicationsVOList;
        }
        DateTimeFormatter formatterDate = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        DateTimeFormatter formatterDateTime = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String dateStr;
        for (int i = 1;i <= duration ;i++){
            dateStr = start.format(formatterDate);
            String startStr = dateStr + " 00:00:00";
            String endStr = dateStr + " 23:59:59";
            LeaveApplicationsVO vo = BeanCopier.copyObject(leaveApplicationsVO,LeaveApplicationsVO.class);
            vo.setBeginTime(LocalDateTime.parse(startStr,formatterDateTime));
            vo.setEndTime(LocalDateTime.parse(endStr,formatterDateTime));
            leaveApplicationsVOList.add(vo);
            start = start.plusDays(1);
        }
        return leaveApplicationsVOList;
    }

    /**
     * 银保分割时间段 ，以半天为单位
     */
    public static List<LeaveApplicationsVO> decomposeTimeInterval(LocalDateTime start, LocalDateTime end,double duration,LeaveApplicationsVO leaveApplicationsVO) {
        List<LeaveApplicationsVO> leaveApplicationsVOList = new ArrayList<>();
        if (duration == 0.5){
            if (start.toLocalTime().isBefore(LocalTime.NOON)) {
                leaveApplicationsVO.setMorningOrAfternoon(0);
            } else {
                leaveApplicationsVO.setMorningOrAfternoon(1);
            }
            leaveApplicationsVOList.add(leaveApplicationsVO);
            return leaveApplicationsVOList;
        }
        DateTimeFormatter formatterDate = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        DateTimeFormatter formatterDateTime = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        boolean isShangWu = start.toLocalTime().isBefore(LocalTime.NOON);
        int starIndex = isShangWu ? 1 : 2;
        for (int i = 1 ; i <= duration / 0.5 ; i++){
            String dateStr = start.format(formatterDate);
            if (starIndex % 2 == 1){
                //上午
                String startStr = dateStr + " 00:00:00";
                String endStr = dateStr + " 12:00:00";
                LeaveApplicationsVO vo = BeanCopier.copyObject(leaveApplicationsVO,LeaveApplicationsVO.class);
                vo.setBeginTime(LocalDateTime.parse(startStr,formatterDateTime));
                vo.setEndTime(LocalDateTime.parse(endStr,formatterDateTime));
                vo.setMorningOrAfternoon(0);
                leaveApplicationsVOList.add(vo);
            }else{
                //下午
                String startStr = dateStr + " 12:00:01";
                String endStr = dateStr + " 23:59:59";
                LeaveApplicationsVO vo = BeanCopier.copyObject(leaveApplicationsVO,LeaveApplicationsVO.class);
                vo.setBeginTime(LocalDateTime.parse(startStr,formatterDateTime));
                vo.setEndTime(LocalDateTime.parse(endStr,formatterDateTime));
                vo.setMorningOrAfternoon(1);
                leaveApplicationsVOList.add(vo);
                start = start.plusDays(1);
            }
            starIndex++;
        }
        return leaveApplicationsVOList;
    }


    //sortGroup方法
    private List<MonthPunchIn> sortGroup(List<MonthPunchIn> monthPunchIns) {
        //根据locationCode分组
//        Map<String, List<MonthPunchIn>> groupedMonthPunchIns = monthPunchIns.stream()
//                .sorted(Comparator.comparing(MonthPunchIn::getSignInTime))
//                .collect(Collectors.groupingBy(MonthPunchIn::getLocationCode));
//
//        //根据每个组signInTime最早的排序，组与组之间
//        List<List<MonthPunchIn>> sortedGroups = groupedMonthPunchIns.values().stream()
//                .sorted(Comparator.comparing(group -> group.get(0).getSignInTime()))
//                .collect(Collectors.toList());
//
//        //将排序后的组转换为List<MonthPunchIn>
//        List<MonthPunchIn> sortedMonthPunchIns = new ArrayList<>();
//        for (List<MonthPunchIn> group : sortedGroups) {
//            sortedMonthPunchIns.addAll(group);
//        }
        return monthPunchIns.stream().sorted(Comparator.comparing(MonthPunchIn::getSignInTime)).collect(Collectors.toList());
    }

    @Override
    @Transactional
    public Integer supplement(ReSignRequest request) {
        final EmployeeVO emp = employeeApi.getEmployeeByEmployeeCode(AgentOrgType.valueOf(RequestContextHolder.getOrgType())
                , RequestContextHolder.getEmployeeCode());

        if (PermissionType.P00001.name().equals(emp.getTopCode())){
            AttendanceLeaveCheck attendanceLeaveCheck = attendanceLeaveCheckMapper.selectOne(Wrappers.lambdaQuery(AttendanceLeaveCheck.class)
                    .eq(AttendanceLeaveCheck::getDeleted, Boolean.FALSE)
                    .last(" and FIND_IN_SET('" + emp.getCode() + "', attendance_codes)>0  limit 1"));
            if (attendanceLeaveCheck != null){
                throw new BadRequestException("当前功能已禁用，有问题请联系机构内勤");
            }
        }
        replacementCheck(request.getSignInTime().withSecond(0),
                null == request.getSignOutTime()? null :request.getSignOutTime().withSecond(0),
                request.getSignInType(),
                emp.getOrgCode(),
                emp.getTopCode(),
                request.getId(),
                request.getLeader()
        );
        //查询归属渠道商
        String merchantName = "";
        String merchantCode = "";
        if (request.getAppointType().equals(AppointType.APPOINT.name())){
            AttendanceAddress attendanceAddress = new LambdaQueryChainWrapper<>(attendanceAddressMapper)
                    .eq(AttendanceAddress::getMerchantOrgCode, request.getLocationCode())
                    .last("limit 1")
                    .one();
            if (null != attendanceAddress){
                merchantName = attendanceAddress.getMerchantName();
                merchantCode = attendanceAddress.getMerchantCode();
            }
        }

        //数据填充
        EmployeeOrgVO employeeOrgVO = employeeOrgApi.queryEmployeeOrgInfo(RequestContextHolder.getEmployeeCode());
        Long id = request.getId();
        if (SignInType.APPOINT_HALF.equals(request.getSignInType())
                && !request.getOrgTypeStr().equals("BG")
        ){
            //查询auditType拼接auditType的字段
            AgentAttendance agentAttendance = agentAttendanceMapper.selectById(id);
            String auditType = agentAttendance.getAuditType();
            if (StringUtil.isNotEmpty(auditType)){
                auditType = auditType + "," + request.getSignInType().name();
            }else {
                auditType = request.getSignInType().name();
            }
            //查询businessLabel拼接businessLabel的字段
            agentAttendanceMapper.updateByPrimaryKeySelective(AgentAttendance.builder()
                    .id(id)
                    .checkOutTime(request.getSignOutTime())
                    .auditRemark(request.getAuditRemark())
                    .auditType(auditType)
                    .businessLabel(String.join(",", request.getBusinessLabel()))
                    .audit(AttendanceCheckType.TO_BE_REVIEWED.name())
                    .build());
        }
        else {
            AgentAttendance build = AgentAttendance.builder()
                    .employeeCode(RequestContextHolder.getEmployeeCode())
                    .orgType(RequestContextHolder.getOrgType())
                    .orgCode(emp.getOrgCode())
                    .employeeName(emp.getName())
                    .orgName(emp.getOrgName())
                    .topCode(emp.getTopCode())
                    .topName(emp.getTopName())
                    .companyName(merchantName)
                    .companyCode(merchantCode)
                    .locationCode(request.getLocationCode())
                    .locationName(request.getLocationName())
                    .latitude(request.getLatitude())
                    .longitude(request.getLongitude())
                    .signInType(request.getAppointType())
                    .checkOutTime(request.getSignOutTime())
                    .checkInTime(request.getSignInTime())
                    .auditRemark(request.getAuditRemark())
                    .activityLabel(request.getActivityLabel())
                    .updateTime(LocalDateTime.now())
                    .spotCheckType(SpotCheckType.UNTESTED.name())
                    .businessLabel(String.join(",", request.getBusinessLabel()))
                    .auditType(request.getSignInType().name())
                    .audit(AttendanceCheckType.TO_BE_REVIEWED.name())
                    .addressType(request.getAddressType())
                    .build();
            if (null !=employeeOrgVO && (StringUtils.isNotEmpty(employeeOrgVO.getBusinessTeamLeaderCode()))){
                build.setReviewersCode(employeeOrgVO.getBusinessTeamLeaderCode());
            }else if (null !=employeeOrgVO &&
                    (StringUtils.isEmpty(employeeOrgVO.getBusinessTeamLeaderCode()) &&
                            StringUtils.isNotEmpty(employeeOrgVO.getBusinessAreaLeaderCode()))){
                build.setReviewersCode(employeeOrgVO.getBusinessDeptLeaderCode());
            }else if (null !=employeeOrgVO &&
                    (StringUtils.isEmpty(employeeOrgVO.getBusinessTeamLeaderCode()) &&
                            StringUtils.isEmpty(employeeOrgVO.getBusinessAreaLeaderCode()) &&
                            StringUtils.isNotEmpty(employeeOrgVO.getBusinessAreaLeaderCode()))){
                build.setReviewersCode(employeeOrgVO.getBusinessAreaLeaderCode());
            }
            agentAttendanceMapper.insertSelective(build);
            id = build.getId();
        }

        //保存客户信息
        if (CollectionUtils.isNotEmpty(request.getCustomerData())) {
            List<AttendanceCustomer> attendanceCustomers = attendanceCustomerMapper.selectList(new LambdaQueryWrapper<AttendanceCustomer>()
                    .eq(AttendanceCustomer::getAttendanceId, id));
            if (CollectionUtils.isNotEmpty(attendanceCustomers)){
                attendanceCustomerMapper.delete(new LambdaQueryWrapper<AttendanceCustomer>()
                        .eq(AttendanceCustomer::getAttendanceId, id));
            }
            for (com.hqins.agent.postsale.dto.request.AttendanceCustomer customerDatum : request.getCustomerData()) {
                AttendanceCustomer attendanceCustomer = BeanCopier.copyObject(customerDatum, AttendanceCustomer.class);
                attendanceCustomer.setAttendanceId(id);
                attendanceCustomer.setCreatedTime(LocalDateTime.now());
                attendanceCustomer.setUpdatedTime(LocalDateTime.now());
                //前三项为空不存地点
                if (StringUtil.isEmpty(customerDatum.getCustomerName()) ||
                        StringUtil.isEmpty(customerDatum.getCustomerGender()) ||
                        null == customerDatum.getEstimatedPremium()){
                    attendanceCustomer.setOutlets(null);
                    attendanceCustomer.setOutletsCode(null);
                }
                attendanceCustomerMapper.insertSelective(attendanceCustomer);
            }
        }

        //保存活动信息
        if (CollectionUtils.isNotEmpty(request.getActivityData())) {
            List<AttendanceActivity> attendanceCustomers = attendanceActivityMapper.selectList(new LambdaQueryWrapper<AttendanceActivity>()
                    .eq(AttendanceActivity::getAttendanceId, id));
            if (CollectionUtils.isNotEmpty(attendanceCustomers)){
                attendanceActivityMapper.delete(new LambdaQueryWrapper<AttendanceActivity>()
                        .eq(AttendanceActivity::getAttendanceId, id));
            }

            for (com.hqins.agent.postsale.dto.request.AttendanceActivity activityDatum : request.getActivityData()) {
                AttendanceActivity attendanceActivity = BeanCopier.copyObject(activityDatum, AttendanceActivity.class);
                attendanceActivity.setAttendanceId(id);
                attendanceActivity.setActivityOutletsCode(activityDatum.getActivityOutletsCode());
                attendanceActivityMapper.insertSelective(attendanceActivity);
            }
        }

        //保存影像
        if (CollectionUtils.isNotEmpty(request.getImageUrls())) {
            agentImageMapper.delete(new LambdaQueryWrapper<AgentImage>()
                    .eq(AgentImage::getImageType, ImageType.APPOINT_IMG.name())
                    .eq(AgentImage::getImageKey, id.toString()));
            for (String info : request.getImageUrls()) {
                final AgentImage build = AgentImage.builder()
                        .employeeCode(RequestContextHolder.getEmployeeCode())
                        .agentId(RequestContextHolder.getAgentId().toString())
                        .imageUrl(info)
                        .imageType(ImageType.APPOINT_IMG.name())
                        .imageKey(id.toString())
                        .build();
                agentImageMapper.insertSelective(build);
            }
        }


        if (request.getOrgTypeStr().equals("BG")  && !RequestContextHolder.getEmployeeCode().startsWith("S") ){
            //没有上级的情况
            RepairLeaveApproval repBuilder = new RepairLeaveApproval();
            boolean lzFlag = null == employeeOrgVO ||
                    (StringUtils.isEmpty(employeeOrgVO.getBusinessDeptLeaderCode()) &&
                            StringUtils.isEmpty(employeeOrgVO.getBusinessAreaLeaderCode()) &&
                            StringUtils.isEmpty(employeeOrgVO.getBusinessTeamLeaderCode()));

            if (!lzFlag) {
                //判断申请人角色
                boolean isAreaLeader1 = RequestContextHolder.getEmployeeCode().equals(employeeOrgVO.getBusinessAreaLeaderCode());
                boolean isDeptLeader1 = RequestContextHolder.getEmployeeCode().equals(employeeOrgVO.getBusinessDeptLeaderCode());
                boolean isTeamLeader1 = RequestContextHolder.getEmployeeCode().equals(employeeOrgVO.getBusinessTeamLeaderCode());

                //申请人是区长，直接提交量子
                if (isAreaLeader1) {
                    attendanceSettingService.applicationForm(id, LeaveApplyType.APPOINT);
                    return 0;
                }
                //申请人是部长，没有区长
                if (isDeptLeader1 && org.apache.commons.lang3.StringUtils.isEmpty(employeeOrgVO.getBusinessAreaLeaderCode())) {
                    attendanceSettingService.applicationForm(id, LeaveApplyType.APPOINT);
                    return 0;
                }
                //申请人是组长，没有区长部长
                if (isTeamLeader1 && org.apache.commons.lang3.StringUtils.isEmpty(employeeOrgVO.getBusinessAreaLeaderCode())
                        && org.apache.commons.lang3.StringUtils.isEmpty(employeeOrgVO.getBusinessDeptLeaderCode())) {
                    attendanceSettingService.applicationForm(id, LeaveApplyType.APPOINT);
                    return 0;
                }

                //有组长的情况
                if (StringUtils.isNotEmpty(employeeOrgVO.getBusinessTeamLeaderCode()) && !isTeamLeader1) {
                    repBuilder = RepairLeaveApproval.builder()
                            .applicationType(LeaveApplyType.APPOINT.name())
                            .leaveType(AttendanceCheckType.TO_BE_REVIEWED.name())
                            .leaveCode(RequestContextHolder.getEmployeeCode())
                            .approvalCode(employeeOrgVO.getBusinessTeamLeaderCode())
                            .createdTime(LocalDateTime.now())
                            .createdUser(1)
                            .sub(Boolean.TRUE)
                            .build();
                    attendanceSettingService.messageSaveByLeader(employeeOrgVO.getBusinessTeamLeaderCode(), MessageContentType.LEADER_APPOINT_COMMIT, emp.getName());
                } else
                    //有部长
                    if (StringUtils.isNotEmpty(employeeOrgVO.getBusinessDeptLeaderCode()) && !isDeptLeader1) {
                        repBuilder = RepairLeaveApproval.builder()
                                .applicationType(LeaveApplyType.APPOINT.name())
                                .leaveType(AttendanceCheckType.TO_BE_REVIEWED.name())
                                .leaveCode(RequestContextHolder.getEmployeeCode())
                                .approvalCode(employeeOrgVO.getBusinessDeptLeaderCode())
                                .createdTime(LocalDateTime.now())
                                .createdUser(1)
                                .sub(Boolean.TRUE)
                                .build();
                        attendanceSettingService.messageSaveByLeader(employeeOrgVO.getBusinessDeptLeaderCode(), MessageContentType.LEADER_APPOINT_COMMIT, emp.getName());

                    } else
                        //有区长
                        if (StringUtils.isNotEmpty(employeeOrgVO.getBusinessAreaLeaderCode())) {
                            repBuilder = RepairLeaveApproval.builder()
                                    .applicationType(LeaveApplyType.APPOINT.name())
                                    .leaveType(AttendanceCheckType.TO_BE_REVIEWED.name())
                                    .leaveCode(RequestContextHolder.getEmployeeCode())
                                    .approvalCode(employeeOrgVO.getBusinessAreaLeaderCode())
                                    .createdTime(LocalDateTime.now())
                                    .createdUser(1)
                                    .sub(Boolean.TRUE)
                                    .build();
                            attendanceSettingService.messageSaveByLeader(employeeOrgVO.getBusinessAreaLeaderCode(), MessageContentType.LEADER_APPOINT_COMMIT, emp.getName());
                        }
            }
            //调用量子
            if ("BG".equals(request.getOrgTypeStr())  && !RequestContextHolder.getEmployeeCode().startsWith("S")){
                if (lzFlag){
                    attendanceSettingService.applicationForm(id,LeaveApplyType.APPOINT);
                }else{
                    repBuilder.setLeaveId(id);
                    repairLeaveApprovalMapper.insertSelective(repBuilder);
                }
            }
        }

        //保存日志
        logData(JsonUtil.toJSON(request), RequestContextHolder.getEmployeeCode(), AttendanceNodeType.APPOINT, Boolean.TRUE,"");
        return 0;
    }

    @Override
    @Transactional
    public void supplementBranch(List<ReSignRequest> request) {
        final EmployeeVO emp = employeeApi.getEmployeeByEmployeeCode(AgentOrgType.valueOf(RequestContextHolder.getOrgType())
                , RequestContextHolder.getEmployeeCode());

        if (PermissionType.P00001.name().equals(emp.getTopCode())){
            AttendanceLeaveCheck attendanceLeaveCheck = attendanceLeaveCheckMapper.selectOne(Wrappers.lambdaQuery(AttendanceLeaveCheck.class)
                    .eq(AttendanceLeaveCheck::getDeleted, Boolean.FALSE)
                    .last(" and FIND_IN_SET('" + emp.getCode() + "', attendance_codes)>0  limit 1"));
            if (attendanceLeaveCheck != null){
                throw new BadRequestException("当前功能已禁用，有问题请联系机构内勤");
            }
        }
        request.forEach(info -> {
            replacementCheck(info.getSignInTime().withSecond(0),
                    null == info.getSignOutTime() ? null : info.getSignOutTime().withSecond(0),
                    info.getSignInType(),
                    emp.getOrgCode(),
                    emp.getTopCode(),
                    info.getId(),
                    info.getLeader());
            //查询归属渠道商
            String merchantName = "";
            String merchantCode = "";
            if (info.getAppointType().equals(AppointType.APPOINT.name())) {
                AttendanceAddress attendanceAddress = new LambdaQueryChainWrapper<>(attendanceAddressMapper)
                        .eq(AttendanceAddress::getMerchantOrgCode, info.getLocationCode())
                        .last("limit 1")
                        .one();
                if (null != attendanceAddress) {
                    merchantName = attendanceAddress.getMerchantName();
                    merchantCode = attendanceAddress.getMerchantCode();
                }
            }

            //数据填充
            EmployeeOrgVO employeeOrgVO = employeeOrgApi.queryEmployeeOrgInfo(RequestContextHolder.getEmployeeCode());
            Long id = info.getId();
            if (SignInType.APPOINT_HALF.equals(info.getSignInType())
                    && !info.getOrgTypeStr().equals("BG")
            ) {
                //查询auditType拼接auditType的字段
                AgentAttendance agentAttendance = agentAttendanceMapper.selectById(id);
                String auditType = agentAttendance.getAuditType();
                if (StringUtil.isNotEmpty(auditType)) {
                    auditType = auditType + "," + info.getSignInType().name();
                } else {
                    auditType = info.getSignInType().name();
                }
                //查询businessLabel拼接businessLabel的字段
                agentAttendanceMapper.updateByPrimaryKeySelective(AgentAttendance.builder()
                        .id(id)
                        .checkOutTime(info.getSignOutTime())
                        .auditRemark(info.getAuditRemark())
                        .auditType(auditType)
                        .businessLabel(String.join(",", info.getBusinessLabel()))
                        .audit(AttendanceCheckType.TO_BE_REVIEWED.name())
                        .build());
            } else {
                AgentAttendance build = AgentAttendance.builder()
                        .employeeCode(RequestContextHolder.getEmployeeCode())
                        .orgType(RequestContextHolder.getOrgType())
                        .orgCode(emp.getOrgCode())
                        .employeeName(emp.getName())
                        .orgName(emp.getOrgName())
                        .topCode(emp.getTopCode())
                        .topName(emp.getTopName())
                        .companyName(merchantName)
                        .companyCode(merchantCode)
                        .locationCode(info.getLocationCode())
                        .locationName(info.getLocationName())
                        .latitude(info.getLatitude())
                        .longitude(info.getLongitude())
                        .signInType(info.getAppointType())
                        .checkOutTime(info.getSignOutTime())
                        .checkInTime(info.getSignInTime())
                        .auditRemark(info.getAuditRemark())
                        .activityLabel(info.getActivityLabel())
                        .updateTime(LocalDateTime.now())
                        .spotCheckType(SpotCheckType.UNTESTED.name())
                        .businessLabel(String.join(",", info.getBusinessLabel()))
                        .auditType(info.getSignInType().name())
                        .audit(AttendanceCheckType.TO_BE_REVIEWED.name())
                        .addressType(info.getAddressType())
                        .build();
                if (null != employeeOrgVO && (StringUtils.isNotEmpty(employeeOrgVO.getBusinessTeamLeaderCode()))) {
                    build.setReviewersCode(employeeOrgVO.getBusinessTeamLeaderCode());
                } else if (null != employeeOrgVO &&
                        (StringUtils.isEmpty(employeeOrgVO.getBusinessTeamLeaderCode()) &&
                                StringUtils.isNotEmpty(employeeOrgVO.getBusinessAreaLeaderCode()))) {
                    build.setReviewersCode(employeeOrgVO.getBusinessDeptLeaderCode());
                } else if (null != employeeOrgVO &&
                        (StringUtils.isEmpty(employeeOrgVO.getBusinessTeamLeaderCode()) &&
                                StringUtils.isEmpty(employeeOrgVO.getBusinessAreaLeaderCode()) &&
                                StringUtils.isNotEmpty(employeeOrgVO.getBusinessAreaLeaderCode()))) {
                    build.setReviewersCode(employeeOrgVO.getBusinessAreaLeaderCode());
                }
                agentAttendanceMapper.insertSelective(build);
                id = build.getId();

            }

            //保存客户信息
            if (CollectionUtils.isNotEmpty(info.getCustomerData())) {
                List<AttendanceCustomer> attendanceCustomers = attendanceCustomerMapper.selectList(new LambdaQueryWrapper<AttendanceCustomer>()
                        .eq(AttendanceCustomer::getAttendanceId, id));
                if (CollectionUtils.isNotEmpty(attendanceCustomers)) {
                    attendanceCustomerMapper.delete(new LambdaQueryWrapper<AttendanceCustomer>()
                            .eq(AttendanceCustomer::getAttendanceId, id));
                }
                for (com.hqins.agent.postsale.dto.request.AttendanceCustomer customerDatum : info.getCustomerData()) {
                    AttendanceCustomer attendanceCustomer = BeanCopier.copyObject(customerDatum, AttendanceCustomer.class);
                    attendanceCustomer.setAttendanceId(id);
                    attendanceCustomer.setCreatedTime(LocalDateTime.now());
                    attendanceCustomer.setUpdatedTime(LocalDateTime.now());
                    //前三项为空不存地点
                    if (StringUtil.isEmpty(customerDatum.getCustomerName()) ||
                            StringUtil.isEmpty(customerDatum.getCustomerGender()) ||
                            null == customerDatum.getEstimatedPremium()) {
                        attendanceCustomer.setOutlets(null);
                        attendanceCustomer.setOutletsCode(null);
                    }
                    attendanceCustomerMapper.insertSelective(attendanceCustomer);
                }
            }

            //保存活动信息
            if (CollectionUtils.isNotEmpty(info.getActivityData())) {
                List<AttendanceActivity> attendanceCustomers = attendanceActivityMapper.selectList(new LambdaQueryWrapper<AttendanceActivity>()
                        .eq(AttendanceActivity::getAttendanceId, id));
                if (CollectionUtils.isNotEmpty(attendanceCustomers)) {
                    attendanceActivityMapper.delete(new LambdaQueryWrapper<AttendanceActivity>()
                            .eq(AttendanceActivity::getAttendanceId, id));
                }

                for (com.hqins.agent.postsale.dto.request.AttendanceActivity activityDatum : info.getActivityData()) {
                    AttendanceActivity attendanceActivity = BeanCopier.copyObject(activityDatum, AttendanceActivity.class);
                    attendanceActivity.setAttendanceId(id);
                    attendanceActivity.setActivityOutletsCode(activityDatum.getActivityOutletsCode());
                    attendanceActivityMapper.insertSelective(attendanceActivity);
                }
            }

            //保存影像
            if (CollectionUtils.isNotEmpty(info.getImageUrls())) {
                agentImageMapper.delete(new LambdaQueryWrapper<AgentImage>()
                        .eq(AgentImage::getImageType, ImageType.APPOINT_IMG.name())
                        .eq(AgentImage::getImageKey, id.toString()));
                for (String infos : info.getImageUrls()) {
                    final AgentImage build = AgentImage.builder()
                            .employeeCode(RequestContextHolder.getEmployeeCode())
                            .agentId(RequestContextHolder.getAgentId().toString())
                            .imageUrl(infos)
                            .imageType(ImageType.APPOINT_IMG.name())
                            .imageKey(id.toString())
                            .build();
                    agentImageMapper.insertSelective(build);
                }
            }

            if (info.getOrgTypeStr().equals("BG") && !RequestContextHolder.getEmployeeCode().startsWith("S")) {
                AgentAttendance agentAttendance = new AgentAttendance();
                agentAttendance.setId(id);
                //没有上级的情况
                RepairLeaveApproval repBuilder = new RepairLeaveApproval();
                boolean lzFlag = null == employeeOrgVO ||
                        (StringUtils.isEmpty(employeeOrgVO.getBusinessDeptLeaderCode()) &&
                                StringUtils.isEmpty(employeeOrgVO.getBusinessAreaLeaderCode()) &&
                                StringUtils.isEmpty(employeeOrgVO.getBusinessTeamLeaderCode()));

                if (!lzFlag) {
                    //判断申请人角色
                    boolean isAreaLeader1 = RequestContextHolder.getEmployeeCode().equals(employeeOrgVO.getBusinessAreaLeaderCode());
                    boolean isDeptLeader1 = RequestContextHolder.getEmployeeCode().equals(employeeOrgVO.getBusinessDeptLeaderCode());
                    boolean isTeamLeader1 = RequestContextHolder.getEmployeeCode().equals(employeeOrgVO.getBusinessTeamLeaderCode());

                    //申请人是区长，直接提交量子
                    if (isAreaLeader1) {
                        attendanceSettingService.applicationForm(id, LeaveApplyType.APPOINT);
                        return;
                    }
                    //申请人是部长，没有区长
                    if (isDeptLeader1 && org.apache.commons.lang3.StringUtils.isEmpty(employeeOrgVO.getBusinessAreaLeaderCode())) {
                        attendanceSettingService.applicationForm(id, LeaveApplyType.APPOINT);
                        return;
                    }
                    //申请人是组长，没有区长部长
                    if (isTeamLeader1 && org.apache.commons.lang3.StringUtils.isEmpty(employeeOrgVO.getBusinessAreaLeaderCode())
                            && org.apache.commons.lang3.StringUtils.isEmpty(employeeOrgVO.getBusinessDeptLeaderCode())) {
                        attendanceSettingService.applicationForm(id, LeaveApplyType.APPOINT);
                        return;
                    }

                    //有组长的情况
                    if (StringUtils.isNotEmpty(employeeOrgVO.getBusinessTeamLeaderCode()) && !isTeamLeader1) {
                        agentAttendance.setAuditor(employeeOrgVO.getBusinessTeamLeaderName());
                        repBuilder = RepairLeaveApproval.builder()
                                .applicationType(LeaveApplyType.APPOINT.name())
                                .leaveType(AttendanceCheckType.TO_BE_REVIEWED.name())
                                .leaveCode(RequestContextHolder.getEmployeeCode())
                                .approvalCode(employeeOrgVO.getBusinessTeamLeaderCode())
                                .createdTime(LocalDateTime.now())
                                .createdUser(1)
                                .sub(Boolean.TRUE)
                                .build();
                        attendanceSettingService.messageSaveByLeader(employeeOrgVO.getBusinessTeamLeaderCode(), MessageContentType.LEADER_APPOINT_COMMIT, emp.getName());
                    } else
                        //有部长
                        if (StringUtils.isNotEmpty(employeeOrgVO.getBusinessDeptLeaderCode()) && !isDeptLeader1) {
                            agentAttendance.setAuditor(employeeOrgVO.getBusinessDeptLeaderName());
                            repBuilder = RepairLeaveApproval.builder()
                                    .applicationType(LeaveApplyType.APPOINT.name())
                                    .leaveType(AttendanceCheckType.TO_BE_REVIEWED.name())
                                    .leaveCode(RequestContextHolder.getEmployeeCode())
                                    .approvalCode(employeeOrgVO.getBusinessDeptLeaderCode())
                                    .createdTime(LocalDateTime.now())
                                    .createdUser(1)
                                    .sub(Boolean.TRUE)
                                    .build();
                            attendanceSettingService.messageSaveByLeader(employeeOrgVO.getBusinessDeptLeaderCode(), MessageContentType.LEADER_APPOINT_COMMIT, emp.getName());

                        } else
                            //有区长
                            if (StringUtils.isNotEmpty(employeeOrgVO.getBusinessAreaLeaderCode())) {
                                agentAttendance.setAuditor(employeeOrgVO.getBusinessAreaLeaderName());
                                repBuilder = RepairLeaveApproval.builder()
                                        .applicationType(LeaveApplyType.APPOINT.name())
                                        .leaveType(AttendanceCheckType.TO_BE_REVIEWED.name())
                                        .leaveCode(RequestContextHolder.getEmployeeCode())
                                        .approvalCode(employeeOrgVO.getBusinessAreaLeaderCode())
                                        .createdTime(LocalDateTime.now())
                                        .createdUser(1)
                                        .sub(Boolean.TRUE)
                                        .build();
                                attendanceSettingService.messageSaveByLeader(employeeOrgVO.getBusinessAreaLeaderCode(), MessageContentType.LEADER_APPOINT_COMMIT, emp.getName());
                            }
                }
                //调用量子
                if ("BG".equals(info.getOrgTypeStr()) && !RequestContextHolder.getEmployeeCode().startsWith("S")) {
                    if (lzFlag) {
                        attendanceSettingService.applicationForm(id, LeaveApplyType.APPOINT);
                    } else {
                        agentAttendanceMapper.updateByPrimaryKeySelective(agentAttendance);
                        repBuilder.setLeaveId(id);
                        repairLeaveApprovalMapper.insertSelective(repBuilder);
                    }
                }
            }
            //保存日志
            logData(JsonUtil.toJSON(request), RequestContextHolder.getEmployeeCode(), AttendanceNodeType.APPOINT, Boolean.TRUE, "");
        });
    }

    @Override
    public Integer querySupplementCount(DataStatisticsRequest request) {
        if (StringUtil.isEmpty(request.getOrgTypeStr())) {
            throw new BadRequestException("组织类型不能为空");
        }
        String employeeCode =  StringUtils.isEmpty(request.getEmployeeCode())? RequestContextHolder.getEmployeeCode() : request.getEmployeeCode();
        String orgType =StringUtils.isEmpty(request.getOrgType()) ? RequestContextHolder.getOrgType():  request.getOrgType();
        List<String> str = agentAttendanceExtMapper.selectCountByReissueACard(employeeCode, request.getYears(), request.getMonth());

        if ("BG".equals(request.getOrgTypeStr())) {
            String lastDayOfMonth = getLastDayOfMonth(String.valueOf(request.getYears()), String.format("%02d", request.getMonth()), String.format("%02d",request.getDay()));
            String date = request.getYears() +"-"+ String.format("%02d", request.getMonth())+"-" + lastDayOfMonth + " 23:59:59";
            final EmployeeVO employeeInfo = employeeApi.getEmployeeInfo(AgentOrgType.valueOf(orgType), employeeCode);
            AgentAttendanceTime twoQuery = agentAttendanceTimeMapper.selectOne(new LambdaQueryWrapper<AgentAttendanceTime>()
                    .eq(AgentAttendanceTime::getOrgIns, employeeInfo.getOrgCode())
                    .le(AgentAttendanceTime::getYears, request.getYears())
                    .le(AgentAttendanceTime::getCreatedTime,date)
                    .le(AgentAttendanceTime::getCreatedTime,attendanceConfig.getYinBaoStartTime())
                    .orderByDesc(AgentAttendanceTime::getCreatedTime)
                    .last("limit 1"));
            final int agentRepairCount = twoQuery == null ? 10 : twoQuery.getAgentRepairCount();
            final int leaderRepairCount = twoQuery == null ? 10 : twoQuery.getLeaderRepairCount();

            final List<String> collect = str.stream()
                    .filter(info -> info.contains("APPOINT_HALF"))
                    .collect(Collectors.toList());
            int count = agentRepairCount - collect.size();
            final Boolean aBoolean = checkLeader(employeeCode);
            if (aBoolean) {
                count = leaderRepairCount - collect.size();
            }
            return Math.max(count, 0);
        } else {
            /*银保 = BG 团险 = GI  个险 = PI*/
            int i = attendanceSignInImageConfig.getGi();
            //计算剩余补签次数，半天-1天 全天-2天 0次不可补签
            for (String s : str) {
                if (s.contains(SignInType.APPOINT_HALF.name())) {
                    //计算APPOINT_HALF在s中出现的次数
                    int count = StringUtils.countMatches(s, SignInType.APPOINT_HALF.name());
                    i = i - count;
                } else if (s.contains(SignInType.APPOINT_ALL.name())) {
                    i = i - 2;
                }
            }
            return Math.max(i, 0);
        }
    }

    public static String getLastDayOfMonth(String year, String month, String day) {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.YEAR, Integer.parseInt(year));
        calendar.set(Calendar.MONTH, Integer.parseInt(month) - 1); // 月份是从0开始的，所以要减1
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
        return String.valueOf(calendar.get(Calendar.DAY_OF_MONTH));
    }

    @Override
    public LogInfoResponse customerQuery(CustomerQueryRequest request) {
        //查询业务标签
        AgentAttendance agentAttendance = agentAttendanceMapper.selectById(request.getId());
        //查询客户信息
        List<AttendanceCustomer> customers = agentAttendanceExtMapper.selectAttCust(request.getId());
        //查询活动信息
        List<AttendanceActivity> activities = agentAttendanceExtMapper.selectAttAct(request.getId());

        LogInfoResponse data = new LogInfoResponse();
        if (null != agentAttendance && StringUtil.isNotEmpty(agentAttendance.getBusinessLabel())){
            data.setBusinessLabel(Arrays.asList(agentAttendance.getBusinessLabel().split(",")));
            if (StringUtil.isNotEmpty(agentAttendance.getActivityLabel())){
                data.setActivityLabel(agentAttendance.getActivityLabel());
            }
        }
        if(CollectionUtils.isNotEmpty(customers)){
            data.setCustomerList(customers);
        }

        if(CollectionUtils.isNotEmpty(activities)){
            data.setActivityList(activities);
        }
        return data;
    }

    @Override
    public void customerUpdate(LogInfoUpdateRequest request) {
        if (CollectionUtils.isNotEmpty(request.getCustomerData())) {
            attendanceCustomerMapper.delete(Wrappers.lambdaQuery(AttendanceCustomer.class)
                    .eq(AttendanceCustomer::getAttendanceId,request.getId()));
            request.getCustomerData().forEach(info -> {
                AttendanceCustomer attendanceCustomer = BeanCopier.copyObject(info, AttendanceCustomer.class);
                //先删除后插入
                attendanceCustomer.setCreatedTime(LocalDateTime.now());
                attendanceCustomer.setUpdatedTime(LocalDateTime.now());
                attendanceCustomer.setAttendanceId(request.getId());
                //前三项为空不存地点
                if (StringUtil.isEmpty(info.getCustomerName()) ||
                        StringUtil.isEmpty(info.getCustomerGender()) ||
                        null == info.getEstimatedPremium()){
                    attendanceCustomer.setOutlets(null);
                    attendanceCustomer.setOutletsCode(null);
                }
                attendanceCustomerMapper.insertSelective(attendanceCustomer);
            });
        }

        //保存活动信息
        if (CollectionUtils.isNotEmpty(request.getActivityData())) {
            attendanceActivityMapper.delete(Wrappers.lambdaQuery(AttendanceActivity.class).eq(AttendanceActivity::getAttendanceId,request.getId()));
            request.getActivityData().forEach(info -> {
                AttendanceActivity attendanceActivity = BeanCopier.copyObject(info, AttendanceActivity.class);
                attendanceActivity.setAttendanceId(request.getId());
                attendanceActivityMapper.insertSelective(attendanceActivity);
            });
        }
    }

    @Override
    public AttendanceListResponse getDaySing(String orgTypeStr,String orgCode) {
        LocalDateTime now = LocalDateTime.now();
        String employeeCode = RequestContextHolder.getEmployeeCode();
        AttendanceListResponse response = new AttendanceListResponse();
        now = now.withHour(23).withMinute(59).withSecond(59);
        response.setNewTime(attendanceConfig.getYinBaoStartTime());
        //查询签到规则数据
        AgentAttendanceTime agentAttendanceTime = agentAttendanceTimeMapper.selectOne(new LambdaQueryWrapper<AgentAttendanceTime>()
                .eq(AgentAttendanceTime::getOrgIns, orgCode)
//                .eq(AgentAttendanceTime::getYears, now.getYear())
                .le(AgentAttendanceTime::getYears,now.getYear())
                .le(AgentAttendanceTime::getCreatedTime,attendanceConfig.getYinBaoStartTime())
                .orderByDesc(AgentAttendanceTime::getYears)
                .orderByDesc(AgentAttendanceTime::getCreatedTime)
//                .le(AgentAttendanceTime::getCreatedTime, now)
//                .orderByDesc(AgentAttendanceTime::getCreatedTime)
                .last("limit 1"));

        if (null != agentAttendanceTime){
            response.setMorningStipulateCheckInTime(agentAttendanceTime.getMStartTime()+"-"+agentAttendanceTime.getMEndTime());
            response.setAfterStipulateCheckInTime(agentAttendanceTime.getAStartTime()+"-"+agentAttendanceTime.getAEndTime());
        }

        //查询当前用户当月的签到数据
        now = now.withHour(0).withMinute(0).withSecond(0);
        List<AgentAttendance> agentAttendances = agentAttendanceExtMapper.selectDaySingData(now, employeeCode);
        //银保BG  团险GI  个险PI
        if (orgTypeStr.equals("BG") && CollectionUtils.isNotEmpty(agentAttendances)) {
            //要最早的一条数据和最晚的一条数据
            List<AgentAttendance> agentAttendancesCopy = new ArrayList<>();
            AgentAttendance agentAttendance = agentAttendances.stream().min(Comparator.comparing(AgentAttendance::getCheckInTime)).get();
            agentAttendancesCopy.add(agentAttendance);
            if (agentAttendances.size()>1){
                AgentAttendance agentAttendance1 = agentAttendances.stream().max(Comparator.comparing(AgentAttendance::getCheckInTime)).get();
                agentAttendancesCopy.add(agentAttendance1);
            }
            agentAttendances = agentAttendancesCopy;
        }
        if (CollectionUtils.isEmpty(agentAttendances)){
            return response;
        }
        List<AttendanceListResponse.ResponseList> attendanceListResponses = StreamEx.of(agentAttendances).map(agentAttendance -> BeanCopier.copyObject(agentAttendance, AttendanceListResponse.ResponseList.class)).toList();
        //查询指定地点
        final List<String> collect = attendanceListResponses.stream().map(AttendanceListResponse.ResponseList::getLocationCode).collect(Collectors.toList());
        final List<AttendanceAddress> list = new LambdaQueryChainWrapper<>(attendanceAddressMapper)
                .in(AttendanceAddress::getMerchantOrgCode, collect)
                .list();
        //设置经纬度
        if (CollectionUtils.isNotEmpty(list)){
            Map<String, AttendanceAddress> collect1 = list.stream().collect(Collectors.toMap(AttendanceAddress::getMerchantOrgCode, Function.identity()));
            attendanceListResponses.forEach(info ->{
                AttendanceAddress attendanceAddress = collect1.get(info.getLocationCode());
                if (attendanceAddress == null){
                    return;
                }
                info.setTargetLatitude(attendanceAddress.getLatitude());
                info.setTargetLongitude(attendanceAddress.getLongitude());
                info.setCompanyInsCode(attendanceAddress.getCompanyInstCode());
            });
        }
        response.setResponseListList(attendanceListResponses);
        return response;
    }

    @Override
    public void checkTime(String id,String companyInsCode,String orgTypeStr) {
        //校验签退时间与签到间隔是否符合配置时间间隔
        final LocalDateTime now = LocalDateTime.now();

        //签到时间是否和请假数据重合
        LocalDateTime std = LocalDateTime.now().withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0);
        LocalDateTime end = LocalDateTime.now().with(TemporalAdjusters.lastDayOfMonth()).withHour(23).withMinute(59).withSecond(59);

        List<LeaveApplications> agentLeaves = leaveApplicationsMapper.selectList(new LambdaQueryWrapper<LeaveApplications>()
                .eq(LeaveApplications::getEmployeeCode, RequestContextHolder.getEmployeeCode())
                .eq(LeaveApplications::getOrgType, RequestContextHolder.getOrgType())
                .in(LeaveApplications::getAudit, "PASS_THE_AUDIT", "TO_BE_REVIEWED", "UNDER_REVIEW", "AUTO_BYPASS")
                .ge(LeaveApplications::getBeginTime, std.withNano(0))
                .le(LeaveApplications::getEndTime, end.withNano(0))
                .eq(LeaveApplications::getRevoked,Boolean.FALSE)
                .orderByDesc(LeaveApplications::getBeginTime));
        if (CollectionUtils.isNotEmpty(agentLeaves)){
            agentLeaves.forEach(info ->{
                final LocalDateTime beginTime = info.getBeginTime();
                final LocalDateTime endTime = info.getEndTime();
                //判断signInTimeReq不在请假时间内
                if(LocalDateTime.now().isAfter(beginTime) && LocalDateTime.now().isBefore(endTime)){
                    throw new BadRequestException("当前时间段内"+info.getBeginTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))+"~"+info.getEndTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))+"有请假数据，暂无法操作~");
                }
            });
        }

        AgentAttendanceTime agentAttendanceTime = agentAttendanceTimeMapper.selectOne(new LambdaQueryWrapper<AgentAttendanceTime>()
                .eq(AgentAttendanceTime::getOrgIns, companyInsCode)
                .le(AgentAttendanceTime::getYears, String.valueOf(LocalDate.now().getYear()))
                .le("BG".equals(orgTypeStr),AgentAttendanceTime::getCreatedTime,attendanceConfig.getYinBaoStartTime())
                .orderByDesc(AgentAttendanceTime::getCreatedTime)
                .last("limit 1"));

        if (null == agentAttendanceTime || !agentAttendanceTime.getPermissions()) {
            return;
        }

        if ("BG".equals(orgTypeStr)) {
            return;
        }

        //EARLY 早 / LATE 晚
        final String signInTime = agentAttendanceTime.getSignInTime();
        final String signInType = agentAttendanceTime.getSignInType();

        final String signOutTime = agentAttendanceTime.getSignOutTime();
        final String signOutType = agentAttendanceTime.getSignOutType();

        String signInTimeStr = signInTime.substring(0, signInTime.lastIndexOf(":"));
        String signOutTimeStr = signOutTime.substring(0, signOutTime.lastIndexOf(":"));


        if ("EARLY".equals(signInType) && null == id) {
            if (now.toLocalTime().isAfter(LocalTime.parse(signInTime, DateTimeFormatter.ofPattern("HH:mm:ss")))) {
                throw new BadRequestException("机构规定需在" + signInTimeStr + "前签到，暂无法操作~");
            }
        }

        if ("LATE".equals(signInType) && null == id) {
            if (now.toLocalTime().isBefore(LocalTime.parse(signInTime, DateTimeFormatter.ofPattern("HH:mm:ss")))) {
                throw new BadRequestException("机构规定需在" + signInTimeStr + "后签到，暂无法操作~");
            }
            if (now.toLocalTime().isAfter(LocalTime.parse(signOutTime, DateTimeFormatter.ofPattern("HH:mm:ss")))) {
                throw new BadRequestException("机构规定需在" + signInTimeStr + "~" + signOutTimeStr + "之间签到，暂无法提作~");
            }
        }

        if ("EARLY".equals(signOutType) && null != id) {
            if (now.toLocalTime().isAfter(LocalTime.parse(signOutTime, DateTimeFormatter.ofPattern("HH:mm:ss")))) {
                throw new BadRequestException("机构规定需在" + signOutTimeStr + "前签退，暂无法操作~");
            }
        }

        if ("LATE".equals(signOutType) && null != id) {
            if (now.toLocalTime().isBefore(LocalTime.parse(signOutTime, DateTimeFormatter.ofPattern("HH:mm:ss")))) {
                throw new BadRequestException("机构规定需在" + signOutTimeStr + "后签退，暂无法操作~");
            }
        }

        //签退校验
        if (StringUtil.isNotEmpty(id)) {
            String param = new String(Base64.getDecoder().decode(id));
            AgentAttendance agentAttendance = agentAttendanceMapper.selectById(param);
            if (agentAttendance == null) {
                throw new BadRequestException("签到数据不存在");
            }
            if (agentAttendance.getCheckOutTime() != null) {
                throw new BadRequestException("已签退");
            }

            if (null != agentAttendanceTime.getTimeMinInterval()) {
                LocalDateTime checkInTime = agentAttendance.getCheckInTime();
                if (null != checkInTime) {
                    long between = Duration.between(checkInTime, LocalDateTime.now()).toMinutes();
                    if (between < agentAttendanceTime.getTimeMinInterval()) {
                        throw new BadRequestException("签退必须与签到间隔" + agentAttendanceTime.getTimeMinInterval() + "分钟以上哦~");
                    }
                }
            }
        }
    }

    @Override
    public void jobDate(CheckOutRequest request) {
        if (null == request.getId()){
            log.info("jobDate参数错误{}", request);
            return;
        }
        final EmployeeVO emp = employeeApi.getEmployeeByEmployeeCode(AgentOrgType.valueOf(RequestContextHolder.getOrgType())
                , RequestContextHolder.getEmployeeCode());
        if (emp == null) {
            logData(JsonUtil.toJSON(request), RequestContextHolder.getEmployeeCode(), AttendanceNodeType.SIGN_IN, Boolean.FALSE,"人员信息不存在，请联系管理员");
            throw new BadRequestException("人员信息不存在，请联系管理员");
        }
        if (PermissionType.P00001.name().equals(emp.getTopCode())){
            AttendanceLeaveCheck attendanceLeaveCheck = attendanceLeaveCheckMapper.selectOne(Wrappers.lambdaQuery(AttendanceLeaveCheck.class)
                    .eq(AttendanceLeaveCheck::getDeleted, Boolean.FALSE)
                    .last(" and FIND_IN_SET('" + emp.getCode() + "', attendance_codes)>0  limit 1"));
            if (attendanceLeaveCheck != null){
                throw new BadRequestException("当前功能已禁用，有问题请联系机构内勤");
            }
        }
        //更新签退时间
        String bus = CollectionUtils.isNotEmpty( request.getBusinessLabel())? String.join(",", request.getBusinessLabel()):"";
        int i = agentAttendanceMapper.updateByPrimaryKeySelective(AgentAttendance.builder()
                .id(request.getId())
                .activityLabel(request.getActivityLabel())
                .businessLabel(bus)
                .updateTime(LocalDateTime.now())
                .build());
        if (i != 1)  {
            log.info("jobDate没有签到记录{}", request);
            return;
        }

        //保存客户信息
        if (CollectionUtils.isNotEmpty(request.getCustomerData())) {
            request.getCustomerData().forEach(info -> {
                AttendanceCustomer attendanceCustomer = BeanCopier.copyObject(info, AttendanceCustomer.class);
                attendanceCustomer.setAttendanceId(request.getId());
                attendanceCustomer.setCreatedTime(LocalDateTime.now());
                attendanceCustomer.setUpdatedTime(LocalDateTime.now());
                //前三项为空不存地点
                if (StringUtil.isEmpty(info.getCustomerName()) ||
                        StringUtil.isEmpty(info.getCustomerGender()) ||
                        null == info.getEstimatedPremium()){
                    attendanceCustomer.setOutlets(null);
                    attendanceCustomer.setOutletsCode(null);
                }
                attendanceCustomerMapper.insertSelective(attendanceCustomer);
            });
        }

        //保存活动信息
        if (CollectionUtils.isNotEmpty(request.getActivityData())) {
            request.getActivityData().forEach(info -> {
                AttendanceActivity attendanceActivity = BeanCopier.copyObject(info, AttendanceActivity.class);
                attendanceActivity.setAttendanceId(request.getId());
                attendanceActivity.setActivityOutletsCode(info.getActivityOutletsCode());
                attendanceActivityMapper.insertSelective(attendanceActivity);
            });
        }
    }

    @Override
    public List<AttendanceImageResponse> temporary(List<AttendanceImageRequest> request) {
        List<FileGetUrlsVO> filLis = new ArrayList<>();
        String accessToken = wechatApi.getWechatAccessToken(WechatAppType.OFFICIAL.getAppCode()).getAccessToken();
        try {
            for (AttendanceImageRequest info : request) {
                String url = "https://api.weixin.qq.com/cgi-bin/media/get?access_token=" + accessToken + "&media_id=" + info.getMediaId();
                URLConnection connection = new URL(url).openConnection();
                log.info("响应内容：{}", connection.getHeaderFields());
                try (InputStream inputStream = connection.getInputStream()) {
                    String base64 = Base64.getEncoder().encodeToString(IOUtils.toByteArray(inputStream));
                    FileGetUrlsVO addWatermark;
                    if (ImageType.APPOINT_IMG.equals(info.getImageType())) {
                        addWatermark = addWatermark(base64, info.getImageType());
                    } else {
                        addWatermark = addWatermark(base64, info.getCompany(), info.getImageType());
                    }
                    filLis.add(addWatermark);
                }
            }
        } catch (Exception e) {
            log.error("异常：{}",e.getMessage(),e);
            logData(JsonUtil.toJSON(request), RequestContextHolder.getEmployeeCode(), AttendanceNodeType.SIGN_IN, Boolean.FALSE,"图片水印处理异常，已将本次签到数据记录，请联系管理员");
            throw new BadRequestException("图片处理异常，已将本次签到数据记录，请联系管理员");
        }

        if (CollectionUtils.isEmpty(filLis)) {
            logData(JsonUtil.toJSON(request), RequestContextHolder.getEmployeeCode(), AttendanceNodeType.SIGN_IN, Boolean.FALSE,"图片处理异常，已将本次签到数据记录，请联系管理员");
            throw new BadRequestException("图片处理异常，已将本次签到数据记录，请联系管理员");
        }

        return StreamEx.of(filLis).map(fileGetUrlsVO -> {
            final FileUrlVO fileUrlVO = fileGetUrlsVO.getFileUrlList().get(0);
            final String substring = fileUrlVO.getOriginalFileName().substring(0, fileUrlVO.getOriginalFileName().indexOf("&"));
            return AttendanceImageResponse.builder()
                    .imgUrl(fileUrlVO.getOuterUrl())
                    .imageType(ImageType.valueOf(substring))
                    .build();
        }).toList();
    }

    @Override
    public void replacementCheck(LocalDateTime signInTimeReq,LocalDateTime signOutTimeReq,SignInType signInTypeReq,
                                 String companyInsCode,String topCode,Long id,Boolean leader) {
        //校验签退时间与签到间隔是否符合配置时间间隔
        LocalDate yinBaoStartDate = attendanceSignInImageConfig.getYinBaoStartTime().toInstant().atZone(ZoneId.of("UTC")).toLocalDate();
        LocalDate firstDay= LocalDate.of(signInTimeReq.getYear(), signInTimeReq.getMonthValue(), 1);
        LambdaQueryWrapper<AgentAttendanceTime> queryWrapper = Wrappers.lambdaQuery(AgentAttendanceTime.class)
                .eq(AgentAttendanceTime::getOrgIns, companyInsCode)
                .le(AgentAttendanceTime::getYears, signInTimeReq.getYear())
                .le(AgentAttendanceTime::getCreatedTime, firstDay.withMonth(firstDay.getMonthValue()).plusMonths(1).minusDays(1) + " 23:59:59");
        if (companyInsCode.contains(PermissionType.P00001.name())){
            queryWrapper.le(AgentAttendanceTime::getCreatedTime, yinBaoStartDate + " 00:00:00");
        }
        AgentAttendanceTime agentAttendanceTime = agentAttendanceTimeMapper.selectOne(queryWrapper
                .orderByDesc(AgentAttendanceTime::getCreatedTime)
                .last("limit 1"));

        if (null == agentAttendanceTime || !agentAttendanceTime.getPermissions()) {
            return;
        }

        if ("P00001".equals(topCode)) {
            if (signInTimeReq.toLocalDate().isAfter(yinBaoStartDate) || signInTimeReq.toLocalDate().isEqual(yinBaoStartDate)) {
                throw new BadRequestException("补卡时间限制在"+yinBaoStartDate+"之前");
            }
            //gt 大于 lt 小于 ge 大于等于 le 小于等于
            // 获取当前月份的第一天
            LocalDate firstDayOfMonth = LocalDate.of(signInTimeReq.getYear(), signInTimeReq.getMonthValue(), 1);
            // 获取当前月份的最后一天
            LocalDate lastDayOfMonth = firstDayOfMonth.withMonth(signInTimeReq.getMonthValue()).plusMonths(1).minusDays(1);
            List<AgentAttendance> agentAttendances = agentAttendanceMapper.selectList(new LambdaQueryWrapper<AgentAttendance>()
//                    .notIn(null != id, AgentAttendance::getId, id)
//                    .notIn(AgentAttendance::getAudit, AttendanceCheckType.AUDIT_FAILURE.name())
                    .eq(AgentAttendance::getEmployeeCode, RequestContextHolder.getEmployeeCode())
                    .eq(AgentAttendance::getAuditType,SignInType.APPOINT_HALF.name())
                    .ge(AgentAttendance::getCheckInTime, firstDayOfMonth + " 00:00:00")
                    .le(AgentAttendance::getCheckInTime, lastDayOfMonth + " 23:59:59")
                    .orderByAsc(AgentAttendance::getId));

            //格式是00：00：00
            final String mStartTime = agentAttendanceTime.getMStartTime();
            final String mEndTime = agentAttendanceTime.getMEndTime();
            final String aStartTime = agentAttendanceTime.getAStartTime();
            final String aEndTime = agentAttendanceTime.getAEndTime();
            //获取signInTimeReq的时分秒
            final Integer agentRepairCount = leader ? agentAttendanceTime.getLeaderRepairCount() : agentAttendanceTime.getAgentRepairCount();
            //筛选剔除审核不通过的数据
            List<AgentAttendance> bkFilterList = agentAttendances.stream().filter(o -> !o.getAudit().equals(AttendanceCheckType.AUDIT_FAILURE.name())).collect(Collectors.toList());
            if (agentRepairCount <= bkFilterList.size()) {
                throw new BadRequestException("补卡次数不足，无法提交");
            }

            //判断是否有相同地打卡时间
            for (AgentAttendance agentAttendance : agentAttendances) {
                final LocalDateTime checkInTime = agentAttendance.getCheckInTime();
                if (checkInTime.equals(signInTimeReq)) {
                    throw new BadRequestException("补卡时间与其他行为存在冲突，请重新调整时间");
                }
            }

            //判断signInTimeReq是否在中午十二点之前
            if (signInTimeReq.getHour() <= 12) {
                if (signInTimeReq.toLocalTime().isBefore(LocalTime.parse(mStartTime, DateTimeFormatter.ofPattern("HH:mm:ss")))
                        || signInTimeReq.toLocalTime().isAfter(LocalTime.parse(mEndTime, DateTimeFormatter.ofPattern("HH:mm:ss")))) {
                    throw new BadRequestException("补卡时间需满足"+mStartTime+" ~ "+mEndTime);
                }
            }

            //判断signInTimeReq是否在中午十二点之后
            if (signInTimeReq.getHour() > 12) {
                if (signInTimeReq.toLocalTime().isBefore(LocalTime.parse(aStartTime, DateTimeFormatter.ofPattern("HH:mm:ss")))
                        || signInTimeReq.toLocalTime().isAfter(LocalTime.parse(aEndTime, DateTimeFormatter.ofPattern("HH:mm:ss")))) {
                    throw new BadRequestException("补卡时间需满足"+aStartTime+" ~ "+aEndTime);
                }
            }

            //判断是否有审核不通过请假
            String time = signInTimeReq.getYear()+"-"+signInTimeReq.getMonthValue()+"-"+signInTimeReq.getDayOfMonth()+" "+"00:00:00";
            List<LeaveApplications> leaveApplications = leaveApplicationsMapper
                    .selectCalendarByMonthEntity(time, RequestContextHolder.getEmployeeCode());
            leaveApplications.forEach(info -> {
                final LocalDateTime beginTime = info.getBeginTime();
                final LocalDateTime endTime = info.getEndTime();
                if(signInTimeReq.isAfter(endTime) && signInTimeReq.isBefore(beginTime)){
                    throw new BadRequestException("补卡时间与其他行为存在冲突，请重新调整时间");

                }
            });
        } else {

            //EARLY 早 / LATE 晚
            final String signInTime = agentAttendanceTime.getSignInTime();
            final String signInType = agentAttendanceTime.getSignInType();

            final String signOutTime = agentAttendanceTime.getSignOutTime();
            final String signOutType = agentAttendanceTime.getSignOutType();

            String signInTimeStr = signInTime.substring(0, signInTime.lastIndexOf(":"));
            String signOutTimeStr = signOutTime.substring(0, signOutTime.lastIndexOf(":"));

            if (signInTypeReq.equals(SignInType.APPOINT_ALL) && StringUtil.isNotEmpty(signInTime) && StringUtil.isNotEmpty(signInType) && "EARLY".equals(signInType)) {
                if (signInTimeReq.toLocalTime().isAfter(LocalTime.parse(signInTime, DateTimeFormatter.ofPattern("HH:mm:ss"))) ||
                        signInTimeReq.toLocalTime().equals(LocalTime.parse(signInTime, DateTimeFormatter.ofPattern("HH:mm:ss")))
                ) {
                    throw new BadRequestException("机构规定需在" + signInTimeStr + "前签到，暂无法操作~");
                }
            }

            if (signInTypeReq.equals(SignInType.APPOINT_ALL) && StringUtil.isNotEmpty(signInTime) && StringUtil.isNotEmpty(signInType) && "LATE".equals(signInType)) {
                if (signInTimeReq.toLocalTime().isBefore(LocalTime.parse(signInTime, DateTimeFormatter.ofPattern("HH:mm:ss")))) {
                    throw new BadRequestException("机构规定需在" + signInTimeStr + "后签到，暂无法操作~");
                }
            }

            if (StringUtil.isNotEmpty(signOutTime) && StringUtil.isNotEmpty(signOutType) && "EARLY".equals(signOutType)) {
                if (signOutTimeReq.toLocalTime().isAfter(LocalTime.parse(signOutTime, DateTimeFormatter.ofPattern("HH:mm:ss"))) ||
                        signOutTimeReq.toLocalTime().equals(LocalTime.parse(signOutTime, DateTimeFormatter.ofPattern("HH:mm:ss")))
                ) {
                    throw new BadRequestException("机构规定需在" + signOutTimeStr + "前签退，暂无法操作~");
                }
            }

            if (StringUtil.isNotEmpty(signOutTime) && StringUtil.isNotEmpty(signOutType) && "LATE".equals(signOutType)) {
                if (signOutTimeReq.toLocalTime().isBefore(LocalTime.parse(signOutTime, DateTimeFormatter.ofPattern("HH:mm:ss")))) {
                    throw new BadRequestException("机构规定需在" + signOutTimeStr + "后签退，暂无法操作~");
                }
            }

            if (null != agentAttendanceTime.getTimeMinInterval()) {
                long between = Duration.between(signInTimeReq, signOutTimeReq).toMinutes();
                if (between < agentAttendanceTime.getTimeMinInterval()) {
                    throw new BadRequestException("签退必须与签到间隔" + agentAttendanceTime.getTimeMinInterval() + "分钟以上哦~");
                }
            }
        }

        //签到时间是否和请假数据重合
        LocalDateTime std = signInTimeReq.withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0);
        LocalDateTime end = signInTimeReq.with(TemporalAdjusters.lastDayOfMonth()).withHour(23).withMinute(59).withSecond(59);

        List<LeaveApplications> agentLeaves = leaveApplicationsMapper.selectList(new LambdaQueryWrapper<LeaveApplications>()
                .eq(LeaveApplications::getEmployeeCode, RequestContextHolder.getEmployeeCode())
                .eq(LeaveApplications::getOrgType, RequestContextHolder.getOrgType())
                .in(LeaveApplications::getAudit, "PASS_THE_AUDIT", "TO_BE_REVIEWED", "UNDER_REVIEW", "AUTO_BYPASS")
                .ge(LeaveApplications::getBeginTime, std.withNano(0))
                .le(LeaveApplications::getEndTime, end.withHour(23).withMinute(59).withSecond(59).withNano(0))
                .eq(LeaveApplications::getRevoked,Boolean.FALSE)
                .orderByDesc(LeaveApplications::getBeginTime));
        if (CollectionUtils.isNotEmpty(agentLeaves)){
            agentLeaves.forEach(info ->{
                final LocalDateTime beginTime = info.getBeginTime();
                final LocalDateTime endTime = info.getEndTime();
                //判断signInTimeReq不在请假时间内
                if(signInTimeReq.isBefore(endTime) && signInTimeReq.isAfter(beginTime)){
                    throw new BadRequestException("当前时间段内"+info.getBeginTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))+"~"+info.getEndTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))+"有请假数据，暂无法操作~");
                }
            });
        }
    }

    /**
     * 日志记录
     */
    @Override
    public void logData(String json, String employeeCode, AttendanceNodeType type, boolean success,String content) {

        attendanceLogMapper.insert(AttendanceLog.builder()
                .requestJson(json)
                .employeeCode(employeeCode)
                .nodeType(type.name())
                .success(success)
                .createdId(null == RequestContextHolder.getEmployeeCode() ? employeeCode : RequestContextHolder.getAgentId().toString())
                .createdUser(null == RequestContextHolder.getEmployeeCode() ? employeeCode : RequestContextHolder.getEmployeeCode())
                .updatedUser(null == RequestContextHolder.getEmployeeCode() ? employeeCode : RequestContextHolder.getEmployeeCode())
                .updatedId(null == RequestContextHolder.getEmployeeCode() ? employeeCode : RequestContextHolder.getAgentId().toString())
                .createdTime(LocalDateTime.now())
                .content(content)
                .build());
    }

    /**
     * 生成水印图像
     */
    private FileGetUrlsVO addWatermark(String base64Image, String company, ImageType imageType) throws Exception {
        // 解码base64图像字符串
        byte[] imageBytes = javax.xml.bind.DatatypeConverter.parseBase64Binary(base64Image);
        ByteArrayInputStream stream = new ByteArrayInputStream(imageBytes);

        // 加载图像
        BufferedImage image = ImageIO.read(stream);


        // 获取图像宽度和高度
        int imageWidth = image.getWidth();
        int imageHeight = image.getHeight();

        // 创建一个图形对象以在图像上绘图
        Graphics graphics = image.getGraphics();

        // 设置水印文本的字体和颜色
        Font font;
        if (imageWidth > 2000 && imageHeight > 2000) {
            font = new Font("", Font.BOLD, 100);
        } else {
            font = new Font("微软雅黑", Font.BOLD, 50);
        }
        graphics.setFont(font);
        graphics.setColor(new Color(255,95,31));

        Date date = new Date();

        // 将日期水印文本添加到图像
        String timeText = new SimpleDateFormat(TIME_FORMAT).format(date);
        int stringWidth = graphics.getFontMetrics().stringWidth(timeText);
        graphics.drawString(timeText, imageWidth - stringWidth - X_OFFSET, Y_OFFSET + font.getSize());

        // 重新定义字体大小
        Font font1;
        if (imageWidth > 2000 && imageHeight > 2000) {
            font1 = new Font("微软雅黑", Font.PLAIN, 100);
        } else {
            font1 = new Font("微软雅黑", Font.PLAIN, 50);
        }
        graphics.setFont(font1);

        // 在图像中添加年份和月份水印文本
        String dateText = new SimpleDateFormat(DATE_FORMAT,Locale.CHINA).format(date);
        stringWidth = graphics.getFontMetrics().stringWidth(dateText);
        graphics.drawString(dateText, imageWidth - stringWidth - X_OFFSET, Y_OFFSET + font.getSize() * 2);

        // 将公司名称水印文本添加到图像中
        stringWidth = graphics.getFontMetrics().stringWidth(company);
        // 计算最大允许字符串宽度
        int maxAllowedStringWidth = imageWidth - 2 * X_OFFSET;
        // 如果字符串宽度超过最大允许宽度，缩小字体直到适应
        while (stringWidth > maxAllowedStringWidth) {
            float currentFontSize = font1.getSize2D();
            font1 = font1.deriveFont(currentFontSize - 1);
            graphics.setFont(font1);
            stringWidth = graphics.getFontMetrics().stringWidth(company);
        }
        graphics.drawString(company, imageWidth - stringWidth - X_OFFSET, Y_OFFSET + font.getSize() * 3);

        // 将水印图像转换为字节数组
        ByteArrayOutputStream bStream = new ByteArrayOutputStream();
        ImageIO.write(image, "jpg", bStream);
        byte[] watermarkedImageBytes = bStream.toByteArray();

        // 将水印图像编码为Base64字符串
        String watermarkedBase64Image = javax.xml.bind.DatatypeConverter.printBase64Binary(watermarkedImageBytes);

        // 关闭图形对象
        graphics.dispose();

        return fileApi.upload4Base64(FileBase64Request.builder()
                .source("admin")
                .fileBase64String(watermarkedBase64Image)
                .fileName(imageType + "&" + UUIDUtils.uuid() + ".jpg")
                .build());
    }


    /**
     * 无水印影像
     */
    private FileGetUrlsVO addWatermark(String base64Image,ImageType imageType) {
        return fileApi.upload4Base64(FileBase64Request.builder()
                .source("admin")
                .fileBase64String(base64Image)
                .fileName(imageType + "&" + UUIDUtils.uuid() + ".jpg")
                .build());
    }


}
