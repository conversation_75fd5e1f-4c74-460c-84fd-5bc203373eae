package com.hqins.agent.postsale.dto.request;

import com.hqins.agent.org.model.vo.EmployeeVO;
import com.hqins.agent.postsale.dao.entity.AgentAttendanceTime;
import com.hqins.agent.postsale.dto.response.MonthPunchIn;
import com.hqins.agent.postsale.model.vo.LeaveApplicationsVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("数据查询对象")
public class DataStatisticsRequest implements Serializable {
    @ApiModelProperty("年份")
    private Integer years;

    @ApiModelProperty("月份")
    private Integer month;

    @ApiModelProperty("日")
    private Integer day;

    @ApiModelProperty("代理人工号")
    private String employeeCode;

    private EmployeeVO employeeInfo;

    @ApiModelProperty("归属渠道类型")
    private String orgType;

    @ApiModelProperty("所属合伙人机构类型")
    private String orgTypeStr;

    @ApiModelProperty("所属合伙人机构")
    private String orgCode;

    @ApiModelProperty("所属合伙人顶级机构")
    private String topCode;

//    @ApiModelProperty("打卡配置")
//    AgentAttendanceTime agentAttendanceTime;

    @ApiModelProperty("打卡配置Map,key:日,value:配置,key:0为最新配置")
    Map<Integer,AgentAttendanceTime> agentAttendanceTimeMap;

    @ApiModelProperty("打卡记录列表")
    List<MonthPunchIn> monthPunchInList;

    @ApiModelProperty("请假记录列表")
    List<LeaveApplicationsVO> leaveApplicationsVOList;

    @ApiModelProperty("是否需要返回当月成功打卡地点数，默认返回")
    Boolean isNeedMonthPunchInCount;
}
