package com.hqins.agent.postsale.service;

import com.hqins.agent.postsale.config.AttendanceObjConfig;
import com.hqins.agent.postsale.dao.entity.AgentAttendance;
import com.hqins.agent.postsale.dao.entity.AgentAttendanceTime;
import com.hqins.agent.postsale.dto.enums.AttendanceNodeType;
import com.hqins.agent.postsale.dto.enums.SignInType;
import com.hqins.agent.postsale.dto.request.*;
import com.hqins.agent.postsale.dto.response.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

public interface AttendanceService {
    List<AttendanceImageResponse> imageWatermark(List<AttendanceImageRequest> request);

    DropDownResponse dropDown(String longitude, String latitude);

    Long signIn(AttendanceSignInRequest request);

    List<AgentAttendance> attendanceType(String locationCode);

    SignOutResponse signOut(CheckOutRequest request);

    Map<String, AttendanceObjConfig> advertising();

    DropDownResponse downChild(LocationCodeRequest request);

    DataStatisticsResponse daKaDataStatistics(DataStatisticsRequest request);

    DataStatisticsResponse dataStatistics(DataStatisticsRequest request);

    Integer supplement(ReSignRequest request);

    void supplementBranch(List<ReSignRequest> request);

    Integer querySupplementCount(DataStatisticsRequest request);

    LogInfoResponse customerQuery(CustomerQueryRequest request);

    void customerUpdate(LogInfoUpdateRequest request);

    AttendanceListResponse getDaySing(String teamType, String orgCode);

    void checkTime(String id, String companyInsCode, String orgTypeStr);

    void jobDate(CheckOutRequest request);

    List<AttendanceImageResponse> temporary(List<AttendanceImageRequest> request);

    void logData(String json, String employeeCode, AttendanceNodeType type, boolean success, String content);

     void replacementCheck(LocalDateTime signInTimeReq, LocalDateTime signOutTimeReq, SignInType signInTypeReq,
                                 String companyInsCode, String topCode, Long id, Boolean leader);
}
