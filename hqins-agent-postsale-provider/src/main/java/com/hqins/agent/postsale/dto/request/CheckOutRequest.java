package com.hqins.agent.postsale.dto.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("签退对象")
public class CheckOutRequest implements Serializable {
    @ApiModelProperty("签到ID")
    private Long id;
    @ApiModelProperty("加密ID")
    private String encryptedID;
    @ApiModelProperty("打卡地点代码")
    private String locationCode;
    @ApiModelProperty("打卡地点名称")
    private String locationName;
    @ApiModelProperty("打卡位置(经度)")
    private String  longitude;
    @ApiModelProperty("打卡位置(纬度)")
    private String  latitude;
    @ApiModelProperty("签退时间")
    private LocalDateTime checkOutTime;
    @ApiModelProperty("签退图片")
    private String imageUrl;

    @ApiModelProperty("网点经营")
    private List<String> businessLabel;

    @ApiModelProperty("网点活动")
    private String activityLabel;

    @ApiModelProperty("二级机构")
    private String companyInsCode;

    @ApiModelProperty("客户信息")
    private List<AttendanceCustomer> customerData;

    @ApiModelProperty("活动信息")
    private List<AttendanceActivity> activityData;
}
