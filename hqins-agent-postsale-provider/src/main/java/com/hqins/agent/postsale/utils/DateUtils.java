package com.hqins.agent.postsale.utils;

import com.hqins.common.utils.StringUtil;
import lombok.experimental.UtilityClass;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @since 2023/6/16
 */
@UtilityClass
public class DateUtils {

    public static final String DEFAULT = "yyyy-MM-dd";

    /**
     * 转换"yyyy-MM-dd HH:mm:ss"，为LocalDateTime
     */
    public static LocalDateTime convertLocalDateTime(String dateTimeStr){
        try{
            if (StringUtil.isEmpty(dateTimeStr)){
                return null;
            }
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            return LocalDateTime.parse(dateTimeStr, formatter);
        }catch (Exception e){
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 转换"yyyy-MM-dd"，为LocalDateTime
     */
    public static LocalDate convertLocalDate(String dateStr){
        try{
            if (StringUtil.isEmpty(dateStr)){
                return null;
            }
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            return LocalDate.parse(dateStr, formatter);
        }catch (Exception e){
            e.printStackTrace();
        }
        return null;
    }

    public static String LocalDateToString(LocalDate localDate){
        DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        return localDate.format(df);
    }

    /**
     * 距离当前时间相差几天
     *
     * @param date
     * @return
     */
    public static long getDaysSub(String date) throws ParseException {
        SimpleDateFormat format = new SimpleDateFormat(DEFAULT);
        Date dateString = format.parse(date);
        Date nowDate = getStartOfDay(new Date());
        return (dateString.getTime() - nowDate.getTime()) / (1000 * 60 * 60 * 24);
    }

    /**
     * 获得日期最小时间
     *
     * @param date
     * @return
     */
    public static Date getStartOfDay(Date date) {
        LocalDateTime localDateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(date.getTime()), ZoneId.systemDefault());
        LocalDateTime startOfDay = localDateTime.with(LocalTime.MIN);
        return Date.from(startOfDay.atZone(ZoneId.systemDefault()).toInstant());
    }

    public static List<Integer> getWorkdays(int year, int month) {
        List<Integer> workdays = new ArrayList<>();
        YearMonth yearMonth = YearMonth.of(year, month);
        LocalDate startDate = yearMonth.atDay(1);
        LocalDate endDate = yearMonth.atEndOfMonth();

        LocalDate date = startDate;
        while (!date.isAfter(endDate)) {
            // 检查是否为周一到周五
            if (date.getDayOfWeek() != DayOfWeek.SATURDAY && date.getDayOfWeek() != DayOfWeek.SUNDAY) {
                workdays.add(date.getDayOfMonth());
            }
            date = date.plusDays(1);
        }
        return workdays;
    }

    public static void main(String[] args){
        System.out.println(getWorkdays(2025,4));
    }
}
