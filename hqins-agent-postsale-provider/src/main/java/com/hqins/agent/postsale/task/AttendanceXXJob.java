package com.hqins.agent.postsale.task;

import com.hqins.agent.postsale.service.AttendanceSettingService;
import com.hqins.agent.postsale.service.LeaveApplicationsService;
import com.hqins.agent.postsale.service.UtilsService;
import com.hqins.job.core.context.XxlJobHelper;
import com.hqins.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

@Component
@Slf4j
public class AttendanceXXJob {
    private final AttendanceSettingService attendanceSettingService;
    private final LeaveApplicationsService leaveApplicationsService;
    private final UtilsService utilsService;

    public AttendanceXXJob(AttendanceSettingService attendanceSettingService, LeaveApplicationsService leaveApplicationsService, UtilsService utilsService) {
        this.attendanceSettingService = attendanceSettingService;
        this.leaveApplicationsService = leaveApplicationsService;
        this.utilsService = utilsService;
    }

    /**
     * 打卡地点更新
     */
    @XxlJob("attendanceSettingCheckAddress")
    public void checkAddress() {
        log.info("打卡地点更新任务-----开始");
        attendanceSettingService.checkAddress();
        log.info("打卡地点更新任务-----结束");
    }

    /**
     * 打卡记录抽查
     */
    @XxlJob("attendanceSettingSpotCheck")
    public void spotCheck()  {
        log.info("打卡记录抽查任务-----开始");
        attendanceSettingService.spotCheck();
        log.info("打卡记录抽查任务-----结束");
    }

    /**
     *
     */
    @XxlJob("leaveApplicationsDataOne")
    public void leaveApplicationsDataOne()  {
        log.info("leaveApplicationsDataOne-----开始");
        leaveApplicationsService.jobSupplement();
        log.info("leaveApplicationsDataOne-----结束");
    }

    @XxlJob("leaveApplicationsCompress")
    public void leaveApplicationsCompress()  {
        log.info("leaveApplicationsCompress-----开始");
        leaveApplicationsService.compress();
        log.info("leaveApplicationsCompress-----结束");
    }


    /**
     * 更新所有人员假期剩余情况年底
     */
    @XxlJob("saveAllStaff")
    public void saveAllStaff()  {
        log.info("保存所有人员假期剩余情况-----开始");
        attendanceSettingService.saveAllStaff();
        log.info("保存所有人员假期剩余情况-----结束");
    }

    @XxlJob("updateAllStaff")
    public void updateAllStaff()  {
        log.info("更新所有人员假期过期情况-----开始");
        attendanceSettingService.updateAllStaff();
        log.info("更新所有人员假期过期情况-----结束");
    }

    @XxlJob("missedSignatureMark")
    public void missedSignatureMark()  {
        String param = XxlJobHelper.getJobParam();
        log.info("漏签退定时任务-----开始,{}", param);
        attendanceSettingService.missedSignatureMark(param);
        log.info("漏签退定时任务-----结束");
    }

    @XxlJob("syncHolidayCalendar")
    public void syncHolidayCalendar()  {
        String param = XxlJobHelper.getJobParam();
        if (param == null || param.isEmpty()) {
            //param赋值为当前年的下一年
            param = String.valueOf(LocalDateTime.now().getYear() + 1);
        }
        log.info("同步国家法定节假日-----开始,{}", param);
        utilsService.syncHolidayCalendar(param);
        log.info("同步国家法定节假日-----结束");
    }
}
