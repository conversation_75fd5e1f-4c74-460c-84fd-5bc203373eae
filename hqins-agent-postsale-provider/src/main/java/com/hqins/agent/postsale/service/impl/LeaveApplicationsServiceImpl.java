package com.hqins.agent.postsale.service.impl;

import cn.hutool.core.codec.Base64;
import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hqins.agent.org.model.api.DataAccessApi;
import com.hqins.agent.org.model.api.EmployeeApi;
import com.hqins.agent.org.model.api.EmployeeOrgApi;
import com.hqins.agent.org.model.vo.EmployeeOrgVO;
import com.hqins.agent.org.model.vo.EmployeeVO;
import com.hqins.agent.org.model.vo.MyDataAccessVO;
import com.hqins.agent.postsale.config.AttendanceConfig;
import com.hqins.agent.postsale.dao.entity.*;
import com.hqins.agent.postsale.dao.mapper.*;
import com.hqins.agent.postsale.dto.enums.*;
import com.hqins.agent.postsale.dto.request.DataStatisticsRequest;
import com.hqins.agent.postsale.dto.request.EntryConfig;
import com.hqins.agent.postsale.dto.response.AuditResponse;
import com.hqins.agent.postsale.dto.response.DataStatisticsResponse;
import com.hqins.agent.postsale.dto.response.MeAttendanceAndLeaveResponse;
import com.hqins.agent.postsale.model.enums.LeaveType;
import com.hqins.agent.postsale.model.enums.MessageContentType;
import com.hqins.agent.postsale.model.enums.PermissionType;
import com.hqins.agent.postsale.model.request.*;
import com.hqins.agent.postsale.model.vo.*;
import com.hqins.agent.postsale.service.AttendanceService;
import com.hqins.agent.postsale.service.AttendanceYinBaoService;
import com.hqins.agent.postsale.service.LeaveApplicationsService;
import com.hqins.agent.postsale.utils.PictureCompressionUtil;
import com.hqins.common.base.constants.DatePatterns;
import com.hqins.common.base.enums.AgentOrgType;
import com.hqins.common.base.enums.Gender;
import com.hqins.common.base.errors.ApiException;
import com.hqins.common.base.errors.BadRequestException;
import com.hqins.common.base.page.PageInfo;
import com.hqins.common.base.utils.AssertUtil;
import com.hqins.common.helper.BeanCopier;
import com.hqins.common.utils.JsonUtil;
import com.hqins.common.utils.PageUtil;
import com.hqins.common.web.RequestContextHolder;
import com.hqins.file.service.api.FileApi;
import com.hqins.file.service.model.request.FileBase64Request;
import com.hqins.file.service.model.vo.FileGetUrlsVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.lang.reflect.Method;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 请假记录表业务逻辑实现
 * <AUTHOR>
 * @since 2023-07-14
 */
@Service
@Slf4j
@RefreshScope
public class LeaveApplicationsServiceImpl extends ServiceImpl<LeaveApplicationsMapper, LeaveApplications> implements LeaveApplicationsService {


    private final LeaveApplicationsMapper leaveApplicationsMapper;
    private final EmployeeApi employeeApi;
    private final AgentAttendanceMapper agentAttendanceMapper;
    private final AgentImageMapper agentImageMapper;
    private final DataAccessApi dataAccessApi;
    private final AttendanceSettingServiceImpl attendanceSettingService;
    private final AttendanceCustomerMapper attendanceCustomerMapper;
    private final AttendanceActivityMapper attendanceActivityMapper;
    private final AttendanceLeaveCheckMapper attendanceLeaveCheckMapper;

    private final AttendanceService attendanceService;

    private final LeaveRevokeMapper leaveRevokeMapper;
    private final LeaveCheckConfigMapper leaveCheckConfigMapper;
    private final AgentLeaveRemainingMapper agentLeaveRemainingMapper;
    private final AttendanceConfig attendanceSignInImageConfig;
    private final AttendanceYinBaoService attendanceYinBaoService;

    @Autowired
    private EmployeeOrgApi employeeOrgApi;

    @Autowired
    private RepairLeaveApprovalMapper repairLeaveApprovalMapper;

    @Autowired
    private FileApi fileApi;

    @Value("${applyUrl}")
    private  String applyUrl;

    @Value("${applyUrlNew}")
    private  String applyUrlNew;


    public LeaveApplicationsServiceImpl(LeaveApplicationsMapper leaveApplicationsMapper, EmployeeApi employeeApi, AgentAttendanceMapper agentAttendanceMapper, AgentImageMapper agentImageMapper, DataAccessApi dataAccessApi, AttendanceSettingServiceImpl attendanceSettingService, AttendanceCustomerMapper attendanceCustomerMapper, AttendanceActivityMapper attendanceActivityMapper, AttendanceLeaveCheckMapper attendanceLeaveCheckMapper, AttendanceService attendanceService, LeaveRevokeMapper leaveRevokeMapper, LeaveCheckConfigMapper leaveCheckConfigMapper, AgentLeaveRemainingMapper agentLeaveRemainingMapper, AttendanceConfig attendanceSignInImageConfig, AttendanceYinBaoService attendanceYinBaoService) {
        this.leaveApplicationsMapper = leaveApplicationsMapper;
        this.employeeApi = employeeApi;
        this.agentAttendanceMapper = agentAttendanceMapper;
        this.agentImageMapper = agentImageMapper;
        this.dataAccessApi = dataAccessApi;
        this.attendanceSettingService = attendanceSettingService;
        this.attendanceCustomerMapper = attendanceCustomerMapper;
        this.attendanceActivityMapper = attendanceActivityMapper;
        this.attendanceLeaveCheckMapper = attendanceLeaveCheckMapper;
        this.attendanceService = attendanceService;
        this.leaveRevokeMapper = leaveRevokeMapper;
        this.leaveCheckConfigMapper = leaveCheckConfigMapper;
        this.agentLeaveRemainingMapper = agentLeaveRemainingMapper;
        this.attendanceSignInImageConfig = attendanceSignInImageConfig;
        this.attendanceYinBaoService = attendanceYinBaoService;
    }

    private static final String FLAG = "1";


    @Override
    public Boolean check(String begin, String end, String leaveType,double days) {
        AssertUtil.notNull(begin, new ApiException(400, "请设置开始时间"));
        AssertUtil.notNull(end, new ApiException(400, "请设置结束时间"));
        LocalDateTime beginTime = getBeginTime(begin);
        LocalDateTime endTime = getEndTime(end);
        if (beginTime.isAfter(endTime)) {
            throw new ApiException(400, "开始日期不可晚于结束日期");
        }

        List<String> list = new ArrayList<>();
        list.add(AttendanceCheckType.PASS_THE_AUDIT.name());
        list.add(AttendanceCheckType.TO_BE_REVIEWED.name());
        list.add(AttendanceCheckType.UNDER_REVIEW.name());
        final EmployeeVO emp = employeeApi.getEmployeeByEmployeeCode(AgentOrgType.valueOf(RequestContextHolder.getOrgType())
                , RequestContextHolder.getEmployeeCode());

        if (LeaveType.MATERNITY_LEAVE.name().equals(leaveType) && !emp.getGender().equals(Gender.FEMALE)){
            throw  new BadRequestException("产假仅支持女性提交");
        }
        if (LeaveType.PATERNITY_LEAVE.name().equals(leaveType) && !emp.getGender().equals(Gender.MALE)){
            throw  new BadRequestException("陪产假仅支持男性提交");
        }

        if (LeaveType.SICK_LEAVE.name().equals(leaveType) && (PermissionType.P00003.name().equals(emp.getTopCode()) || PermissionType.P00004.name().equals(emp.getTopCode()))) {

            LocalDate aStartTime = beginTime.toLocalDate().withDayOfMonth(1);
            LocalDate aEndTime = beginTime.toLocalDate().withDayOfMonth(beginTime.toLocalDate().lengthOfMonth());
            if (days > 1) {
                throw new BadRequestException( "最多可申请1天病假~");
            }
            Integer count1 = leaveApplicationsMapper.selectCount(Wrappers.lambdaQuery(LeaveApplications.class)
                    .eq(LeaveApplications::getEmployeeCode, emp.getCode())
                    .eq(LeaveApplications::getLeaveType, LeaveType.SICK_LEAVE.name())
                    .in(LeaveApplications::getAudit, list)
                    .ge(LeaveApplications::getBeginTime, aStartTime)
                    .le(LeaveApplications::getBeginTime, aEndTime)
                    .eq(LeaveApplications::getRevoked,Boolean.FALSE)
            );
            Integer count2 = leaveApplicationsMapper.selectCount(Wrappers.lambdaQuery(LeaveApplications.class)
                    .eq(LeaveApplications::getEmployeeCode, emp.getCode())
                    .eq(LeaveApplications::getLeaveType, LeaveType.SICK_LEAVE.name())
                    .in(LeaveApplications::getAudit, list)
                    .ge(LeaveApplications::getEndTime, aStartTime)
                    .le(LeaveApplications::getEndTime, aEndTime)
                    .eq(LeaveApplications::getRevoked,Boolean.FALSE)
            );
            //判断病假次数
            if ((count1+count2) >= 1) {
                throw new BadRequestException("每月仅可申请1天病假，本月机会已用尽~");
            }
        }else {
            //银保个性化配置 根据请假类型和归属机构查询配置
             LeaveCheckConfig configuration =leaveCheckConfigMapper.selectOne(new LambdaQueryWrapper<LeaveCheckConfig>()
                    .eq(LeaveCheckConfig::getOrgCode, emp.getOrgCode())
                    .eq(LeaveCheckConfig::getLeaveType, leaveType)
                    .eq(LeaveCheckConfig::getYears,LocalDate.now().getYear())
                    .eq(LeaveCheckConfig::getDeleted,Boolean.FALSE)
                    .orderByDesc(LeaveCheckConfig::getCreatedTime)
                    .last("limit 1"));
            if (configuration == null){
                configuration = leaveCheckConfigMapper.selectOne(new LambdaQueryWrapper<LeaveCheckConfig>()
                        .eq(LeaveCheckConfig::getOrgCode, emp.getOrgCode())
                        .eq(LeaveCheckConfig::getLeaveType,leaveType)
                        .lt(LeaveCheckConfig::getYears,LocalDate.now().getYear())
                        .eq(LeaveCheckConfig::getDeleted, Boolean.FALSE)
                        .orderByDesc(LeaveCheckConfig::getYears)
                        .orderByDesc(LeaveCheckConfig::getCreatedTime)
                        .last("limit 1"));
            }

            //如果没查到
            log.info("假期配置:{}", JsonUtil.toJSON(configuration));
            if (configuration != null){
                //入职时间
                LocalDateTime entryTime = emp.getEntryTime();
                long between = ChronoUnit.DAYS.between(entryTime, LocalDateTime.now());
                double betweenYears = (double) between/365;
                //剩余天数
                double residue = 0;
                //发放方式
                String releaseMode = configuration.getReleaseMode();
                //有效期
                Integer validity = configuration.getValidity();
                //看是否是有效期15月  如果是则需要查询结余表
                if (configuration.getValidity() == 15){
                    AgentLeaveRemaining leaveRemaining = agentLeaveRemainingMapper.selectOne(Wrappers.lambdaQuery(AgentLeaveRemaining.class)
                            .eq(AgentLeaveRemaining::getEmployeeCode, emp.getCode())
                            .eq(AgentLeaveRemaining::getYears,LocalDate.now().minusYears(1).getYear())
                            .eq(AgentLeaveRemaining::getLeaveType,leaveType)
                            .last("limit 1"));
                    if (null != leaveRemaining){
                        residue = Double.parseDouble(leaveRemaining.getResidue()) - Double.parseDouble(leaveRemaining.getOverdue());
                    }
                }
                //在查看是固定配额还是司龄配置
                String quotaConfig = configuration.getQuotaConfig();
                if (FixedQuotaType.FIXED_QUOTA.name().equals(quotaConfig)){
                    //固定 然后看是 ANNUALLY("每年") MONTHLY("每月")  SINGLE_COMPLETION("单次休完") SINGLE_LIMIT("单次上限")
                    String daysConfig = configuration.getDaysConfig();
                    //是否折算
                    Boolean converted = configuration.getConverted();
                    //配置天数
                    double configurationDays = Double.parseDouble(configuration.getDays());

                    switchConfigType(leaveType, beginTime, endTime, emp, between, residue, daysConfig, releaseMode, converted, configurationDays,validity,days);
                }else if (FixedQuotaType.SENIORITY.name().equals(quotaConfig)){
                    //司龄
                    List<EntryConfig> entryConfigList = JsonUtil.toList(configuration.getEntryConfig(), List.class, EntryConfig.class);
                    boolean flag = Boolean.FALSE;
                    //最后一个匹配开区间
                    for (int i = 0 ; i < entryConfigList.size() ; i++ ){
                        EntryConfig entryConfig = entryConfigList.get(i);
                        double lessEqual = Double.parseDouble(entryConfig.getLessEqual());
                        double less = Double.parseDouble(entryConfig.getLess());
                        if (i != entryConfigList.size() -1){
                            //说明匹配区间
                            if (lessEqual<= betweenYears && betweenYears < less){
                                String daysConfig = entryConfig.getDaysConfig();
                                double configurationDays = Double.parseDouble(entryConfig.getDays());
                                switchConfigType(leaveType, beginTime, endTime, emp, between, residue, daysConfig, releaseMode, Boolean.FALSE, configurationDays,validity,days);
                                flag = Boolean.TRUE;
                            }
                        }else {
                            if (lessEqual<= betweenYears){
                                String daysConfig = entryConfig.getDaysConfig();
                                double configurationDays = Double.parseDouble(entryConfig.getDays());
                                switchConfigType(leaveType, beginTime, endTime, emp, between, residue, daysConfig, releaseMode, Boolean.FALSE, configurationDays,validity,days);
                                flag = Boolean.TRUE;
                            }
                        }
                    }
                    if(!flag){
                        throw new BadRequestException( "假期余额不足");
                    }

                }
            }

        }

        List<LeaveApplications> selectList = leaveApplicationsMapper.selectList(Wrappers.lambdaQuery(LeaveApplications.class)
                .eq(LeaveApplications::getRevoked, Boolean.FALSE)
                .eq(LeaveApplications::getEmployeeCode, emp.getCode())
                .apply("year(apply_time) = "+LocalDate.now().getYear())
                .in(LeaveApplications::getAudit, list));
        for (LeaveApplications leaveApplications : selectList) {
            boolean hasIntersection = (beginTime.isBefore(leaveApplications.getEndTime()) || beginTime.isEqual(leaveApplications.getEndTime()))
                    && (endTime.isAfter(leaveApplications.getBeginTime()) || endTime.isEqual(leaveApplications.getBeginTime()));
            if (hasIntersection){
                //不允许请假
                return Boolean.FALSE;
            }
        }

        Integer signCheck = agentAttendanceMapper.selectSignCheck(beginTime, endTime, emp.getCode());
        if (signCheck > 0) {
            //不允许请假
            return Boolean.FALSE;
        }

        //可以请假
        return Boolean.TRUE;
    }

    /**
     * 校验用
     * @param leaveType
     * @param beginTime
     * @param endTime
     * @param emp
     * @param between
     * @param residue
     * @param daysConfig
     * @param releaseMode
     * @param converted
     * @param configurationDays
     */

    private void switchConfigType(String leaveType, LocalDateTime beginTime, LocalDateTime endTime, EmployeeVO emp, long between, double residue, String daysConfig, String releaseMode, Boolean converted, double configurationDays,Integer validity,double days) {
        //先看申请时长是否复合规则 也就是没有请过 然后再看请过的 就用可请天数减去已请 如果大于0再看时长是否符合可请
        double configurationDay = configurationDays;


        switch (daysConfig) {
            case "ANNUALLY":
                List<LeaveApplications> leaveYearList ;
                if (validity == 12){
                    if (beginTime.getYear() != LocalDate.now().getYear() || LocalDate.now().getYear() != endTime.getYear()){
                        throw new BadRequestException( "不可跨年申请");
                    }
                    // 处理每年的情况
                    LocalDate startForYear = LocalDate.now().with(TemporalAdjusters.firstDayOfYear());
                    LocalDate endForYear= LocalDate.now().with(TemporalAdjusters.lastDayOfYear());
                    leaveYearList = leaveApplicationsMapper.selectSumDurationList(emp.getCode(), startForYear, endForYear, null, leaveType);

                }else {
                    //有剩余能请上一年的
                    //剩余只能是看剩余次数
                    //申请时间是今年但是开始是去年的 记录天数 不足就不让补   查询去年的配置天数
                    // 处理去年的情况
                    int startYear = beginTime.getYear();
                    int nowYear= LocalDate.now().getYear();
                    double daysCount = 0;
                    if (residue == 0 && (beginTime.getYear() < LocalDate.now().getYear())){
                        throw new BadRequestException( "假期余额不足，申请日期所在年份仅可使用"+residue+"天");
                    }
                    //今年申请前一年情况
                    if (startYear == (nowYear - 1) ){
                        LocalDateTime lastDayOfLastYear = LocalDateTime.now().minusYears(1).with(TemporalAdjusters.lastDayOfYear()).with(LocalTime.MAX);
                        if (endTime.getYear() != startYear){
                            double lastDaysCount = calculateDays(beginTime.toLocalDate(), lastDayOfLastYear.toLocalDate(), amORpm(beginTime).trim(), amORpm(lastDayOfLastYear).trim());
                            log.info("跨年请假情况年前计算:lastDayOfLastYear{},lastDaysCount:{},residue:{}",lastDayOfLastYear,lastDaysCount,residue);
                            if(lastDaysCount > residue){
                                throw new BadRequestException( "假期余额不足，申请日期所在年份仅可使用"+residue+"天");
                            }
                            LocalDateTime firstDayYear = LocalDateTime.now().with(TemporalAdjusters.firstDayOfYear()).with(LocalTime.MIN);
                            double firstDaysCount = calculateDays(firstDayYear.toLocalDate(), endTime.toLocalDate(), amORpm(firstDayYear).trim(), amORpm(endTime).trim());
                            log.info("跨年请假情况年后计算:firstDayYear{},firstDaysCount:{},configurationDays:{}",firstDayYear,firstDaysCount,configurationDays);
                            if(firstDaysCount > configurationDays){
                                throw new BadRequestException( "假期余额不足，申请日期所在年份仅可使用"+configurationDays+"天");
                            }
                            daysCount = lastDaysCount;
                        }else {
                            if(days > residue){
                                throw new BadRequestException( "假期余额不足，申请日期所在年份仅可使用"+residue+"天");
                            }
                            daysCount = days;
                        }

                        List<LeaveApplications> leaveApplicationsList = leaveApplicationsMapper.selectApplyNowList(emp.getCode(), nowYear, startYear, leaveType);
                        if (CollectionUtils.isNotEmpty(leaveApplicationsList)){
                            //今年申请去年的天数
                            Double yearsDuration = setYearsDuration(leaveApplicationsList);
                            //如果比剩余次数就不允许请假
                            log.info("今年申请去年计算residue:{},yearsDuration:{},daysCount:{}",residue, yearsDuration,daysCount,residue  - (yearsDuration + daysCount));
                            if (0 > residue  - (yearsDuration + daysCount)){
                                double max = Math.max(residue - yearsDuration , 0);
                                throw new BadRequestException( "假期余额不足，申请日期所在年份仅可使用"+max+"天");
                            }
                        }

                    }

                    //申请的是今年以及明年3月前的 也就是当前时间段我能请的假
                    LocalDate startForYear = LocalDate.now().with(TemporalAdjusters.firstDayOfYear());
                    LocalDate endForYear= LocalDate.now().plusYears(1).withMonth(3).with(TemporalAdjusters.lastDayOfMonth());
                    leaveYearList = leaveApplicationsMapper.selectSumDurationList(emp.getCode(), startForYear, endForYear, null, leaveType);
                }

                if (FixedQuotaType.ONE_TIME.name().equals(releaseMode)){
                    //说明是固定每年一次性发放 就需要看时候否折算
                    if (converted && between < 365){
                        //需要折算 折算公式 已入职天数/365*配置发放的日期
                        configurationDays = Math.floor(((double) between / 365) * configurationDays * 2) / 2;
                    }
                }else {
                    //按工作时长发放 假期总额*当下月份/12
                    configurationDays = Math.floor(configurationDays * LocalDate.now().getMonthValue() / 12 * 2) / 2;
                }
                //年内请了多少天
                Double yearsDuration = setYearsDuration(leaveYearList);
                log.info("年内请了多少天:{},配置天数{}",yearsDuration,configurationDays);

                //今年请了去年天数
                double applyNowDay = 0;
                /*int startYear = LocalDate.now().minusYears(1).getYear();
                int nowYear= LocalDate.now().getYear();
                List<LeaveApplications> leaveApplicationsList = leaveApplicationsMapper.selectApplyNowList(emp.getCode(), nowYear, startYear, leaveType);
                double applyNowDay = 0;
                if (CollectionUtils.isNotEmpty(leaveApplicationsList)){
                    //今年申请去年的天数
                    Double yearDuration = setYearsDuration(leaveApplicationsList);
                    log.info("今年申请去年的天数:{}",yearDuration);
                    yearsDuration += yearDuration;
                    //如果比剩余次数就不允许请假
                    double count = Math.max(0,residue  - yearDuration);
                    log.info("今年申请去年的天数:{},总共天数:{},剩余天数:{}",yearDuration,yearsDuration,residue  - yearDuration);
                    //已使用天数
                    if(count == 0 ){
                        applyNowDay = residue;
                    }else {
                        applyNowDay = yearDuration;
                    }

                }*/

                //剩余能请多少天 (结余+配置天数-过期)-请了多少天
                double max = Math.max(0,(residue + configurationDays) - yearsDuration);

                //三月之后不能占用三月前次数(结余)
                //今年三月份之前不能请今年三月份之后的
                LocalDate begin = beginTime.toLocalDate();
                LocalDate startDate = LocalDate.of(LocalDate.now().getYear(), 4, 1);
                LocalDate endDate = LocalDate.of(LocalDate.now().getYear() + 1, 3, 31);
                //三月后的次数应该是 配置天数-可用
                double endResidueCount = Math.max(0,max - applyNowDay);
                log.info("剩余能请多少天:{},去年结余天数:{},今年申请去年天数:{}",max,residue,applyNowDay);
                if(days > endResidueCount){

                    if (LocalDate.now().getMonthValue() <= 3 && begin.isAfter(startDate) && begin.isBefore(endDate)){
                        throw new BadRequestException( "假期余额不足，申请日期所在年份仅可使用"+endResidueCount+"天");
                    }
                    if (LocalDate.now().isAfter(startDate) && LocalDate.now().isBefore(endDate) && begin.isAfter(endDate)){
                        throw new BadRequestException( "假期余额不足，申请日期所在年份仅可使用"+endResidueCount+"天");
                    }
                }



                //总天数
                double configDay = Math.max(0,(residue + configurationDay) - yearsDuration);
                /*if (!(LocalDate.now().getMonthValue() <= 3 && configDay != 0)){
                    applyNowDay = residue;
                }*/

                if (days > configDay){
                    throw new BadRequestException( "假期余额不足");
                }

                if (max == 0){
                    throw new BadRequestException( "假期余额不足");
                }
                break;
            case "MONTHLY":
                // 处理每月的情况  每个月的1号发放 还不能跨月申请
                if ((beginTime.getMonthValue() != endTime.getMonthValue())
                        ||
                    (beginTime.getMonthValue() != LocalDateTime.now().getMonthValue())
                            ||
                        (endTime.getMonthValue() != LocalDateTime.now().getMonthValue()) ){

                    throw new BadRequestException( "此类型假期不可跨月申请");
                }

                LocalDate startForMonthly= beginTime.toLocalDate().with(TemporalAdjusters.firstDayOfMonth());
                LocalDate endForMonthly= endTime.toLocalDate().with(TemporalAdjusters.lastDayOfMonth());
                List<LeaveApplications> leaveMonthlyList = leaveApplicationsMapper.selectSumDurationList(emp.getCode(), startForMonthly, endForMonthly, null, leaveType);
                //月内请了多少天
                Double monthlyDuration = setMonthsDuration(leaveMonthlyList);
                double maxMonthly = Math.max(0, configurationDays - monthlyDuration);
                if (days > configurationDays){
                    throw new BadRequestException( "假期余额不足");
                }
                if (maxMonthly == 0){
                    throw new BadRequestException( "假期余额不足");
                }

                break;
            case "SINGLE_COMPLETION":
                //如丧假设置了每年（一次性修完）3天，则可以请3天及3天以下，如果请了2天，则剩余天数直接清零
                // 处理单次休完的情况 就是每年可以请一次，每次请假不能超过设置的天数
                if (beginTime.getYear() != LocalDate.now().getYear() || LocalDate.now().getYear() != endTime.getYear()){
                    throw new BadRequestException( "不可跨年申请");
                }
                if (days > configurationDays){
                    throw new BadRequestException( "假期余额不足");
                }
                LocalDate startForYearCompletion = LocalDate.now().with(TemporalAdjusters.firstDayOfYear());
                LocalDate endForYearCompletion= LocalDate.now().with(TemporalAdjusters.lastDayOfYear());
                List<LeaveApplications> leaveYearCompletionList = leaveApplicationsMapper.selectSumDurationList(emp.getCode(), startForYearCompletion, endForYearCompletion, null, leaveType);
                //年内请了多少天
                Double yearsCompletionDuration = setYearsDuration(leaveYearCompletionList);
                if (yearsCompletionDuration > 0 ){
                    throw new BadRequestException( "此类型假期每年仅可申请一次");
                }
                break;
            case "SINGLE_LIMIT":
                // 处理单次上限的情况
                if (days > configurationDays){
                    throw new BadRequestException("申请天数不得超过"+ configurationDays +"天");
                }
                break;
            default:
                // 处理其他情况
                break;
        }
    }

    /**
     * 计数用
     * @param between
     * @param residue
     * @param daysConfig
     * @param releaseMode
     * @param converted
     * @param configurationDays
     */
    private double switchConfigTypeCount(EmployeeVO emp,String leaveType, long between, double residue, String daysConfig, String releaseMode, Boolean converted, double configurationDays ,Map<String, Double> map) {

        switch (daysConfig) {
            case "ANNUALLY":

                //剩余只能是看剩余次数
                //申请时间是今年但是开始是去年的 记录天数 不足就不让补   查询去年的配置天数
                // 处理去年的情况
                int startYear = LocalDate.now().minusYears(1).getYear();
                int nowYear= LocalDate.now().getYear();
                Double yearsDuration = 0.0;
                List<LeaveApplications> leaveApplicationsList = leaveApplicationsMapper.selectApplyNowList(emp.getCode(), nowYear, startYear, leaveType);
                if (CollectionUtils.isNotEmpty(leaveApplicationsList)){
                    //今年申请去年的天数
                    yearsDuration += setYearsDuration(leaveApplicationsList);
                    if (yearsDuration != 0 ){
                        //如果比剩余次数就不允许请假
                        double max = Math.max(0,residue  - yearsDuration);
                        log.info("剩余能请多少天:{},去年结余天数:{},今年申请去年天数:{}",max,residue,yearsDuration);
                        //已使用天数
                        if(max == 0 ){
                            map.put("applyNowDay",residue);
                        }else {
                            map.put("applyNowDay",yearsDuration);
                        }
                    }

                }

                LocalDate startForYear = LocalDate.now().with(TemporalAdjusters.firstDayOfYear());
                LocalDate endForYear= LocalDate.now().plusYears(1).withMonth(3).with(TemporalAdjusters.lastDayOfMonth());

                List<LeaveApplications> leaveList = leaveApplicationsMapper.selectSumDurationList(emp.getCode(), startForYear, endForYear, null,leaveType);

                if (CollectionUtils.isNotEmpty(leaveList)){
                    leaveList = leaveList.stream().filter(x->{
                        int year = x.getApplyTime().getYear();
                        if (year == LocalDate.now().getYear()){
                            return true;
                        }
                        return false;
                    }).collect(Collectors.toList());
                }


                // 处理每年的情况
                if (FixedQuotaType.ONE_TIME.name().equals(releaseMode)){
                    //说明是固定每年一次性发放 就需要看时候否折算
                    if (converted && between < 365){
                        //需要折算 折算公式 已入职天数/365*配置发放的日期
                        configurationDays = Math.floor(((double) between / 365) * configurationDays * 2) / 2;
                    }
                }else {
                    //按工作时长发放 假期总额*当下月份/12
                    configurationDays = Math.floor(configurationDays * LocalDate.now().getMonthValue() / 12 * 2) / 2;
                }
                //年内请了多少天(总共清了多少天)
                yearsDuration += setYearsDuration(leaveList);
                map.put("yearsDuration",setYearsDuration(leaveList));
                //剩余能请多少天 (结余+配置天数-过期)-请了多少天
                log.info("配置天数:{},总共多少天:{}",configurationDays,yearsDuration);
                return Math.max(0,(residue + configurationDays) - yearsDuration);
            case "MONTHLY":
                // 处理每月的情况  每个月的1号发放 还不能跨月申请
                //月内请了多少天
                LocalDate startForMonthly= LocalDate.now().with(TemporalAdjusters.firstDayOfMonth());
                LocalDate endForMonthly= LocalDate.now().with(TemporalAdjusters.lastDayOfMonth());
                List<LeaveApplications> leaveMonthlyList = leaveApplicationsMapper.selectSumDurationList(emp.getCode(), startForMonthly, endForMonthly, null, leaveType);

                Double monthlyDuration = setMonthsDuration(leaveMonthlyList);
                map.put("yearsDuration",monthlyDuration);
                return Math.max(0, configurationDays - monthlyDuration);

            case "SINGLE_COMPLETION":
                //如丧假设置了每年（一次性修完）3天，则可以请3天及3天以下，如果请了2天，则剩余天数直接清零
                // 处理单次休完的情况 就是每年可以请一次，每次请假不能超过设置的天数
                //年内请了多少天
                LocalDate startForYearCompletion = LocalDate.now().with(TemporalAdjusters.firstDayOfYear());
                LocalDate endForYearCompletion= LocalDate.now().with(TemporalAdjusters.lastDayOfYear());
                List<LeaveApplications> leaveYearCompletionList = leaveApplicationsMapper.selectSumDurationList(emp.getCode(), startForYearCompletion, endForYearCompletion, null, leaveType);

                Double yearsCompletionDuration = setYearsDuration(leaveYearCompletionList);
                map.put("yearsDuration",yearsCompletionDuration);
                if (yearsCompletionDuration > 0 ){
                    return 0;
                }

                return configurationDays;
            case "SINGLE_LIMIT":
                LocalDate startSingleLimit = LocalDate.now().with(TemporalAdjusters.firstDayOfYear());
                LocalDate endSingleLimit= LocalDate.now().with(TemporalAdjusters.lastDayOfYear());
                List<LeaveApplications> leaveYearSingleLimitList = leaveApplicationsMapper.selectSumDurationList(emp.getCode(), startSingleLimit, endSingleLimit, null, leaveType);

                Double singleLimitDuration = setYearsDuration(leaveYearSingleLimitList);
                map.put("yearsDuration",singleLimitDuration);
                // 处理单次上限的情况
                return configurationDays;
            default:
                // 处理其他情况

                return 0;
        }

    }

    @NotNull
    private LocalDateTime getEndTime(String end) {
        LocalDateTime endTime;
        if (end.contains("午")) {
            if (end.contains("上")) {
                String substring = end.substring(0, 10) + " 12:00:00";
                endTime = LocalDateTime.parse(substring, DateTimeFormatter.ofPattern(DatePatterns.YYYY_MM_DD_HH_MM_SS));
            } else {
                String substring = end.substring(0, 10) + " 23:59:59";
                endTime = LocalDateTime.parse(substring, DateTimeFormatter.ofPattern(DatePatterns.YYYY_MM_DD_HH_MM_SS));
            }
        } else {
            String substring = end.substring(0, 10) + " 23:59:59";
            endTime = LocalDateTime.parse(substring, DateTimeFormatter.ofPattern(DatePatterns.YYYY_MM_DD_HH_MM_SS));
        }
        return endTime;
    }

    @NotNull
    private LocalDateTime getBeginTime(String begin) {
        LocalDateTime beginTime;
        if (begin.contains("午")) {
            if (begin.contains("上")) {
                String substring = begin.substring(0, 10) + " 00:00:00";
                beginTime = LocalDateTime.parse(substring, DateTimeFormatter.ofPattern(DatePatterns.YYYY_MM_DD_HH_MM_SS));
            } else {
                String substring = begin.substring(0, 10) + " 12:00:00";
                beginTime = LocalDateTime.parse(substring, DateTimeFormatter.ofPattern(DatePatterns.YYYY_MM_DD_HH_MM_SS));
            }
        } else {
            String substring = begin.substring(0, 10) + " 00:00:00";
            beginTime = LocalDateTime.parse(substring, DateTimeFormatter.ofPattern(DatePatterns.YYYY_MM_DD_HH_MM_SS));
        }
        return beginTime;
    }

    @Override
    public void add(LeaveApplicationsAddRequest request) {

        final EmployeeVO emp = employeeApi.getEmployeeByEmployeeCode(AgentOrgType.valueOf(RequestContextHolder.getOrgType())
                , RequestContextHolder.getEmployeeCode());
        if (PermissionType.P00001.name().equals(emp.getTopCode())){
            AttendanceLeaveCheck attendanceLeaveCheck = attendanceLeaveCheckMapper.selectOne(Wrappers.lambdaQuery(AttendanceLeaveCheck.class)
                    .eq(AttendanceLeaveCheck::getDeleted, Boolean.FALSE)
                    .last(" and FIND_IN_SET('" + emp.getCode() + "', leave_codes) > 0  limit 1"));
            if (attendanceLeaveCheck != null){
                throw new BadRequestException("当前功能已禁用，有问题请联系机构内勤");
            }
        }

        LocalDateTime beginTime = getBeginTime(request.getBeginTime());
        LocalDateTime endTime = getEndTime(request.getEndTime());
        //判断是否和签到时间重叠
        final List<AgentAttendance> agentAttendance = agentAttendanceMapper.selectList(Wrappers.lambdaQuery(AgentAttendance.class)
                .eq(AgentAttendance::getEmployeeCode, RequestContextHolder.getEmployeeCode())
                .notIn(AgentAttendance::getAudit,AttendanceCheckType.AUDIT_FAILURE)
                .ge(AgentAttendance::getCheckInTime, beginTime.withHour(0).withMinute(0).withSecond(0))
                .le(AgentAttendance::getCheckInTime, endTime.withHour(23).withMinute(59).withSecond(59))
                .orderByDesc(AgentAttendance::getCheckInTime));
        if (CollectionUtils.isNotEmpty(agentAttendance)) {
            for (AgentAttendance info : agentAttendance) {
                LocalDateTime checkInTime = info.getCheckInTime();
                //判断checkInTime如果小于12点则时分秒变为零，大于12点则时分秒变为23，59.59
                if (checkInTime.getHour() < 12){ //2023-11-20 00:00:00
                    checkInTime = checkInTime.withHour(0).withMinute(0).withSecond(0);
                }else{
                    checkInTime = checkInTime.withHour(12).withMinute(0).withSecond(0);
                }
                if (checkInTime.equals(beginTime)) {
                    throw new BadRequestException("当前时间段内"+info.getCheckInTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))+"有签到，暂无法操作~");
                }
            }
        }





        //LeaveApplications leaveApplications = BeanCopier.copyObject(request, LeaveApplications.class);
        LeaveApplications leaveApplications = new LeaveApplications();
        LocalDateTime begin = beginTime.getHour() == 12 ? beginTime.plus(Duration.ofSeconds(1)) : beginTime;
        LocalDateTime dateTime = endTime.getHour() == 12 ? endTime.minus(Duration.ofSeconds(1)) : endTime;
        leaveApplications.setBeginTime(begin);
        leaveApplications.setEndTime(dateTime);
        leaveApplications.setDuration(request.getDuration());
        leaveApplications.setExplainDetails(request.getExplainDetails());
        leaveApplications.setLeaveType(request.getLeaveType());
        leaveApplications.setEmployeeCode(emp.getCode());
        leaveApplications.setEmployeeName(emp.getName());
        leaveApplications.setCompanyCode(emp.getTopCode());
        leaveApplications.setCompanyName(emp.getTopName());
        leaveApplications.setCompanyInstCode(emp.getOrgCode());
        leaveApplications.setCompanyInstName(emp.getOrgName());
        leaveApplications.setOrgType(emp.getOrgType().name());
        leaveApplications.setAudit(AttendanceCheckType.TO_BE_REVIEWED.name());
        leaveApplications.setRevoked(Boolean.FALSE);
        leaveApplications.setApplyTime(LocalDateTime.now());
        leaveApplicationsMapper.insert(leaveApplications);
        Long id = leaveApplications.getId();
        //保存影像
        if (CollectionUtils.isNotEmpty(request.getImageUrls())) {
            for (String info : request.getImageUrls()) {
                agentImageMapper.insertSelective(AgentImage.builder()
                        .employeeCode(RequestContextHolder.getEmployeeCode())
                        .agentId(RequestContextHolder.getAgentId().toString())
                        .imageUrl(info)
                        .imageType(ImageType.LEAVE_IMG.name())
                        .imageKey(id.toString())
                        .build());
            }
        }
        RepairLeaveApproval repBuild = new RepairLeaveApproval();
        boolean lzFlag = false;
        final String employeeCode = RequestContextHolder.getEmployeeCode();
        if ("BG".equals(request.getOrgTypeStr()) && !employeeCode.startsWith("S")) {
            EmployeeOrgVO employeeOrgVO = employeeOrgApi.queryEmployeeOrgInfo(employeeCode);
            //没有上级的情况
            lzFlag = null == employeeOrgVO ||
                    (StringUtils.isEmpty(employeeOrgVO.getBusinessDeptLeaderCode()) &&
                            StringUtils.isEmpty(employeeOrgVO.getBusinessAreaLeaderCode()) &&
                            StringUtils.isEmpty(employeeOrgVO.getBusinessTeamLeaderCode())
                    );
            if (!lzFlag) {
                //判断申请人角色
                boolean isAreaLeader1 = employeeCode.equals(employeeOrgVO.getBusinessAreaLeaderCode());
                boolean isDeptLeader1 = employeeCode.equals(employeeOrgVO.getBusinessDeptLeaderCode());
                boolean isTeamLeader1 = employeeCode.equals(employeeOrgVO.getBusinessTeamLeaderCode());

                //申请人是区长，直接提交量子
                if (isAreaLeader1){
                    attendanceSettingService.applicationForm(id,LeaveApplyType.LEAVE);
                    return;
                }
                //申请人是部长，没有区长
                if (isDeptLeader1 && StringUtils.isEmpty(employeeOrgVO.getBusinessAreaLeaderCode())){
                    attendanceSettingService.applicationForm(id,LeaveApplyType.LEAVE);
                    return;
                }
                //申请人是组长，没有区长部长
                if (isTeamLeader1 && StringUtils.isEmpty(employeeOrgVO.getBusinessAreaLeaderCode())
                        && StringUtils.isEmpty(employeeOrgVO.getBusinessDeptLeaderCode())){
                    attendanceSettingService.applicationForm(id,LeaveApplyType.LEAVE);
                    return;
                }


                //有组长的情况
                if ((StringUtils.isNotEmpty(employeeOrgVO.getBusinessTeamLeaderCode())) && !isTeamLeader1) {
                    leaveApplications.setReviewersCode(employeeOrgVO.getBusinessTeamLeaderCode());
                    leaveApplications.setAuditor(employeeOrgVO.getBusinessTeamLeaderName());
                    repBuild = RepairLeaveApproval.builder()
                            .leaveId(id)
                            .applicationType(LeaveApplyType.LEAVE.name())
                            .leaveType(AttendanceCheckType.TO_BE_REVIEWED.name())
                            .leaveCode(RequestContextHolder.getEmployeeCode())
                            .approvalCode(employeeOrgVO.getBusinessTeamLeaderCode())
                            .createdTime(LocalDateTime.now())
                            .createdUser(1)
                            .sub(Boolean.TRUE)
                            .build();
                    attendanceSettingService.messageSaveByLeader(employeeOrgVO.getBusinessTeamLeaderCode(), MessageContentType.LEADER_LEAVE_COMMIT, emp.getName());
                }else
                //有部长
                if (StringUtils.isNotEmpty(employeeOrgVO.getBusinessDeptLeaderCode()) && !isDeptLeader1) {
                    leaveApplications.setReviewersCode(employeeOrgVO.getBusinessDeptLeaderCode());
                    leaveApplications.setAuditor(employeeOrgVO.getBusinessDeptLeaderName());
                    repBuild = RepairLeaveApproval.builder()
                            .leaveId(id)
                            .applicationType(LeaveApplyType.LEAVE.name())
                            .leaveType(AttendanceCheckType.TO_BE_REVIEWED.name())
                            .leaveCode(RequestContextHolder.getEmployeeCode())
                            .approvalCode(employeeOrgVO.getBusinessDeptLeaderCode())
                            .createdTime(LocalDateTime.now())
                            .createdUser(1)
                            .sub(Boolean.TRUE)
                            .build();
                    attendanceSettingService.messageSaveByLeader(employeeOrgVO.getBusinessDeptLeaderCode(), MessageContentType.LEADER_LEAVE_COMMIT, employeeOrgVO.getEmployeeName());

                }else
                //有区长
                if (StringUtils.isNotEmpty(employeeOrgVO.getBusinessAreaLeaderCode())) {
                    leaveApplications.setReviewersCode(employeeOrgVO.getBusinessAreaLeaderCode());
                    leaveApplications.setAuditor(employeeOrgVO.getBusinessAreaLeaderName());
                    repBuild = RepairLeaveApproval.builder()
                            .leaveId(id)
                            .applicationType(LeaveApplyType.LEAVE.name())
                            .leaveType(AttendanceCheckType.TO_BE_REVIEWED.name())
                            .leaveCode(RequestContextHolder.getEmployeeCode())
                            .approvalCode(employeeOrgVO.getBusinessAreaLeaderCode())
                            .createdTime(LocalDateTime.now())
                            .createdUser(1)
                            .sub(Boolean.TRUE)
                            .build();
                    attendanceSettingService.messageSaveByLeader(employeeOrgVO.getBusinessAreaLeaderCode(), MessageContentType.LEADER_LEAVE_COMMIT, emp.getName());

                }
            }
        }
        //调用量子
        if ("BG".equals(request.getOrgTypeStr()) && !employeeCode.startsWith("S")){
            if (lzFlag){
                attendanceSettingService.applicationForm(id,LeaveApplyType.LEAVE);
            }else{
                leaveApplicationsMapper.updateByPrimaryKeySelective(leaveApplications);
                repairLeaveApprovalMapper.insertSelective(repBuild);
            }
        }

    }


    @Override
    public PageInfo<LeaveAttendanceAuditQueryVO> query(LeaveApplicationsQueryRequest request) {
        MyDataAccessVO myDataAccessVO = dataAccessApi.getCurrentStaffDataAccess(RequestContextHolder.getAdminAppId(), RequestContextHolder.getStaffId());
        List<String> partnerOrgCodes = null;
        if (!myDataAccessVO.getContainsSuperAdmin()) {
            partnerOrgCodes = CollectionUtils.isEmpty(myDataAccessVO.getPartnerOrgCodes()) ? null : Lists.newArrayList(myDataAccessVO.getPartnerOrgCodes());
            if (CollectionUtils.isEmpty(myDataAccessVO.getPartnerOrgCodes())) {
                throw new ApiException(400, "没有权限查看数据");
            }
        }
        log.info("[examineQuery] partnerCodes:{}", JsonUtil.toJSON(partnerOrgCodes));
        Page<LeaveAttendanceAuditQueryVO> page =  leaveApplicationsMapper.selectApplyPage(new Page<>(request.getCurrent(), request.getSize()), request, partnerOrgCodes);

        PageInfo<LeaveAttendanceAuditQueryVO> convert = PageUtil.convert(page, agentAttendance -> BeanCopier.copyObject(agentAttendance, LeaveAttendanceAuditQueryVO.class));
        List<LeaveAttendanceAuditQueryVO> records = convert.getRecords();
        if (CollectionUtils.isNotEmpty(records)) {
            //计算数据
            createDates(records, request.getLeaveType());
        }
        return convert;
    }

    private void createDates(List<LeaveAttendanceAuditQueryVO> records, String type) {
        DateTimeFormatter
                formatter = DateTimeFormatter.ofPattern(DatePatterns.YYYY_MM_DD_HH_MM_SS);
        Map<Long, List<AttendanceCustomer>> attendanceCustomerMap = new HashMap<>();
        Map<Long, List<AttendanceActivity>> attendanceActivityMap = new HashMap<>();

        Map<Long, List<RepairLeaveApproval>> applyAttendance = new HashMap<>();
        Map<Long, List<RepairLeaveApproval>> applyLeave = new HashMap<>();

        Map<String, List<AgentImage>> signImageMap = new HashMap<>();
        Map<String, List<AgentImage>> leveImageMap = new HashMap<>();
        List<Long> ids = records.stream().map(LeaveAttendanceAuditQueryVO::getId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(ids)) {
            List<AgentImage> agentImages = agentImageMapper.selectList(Wrappers.lambdaQuery(AgentImage.class)
                    .in(AgentImage::getImageKey, ids)
                    .in(AgentImage::getImageType, Arrays.asList("APPOINT_IMG", "LEAVE_IMG")));
            leveImageMap = agentImages.stream().filter(x -> x.getImageType().equals(ImageType.LEAVE_IMG.name())).collect(Collectors.groupingBy(AgentImage::getImageKey));
            signImageMap = agentImages.stream().filter(x -> x.getImageType().contains(ImageType.APPOINT_IMG.name())).collect(Collectors.groupingBy(AgentImage::getImageKey));
        }

        List<Long> signId = records.stream().filter(x -> x.getAuditType().contains(LeaveApplyType.APPOINT.name())).map(LeaveAttendanceAuditQueryVO::getId).collect(Collectors.toList());
        List<Long> leaveId = records.stream().filter(x -> x.getAuditType().contains(LeaveApplyType.LEAVE.name())).map(LeaveAttendanceAuditQueryVO::getId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(signId)) {
            List<AttendanceCustomer> attendanceCustomers = attendanceCustomerMapper.selectList(Wrappers.lambdaQuery(AttendanceCustomer.class)
                    .in(AttendanceCustomer::getAttendanceId, signId));
            List<AttendanceActivity> attendanceActivities = attendanceActivityMapper.selectList(Wrappers.lambdaQuery(AttendanceActivity.class)
                    .in(AttendanceActivity::getAttendanceId, signId));
            List<RepairLeaveApproval> repairLeaveApprovals = repairLeaveApprovalMapper.selectList(Wrappers.lambdaQuery(RepairLeaveApproval.class)
                    .eq(RepairLeaveApproval::getCreatedUser, "2")
                    .eq(RepairLeaveApproval::getApplicationType, LeaveApplyType.APPOINT.name())
                    .in(RepairLeaveApproval::getLeaveId, signId));
            attendanceCustomerMap = attendanceCustomers.stream().collect(Collectors.groupingBy(AttendanceCustomer::getAttendanceId));
            attendanceActivityMap = attendanceActivities.stream().collect(Collectors.groupingBy(AttendanceActivity::getAttendanceId));
            applyAttendance = repairLeaveApprovals.stream().collect(Collectors.groupingBy(RepairLeaveApproval::getLeaveId));

        }
        if (CollectionUtils.isNotEmpty(leaveId)){
            List<RepairLeaveApproval> repairLeaveApprovals = repairLeaveApprovalMapper.selectList(Wrappers.lambdaQuery(RepairLeaveApproval.class)
                    .eq(RepairLeaveApproval::getCreatedUser, "2")
                    .eq(RepairLeaveApproval::getApplicationType, LeaveApplyType.LEAVE.name())
                    .in(RepairLeaveApproval::getLeaveId, leaveId));
            applyLeave = repairLeaveApprovals.stream().collect(Collectors.groupingBy(RepairLeaveApproval::getLeaveId));
        }

        for (LeaveAttendanceAuditQueryVO item : records) {

            if (item.getType().contains(LeaveApplyType.APPOINT.name())) {
                if (StringUtils.isNotEmpty(item.getCheckOutTime())) {
                    LocalDateTime outDateTime = LocalDateTime.parse(item.getCheckOutTime(), formatter);
                    LocalDateTime startDateTime = LocalDateTime.parse(item.getCheckInTime(), formatter);
                    Duration duration = Duration.between(startDateTime, outDateTime);
                    String minute = String.format("%.1f", (double) duration.toMinutes() / 60);
                    item.setDuration(minute + "小时");
                    item.setCheckOutTime(outDateTime.format(DateTimeFormatter.ofPattern(DatePatterns.YYYY_MM_DD_HH_MM)));
                }
                item.setDate(LocalDateTime.parse(item.getCheckInTime(), formatter).format(DateTimeFormatter.ofPattern(DatePatterns.YYYY_MM_DD)));
                item.setCheckInTime(LocalDateTime.parse(item.getCheckInTime(), formatter).format(DateTimeFormatter.ofPattern(DatePatterns.YYYY_MM_DD_HH_MM)));
                String auditType = item.getAuditType().contains(SignInType.APPOINT_HALF.name()) ? SignInType.APPOINT_HALF.name() : SignInType.APPOINT_ALL.name();
                item.setFlag(LeaveApplyType.APPOINT.name());
                item.setAuditType(auditType);
                List<AttendanceCustomerVO> attendanceCustomerVOS = new ArrayList<>();
                List<AttendanceActivityVO> attendanceActivityVOS = new ArrayList<>();
                //客户线索详情
                Long id = item.getId();
                List<AttendanceCustomer> customers = attendanceCustomerMap.get(id);
                if (CollectionUtils.isNotEmpty(customers)) {
                    customers.forEach(x ->
                            attendanceCustomerVOS.add(AttendanceCustomerVO.builder()
                                    .customerName(x.getCustomerName())
                                    .mangerName(x.getMangerName())
                                    .customerGender(x.getCustomerGender())
                                    .estimatedPremium(x.getEstimatedPremium())
                                    .outlets(x.getOutlets())
                                    .otherInfo(x.getOtherInfo())
                                    .build())
                    );
                    item.setAttendanceCustomers(attendanceCustomerVOS);
                }

                //推动及活动详情
                List<AttendanceActivity> activities = attendanceActivityMap.get(id);
                if (CollectionUtils.isNotEmpty(activities)) {
                    activities.forEach(x -> attendanceActivityVOS.add(AttendanceActivityVO.builder()
                            .label(x.getLabel())
                            .explainTheTopic(x.getExplainTheTopic())
                            .numberOfParticipants(x.getNumberOfParticipants())
                            .activityOutlets(x.getActivityOutlets())
                            .detailRecord(x.getDetailRecord())
                            .build()));
                    item.setAttendanceActivities(attendanceActivityVOS);
                }
                imageData(signImageMap, item, id);
                //量子id
                setApplyUrl(applyAttendance, item);
            } else {
                item.setDuration(item.getDuration() + "天");
                LocalDateTime begin = LocalDateTime.parse(item.getBeginTime(), formatter);
                LocalDateTime end = LocalDateTime.parse(item.getEndTime(), formatter);
                item.setBeginTime(begin.toLocalDate() + amORpm(begin));
                item.setEndTime(end.toLocalDate() + amORpm(end));
                if (item.getType().contains(LeaveApplyType.LEAVE.name())){
                    item.setFlag(LeaveApplyType.LEAVE.name());
                    imageData(leveImageMap, item, item.getId());
                    //量子id
                    setApplyUrl(applyLeave, item);
                }else {
                    item.setFlag(LeaveApplyType.REVOKE.name());
                }
            }

        }

    }

    private void setApplyUrl(Map<Long, List<RepairLeaveApproval>> applyAttendance, LeaveAttendanceAuditQueryVO item ) {
        List<RepairLeaveApproval> repairLeaveApprovals = applyAttendance.get(item.getId());
        if (CollectionUtils.isNotEmpty(repairLeaveApprovals)){
            repairLeaveApprovals.sort(Comparator.comparing(RepairLeaveApproval::getCreatedTime).reversed());
            RepairLeaveApproval repairLeaveApproval = repairLeaveApprovals.get(0);
            String apply = applyUrl;
            if (repairLeaveApproval.getOaType() !=null && repairLeaveApproval.getOaType()){
                apply = applyUrlNew;
            }
            item.setApplyUrl(apply + repairLeaveApproval.getBatchId());
        }
    }

    private <T>  void imageData(Map<String, List<AgentImage>> signImageMap, T item, Long id) {
        List<AgentImage> agentImages = signImageMap.get(id.toString());
        if (CollectionUtils.isNotEmpty(agentImages)) {
            List<String> imageList = new ArrayList<>();
            agentImages.forEach(x -> imageList.add(x.getImageUrl()));
            try {
                Method setImageListMethod = item.getClass().getMethod("setImageList", List.class);
                setImageListMethod.invoke(item, imageList);
            } catch (Exception e) {
                log.error("imageData 异常:",e);
            }
        }
    }

    private String amORpm(LocalDateTime time) {
        if (time.toLocalTime().isBefore(LocalTime.NOON)) {
            return " 上午";
        } else {
            return " 下午";
        }
    }
    private String amORpm2(LocalDateTime time) {
        if (time.toLocalTime().isBefore(LocalTime.NOON)) {
            return "上午";
        } else {
            return "下午";
        }
    }

    @Override
    public LeaveApplicationsVO sumCount(String employeeCode,String type) {

        LeaveApplicationsVO.LeaveApplicationsVOBuilder builder = LeaveApplicationsVO.builder();
        //写死了PARTNER
        final EmployeeVO emp = employeeApi.getEmployeeByEmployeeCode(AgentOrgType.PARTNER
                , employeeCode);
/**
 * .sickLeave("0,0,0")
 *                 .marriageLeave("0,0,0")
 *                 .funeralLeave("0,0,0")
 *                 .maternityLeave("0,0,0")
 *                 .paternityLeave("0,0,0")
 *                 .personalLeave("0,0,0")
 *                 .businessTravelLeave("0,0,0")
 *                 .officialBusinessLeave("0,0,0")
 *                 .annualLeave("0,0,0")
 */
        LocalDate startTime = LocalDate.now().with(TemporalAdjusters.firstDayOfMonth());
        LocalDate endTime = LocalDate.now().with(TemporalAdjusters.lastDayOfMonth());

        List<LeaveApplications> leaveApplicationsList = leaveApplicationsMapper.selectSumDurationList(employeeCode, startTime, endTime, null,null);
        LocalDate startYearTime = LocalDate.now().with(TemporalAdjusters.firstDayOfYear());
        LocalDate endYearTime = LocalDate.now().with(TemporalAdjusters.lastDayOfYear());

        List<LeaveApplications> leaveApplicationsYearList = leaveApplicationsMapper.selectSumDurationList(employeeCode, startYearTime, endYearTime, null,null);

        for (LeaveApplications applications : leaveApplicationsYearList) {
            if (applications.getBeginTime().isBefore(LocalDateTime.now()) && applications.getEndTime().isAfter(LocalDateTime.now())){
                leaveApplicationsList.add(applications);
            }
        }
        List<LeaveApplications> collect = leaveApplicationsList.stream().distinct().collect(Collectors.toList());
        Map<String, Double> durationMap = setDuration(collect);

        List<String> leaveTypeList = new ArrayList<>();

        final List<LeaveCheckConfig> checkConfigList =leaveCheckConfigMapper.selectList(new LambdaQueryWrapper<LeaveCheckConfig>()
                .eq(LeaveCheckConfig::getOrgCode, emp.getOrgCode())
                .eq(LeaveCheckConfig::getDeleted,Boolean.FALSE)
                .groupBy(LeaveCheckConfig::getLeaveType));
        for (LeaveCheckConfig configuration : checkConfigList) {
            String leaveType = configuration.getLeaveType();
            LeaveCheckConfig leaveCheckConfig = leaveCheckConfigMapper.selectOne(new LambdaQueryWrapper<LeaveCheckConfig>()
                    .eq(LeaveCheckConfig::getOrgCode, emp.getOrgCode())
                    .eq(LeaveCheckConfig::getLeaveType,leaveType)
                    .eq(LeaveCheckConfig::getYears,LocalDate.now().getYear())
                    .eq(LeaveCheckConfig::getDeleted, Boolean.FALSE)
                    .orderByDesc(LeaveCheckConfig::getCreatedTime)
                    .last("limit 1"));
            if (leaveCheckConfig == null){
                leaveCheckConfig = leaveCheckConfigMapper.selectOne(new LambdaQueryWrapper<LeaveCheckConfig>()
                        .eq(LeaveCheckConfig::getOrgCode, emp.getOrgCode())
                        .eq(LeaveCheckConfig::getLeaveType,leaveType)
                        .lt(LeaveCheckConfig::getYears,LocalDate.now().getYear())
                        .eq(LeaveCheckConfig::getDeleted, Boolean.FALSE)
                        .orderByDesc(LeaveCheckConfig::getYears)
                        .orderByDesc(LeaveCheckConfig::getCreatedTime)
                        .last("limit 1"));
            }
            setDurationDays(builder, emp, leaveCheckConfig, leaveType,leaveTypeList);
        }


        LocalDate startForYear = LocalDate.now().with(TemporalAdjusters.firstDayOfYear());
        LocalDate endForYear= LocalDate.now().with(TemporalAdjusters.lastDayOfYear());

        //不配置也不限制
        //leaveTypeList.add(LeaveType.BUSINESS_TRAVEL_LEAVE.name());
        //leaveTypeList.add(LeaveType.OFFICIAL_BUSINESS_LEAVE.name());



        //已申请请假天数
        //审核通过请假天数
        String applyCount = formatDuration(String.valueOf(durationMap.get("applyCount")));
        String applyCount1 = formatDuration(String.valueOf(durationMap.get("duration")));
        builder.applyCount(applyCount)
                .applyAuditCount(applyCount1);
        for (String leaveType : leaveTypeList) {
            List<LeaveApplications> leaveList = leaveApplicationsMapper.selectSumDurationList(emp.getCode(), startForYear, endForYear, null,leaveType);
            setValues(leaveType, builder, formatDuration(String.valueOf(setYearsDuration(leaveList))));
        }
        LeaveApplicationsVO build = builder.build();
        String[] leaveTypes = build.getAllLeaveTypes();
        for (int i = 0; i < leaveTypes.length; i++) {
            String leave = leaveTypes[i];
            if (StringUtils.isEmpty(leave)){
                LeaveType leaveType = LeaveType.values()[i];
                List<LeaveApplications> leaveList = leaveApplicationsMapper.selectSumDurationList(emp.getCode(), startForYear, endForYear, null,leaveType.name());
                setValues(leaveType.name(), build, formatDuration(String.valueOf(setYearsDuration(leaveList))));
            }
        }


        return build;
    }

    private void setDurationDays(LeaveApplicationsVO.LeaveApplicationsVOBuilder builder, EmployeeVO emp, LeaveCheckConfig configuration, String leaveType,List<String> leaveTypeList ) {
        //剩余天数
        double daysCount = 0;
        double configDay = 0;

        //去年剩余天数
        double residue = 0;
        //入职时间
        LocalDateTime entryTime = emp.getEntryTime() == null ? LocalDateTime.now() : emp.getEntryTime();
        long between = ChronoUnit.DAYS.between(entryTime, LocalDateTime.now());
        double betweenYears = (double) between/365;

        //发放方式
        String releaseMode = configuration.getReleaseMode();
        //看是否是有效期15月  如果是则需要查询结余表
        if (configuration.getValidity() == 15){
            AgentLeaveRemaining leaveRemaining = agentLeaveRemainingMapper.selectOne(Wrappers.lambdaQuery(AgentLeaveRemaining.class)
                    .eq(AgentLeaveRemaining::getEmployeeCode, emp.getCode())
                    .eq(AgentLeaveRemaining::getYears,LocalDate.now().minusYears(1).getYear())
                    .eq(AgentLeaveRemaining::getLeaveType, leaveType)
                    .last("limit 1"));
            if (null != leaveRemaining){
                residue = Double.parseDouble(leaveRemaining.getResidue()) - Double.parseDouble(leaveRemaining.getOverdue());
            }
        }


        Map<String, Double> map = new HashMap<>();
        map.put("yearsDuration", (double) 0);
        map.put("applyNowDay",(double) 0);
        Boolean configFlag =Boolean.FALSE;
//        Boolean infiniteFlag =Boolean.FALSE;
        if (FixedQuotaType.FIXED_QUOTA.name().equals(configuration.getQuotaConfig())){
            //配置天数
            double configurationDays = Double.parseDouble(configuration.getDays());
            //可用
            daysCount = switchConfigTypeCount(emp, leaveType, between, residue, configuration.getDaysConfig(), releaseMode, configuration.getConverted(), configurationDays,map);
            //剩余
            configDay = Math.max(0,(residue + configurationDays) - (map.get("yearsDuration")+map.get("applyNowDay")));
            log.info("固定额度返回的天数剩余天数:{},配置天数:{},今年申请去年天数以及总共使用天数:{}",daysCount,(residue + configurationDays),JsonUtil.toJSON(map));
            if (configuration.getDaysConfig().equals(FixedQuotaType.SINGLE_LIMIT.name()) ||
                    configuration.getDaysConfig().equals(FixedQuotaType.SINGLE_COMPLETION.name())){
                configFlag =Boolean.TRUE;
            }
        }else if (FixedQuotaType.SENIORITY.name().equals(configuration.getQuotaConfig())){
            //司龄
            List<EntryConfig> entryConfigList = JsonUtil.toList(configuration.getEntryConfig(), List.class, EntryConfig.class);
            for (int i = 0 ; i < entryConfigList.size() ; i++ ){
                EntryConfig entryConfig = entryConfigList.get(i);
                double lessEqual = Double.parseDouble(entryConfig.getLessEqual());
                double less = Double.parseDouble(entryConfig.getLess());
                if (i != entryConfigList.size()-1){
                    //说明匹配区间
                    if (lessEqual<= betweenYears && betweenYears < less){
                        String daysConfig = entryConfig.getDaysConfig();
                        double configurationDay = Double.parseDouble(entryConfig.getDays());
                        //可用
                        daysCount = switchConfigTypeCount(emp, leaveType,between, residue, daysConfig, releaseMode, Boolean.FALSE, configurationDay,map);
                        //剩余
                        configDay = Math.max(0,(residue + configurationDay) - (map.get("yearsDuration")+map.get("applyNowDay")));
                        log.info("司龄返回的天数剩余天数:{},配置天数:{},今年申请去年天数以及总共使用天数:{}",daysCount,(residue + configurationDay),JsonUtil.toJSON(map));
                        if (configuration.getDaysConfig().equals(FixedQuotaType.SINGLE_LIMIT.name()) ||
                                configuration.getDaysConfig().equals(FixedQuotaType.SINGLE_COMPLETION.name())){
                            configFlag =Boolean.TRUE;
                        }
                    }
                }else {
                    if (lessEqual<= betweenYears){
                        String daysConfig = entryConfig.getDaysConfig();
                        double configurationDay = Double.parseDouble(entryConfig.getDays());
                        //可用
                        daysCount = switchConfigTypeCount(emp, leaveType, between, residue, daysConfig, releaseMode, Boolean.FALSE, configurationDay,map);
                        //剩余
                        configDay = Math.max(0,(residue + configurationDay) - (map.get("yearsDuration")+map.get("applyNowDay")));
                        log.info("司龄返回的天数剩余天数:{},配置天数:{},今年申请去年天数以及总共使用天数:{}",daysCount,(residue + configurationDay),JsonUtil.toJSON(map));
                        if (configuration.getDaysConfig().equals(FixedQuotaType.SINGLE_LIMIT.name()) ||
                                configuration.getDaysConfig().equals(FixedQuotaType.SINGLE_COMPLETION.name())){
                            configFlag =Boolean.TRUE;
                        }
                    }
                }
            }
        }else {
            leaveTypeList.add(leaveType);
//            infiniteFlag = Boolean.TRUE;
        }

        //今年已休
        double yearsDuration = map.get("yearsDuration");
        //今年休去年的
        double applyNowDay = map.get("applyNowDay");
        //总天数
        /*if (LocalDate.now().getMonthValue() <= 3 && configDay != 0){
            //当前月份是三月份之前的算次数
            configDay += (residue -applyNowDay);
        }else {
            //applyNowDay = residue;
        }*/
        if (configFlag){
            configDay =daysCount;
        }
        String join = String.join(",",
                //已休
                formatDuration(String.valueOf(yearsDuration)),
                //剩余
                formatDuration(String.valueOf(configDay)),
                //可用
                formatDuration(String.valueOf(daysCount)));

        /*if (infiniteFlag){
            //如果是无限就显示已休
            join =  formatDuration(String.valueOf(yearsDuration));
        }else {
           join = String.join(",",
                    //已休
                    formatDuration(String.valueOf(yearsDuration)),
                    //剩余
                    formatDuration(String.valueOf(configDay)),
                    //可用
                    formatDuration(String.valueOf(daysCount)));
        }*/
        setValues(leaveType, builder, join);
    }

    private void setValues(String leaveType, LeaveApplicationsVO.LeaveApplicationsVOBuilder builder, String join) {
        switch (leaveType){
            case "SICK_LEAVE":
                builder.sickLeave(join);
                break;
            case "MARRIAGE_LEAVE":
                builder.marriageLeave(join);
                break;
            case "FUNERAL_LEAVE":
                builder.funeralLeave(join);
                break;
            case "MATERNITY_LEAVE":
                builder.maternityLeave(join);
                break;
            case "PATERNITY_LEAVE":
                builder.paternityLeave(join);
                break;
            case "PERSONAL_LEAVE":
                builder.personalLeave(join);
                break;
            case "BUSINESS_TRAVEL_LEAVE":
                builder.businessTravelLeave(join);
                break;

            case "ANNUAL_LEAVE":
                builder.annualLeave(join);
                break;
        }
    }

    private void setValues(String leaveType, LeaveApplicationsVO builder, String join) {
        switch (leaveType){
            case "SICK_LEAVE":
                builder.setSickLeave(join);
                break;
            case "MARRIAGE_LEAVE":
                builder.setMarriageLeave(join);
                break;
            case "FUNERAL_LEAVE":
                builder.setFuneralLeave(join);
                break;
            case "MATERNITY_LEAVE":
                builder.setMaternityLeave(join);
                break;
            case "PATERNITY_LEAVE":
                builder.setPaternityLeave(join);
                break;
            case "PERSONAL_LEAVE":
                builder.setPersonalLeave(join);
                break;
            case "BUSINESS_TRAVEL_LEAVE":
                builder.setBusinessTravelLeave(join);
                break;
            case "ANNUAL_LEAVE":
                builder.setAnnualLeave(join);
                break;
        }
    }

    private static String formatDuration(String duration) {
        //如果duration是整数，则去掉.0
        return duration.substring(duration.indexOf(".") + 1).equals("0") ? duration.substring(0, duration.indexOf(".")) : duration;
    }

    @Override
    public AuditResponse audit(AuditRequest request) {
        DateTimeFormatter dfDateTime = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LeaveApplications leaveApplications = leaveApplicationsMapper.selectById(request.getId());
        LocalDate localDate = leaveApplications.getBeginTime().toLocalDate();
        AttendanceCheckType type;
        //通过
        if (FLAG.equals(request.getFlag())) {


            if (leaveApplications.getBeginTime().getMonth() != leaveApplications.getEndTime().getMonth()){
                //拆分 然后再审核表新增一条新id的量子记录
                leaveApplications.setAudit(AttendanceCheckType.PASS_THE_AUDIT.name());
                leaveApplications.setAuditor(RequestContextHolder.getStaffUsername());
                leaveApplications.setAuditorId(RequestContextHolder.getStaffId().toString());
                leaveApplications.setAuditTime(LocalDateTime.now());
                leaveApplications.setUpdateTime(LocalDateTime.now());
                attendanceSettingService.splitLeaveApplication(leaveApplications,"GI");
            }else {
                leaveApplicationsMapper.updateByPrimaryKeySelective(
                        LeaveApplications.builder()
                                .id(request.getId())
                                .audit(AttendanceCheckType.PASS_THE_AUDIT.name())
                                .auditTime(LocalDateTime.now())
                                .updateTime(LocalDateTime.now())
                                .auditor(RequestContextHolder.getStaffUsername())
                                .auditorId(RequestContextHolder.getStaffId().toString())
                                .build());

            }

            type = AttendanceCheckType.PASS_THE_AUDIT;
            //请假通过消息
            attendanceSettingService.messageSave(request.getEmployeeCode(),leaveApplications.getEmployeeName(), "", MessageContentType.LEAVE_PASS_THE_AUDIT, localDate);
        } else {
            leaveApplicationsMapper.updateByPrimaryKeySelective(LeaveApplications.builder()
                    .id(request.getId())
                    .audit(AttendanceCheckType.AUDIT_FAILURE.name())
                    .auditor(RequestContextHolder.getStaffUsername())
                    .auditorId(RequestContextHolder.getStaffId().toString())
                    .auditOutcome(request.getReason())
                    .auditTime(LocalDateTime.now())
                    .updateTime(LocalDateTime.now())
                    .build());
            type = AttendanceCheckType.AUDIT_FAILURE;
            //请假不通过消息
            attendanceSettingService.messageSave(request.getEmployeeCode(),leaveApplications.getEmployeeName(), request.getReason(), MessageContentType.LEAVE_AUDIT_FAILURE, localDate);
        }
        return AuditResponse.builder()
                .audit(type.name())
                .auditor(RequestContextHolder.getStaffUsername())
                .auditOutcome(request.getReason())
                .auditTime(dfDateTime.format(LocalDateTime.now()))
                .build();
    }

    public MeAttendanceAndLeaveResponse attendanceAndLeave(String employeeCode) {
        if (StringUtils.isEmpty(employeeCode)){
            employeeCode = RequestContextHolder.getEmployeeCode();
        }
        LocalDate nowDate = LocalDate.now();
        DataStatisticsRequest dataStatisticsRequest = DataStatisticsRequest.builder()
                .years(nowDate.getYear())
                .month(nowDate.getMonthValue())
                .employeeCode(employeeCode)
                .orgTypeStr(RequestContextHolder.getOrgType())
                .build();
        DataStatisticsResponse dataStatisticsResponse = attendanceService.daKaDataStatistics(dataStatisticsRequest);

        return MeAttendanceAndLeaveResponse.builder()
                .attendance(dataStatisticsResponse.getNormalAttendance())
                .absence(dataStatisticsResponse.getUnusualAttendance())
                .leave(dataStatisticsResponse.getLeaveAttendance())
                .build();
    }

    @Override
    public PageInfo<H5LeaveListVO> h5Query(LeaveApplicationsQueryRequest request) {
        List<H5LeaveListVO> responseList = new ArrayList<>();

        //查询审核表
        String employeeCode = StringUtils.isEmpty(request.getEmployeeCode()) ? RequestContextHolder.getEmployeeCode() : request.getEmployeeCode();

        List<RepairLeaveApproval> repairLeaveApprovals;
        if (!request.getIsSub()){
            repairLeaveApprovals = repairLeaveApprovalMapper.selectNoMeList(employeeCode,request.getLeaveType());
        }else{
            repairLeaveApprovals = repairLeaveApprovalMapper.selectList(new LambdaQueryWrapper<>(RepairLeaveApproval.class)
                    .eq(RepairLeaveApproval::getApprovalCode, employeeCode)
                    .eq(RepairLeaveApproval::getSub,Boolean.TRUE)
                    .eq(RepairLeaveApproval::getLeaveType, AttendanceCheckType.TO_BE_REVIEWED.name())
                    .orderByDesc(RepairLeaveApproval::getCreatedTime));
        }

        for (RepairLeaveApproval row : repairLeaveApprovals) {
            //查询请假记录
            if (row.getApplicationType().equals(LeaveApplyType.LEAVE.name())) {
                LeaveApplications leaveApplications = leaveApplicationsMapper.selectOne(Wrappers.lambdaQuery(LeaveApplications.class)
                        .eq(LeaveApplications::getId, row.getLeaveId()).last("limit 1"));

                if (null != leaveApplications) {
                    List<AgentImage> agentImages = agentImageMapper.selectList(Wrappers.lambdaQuery(AgentImage.class)
                            .eq(AgentImage::getImageKey, leaveApplications.getId())
                            .eq(AgentImage::getImageType, ImageType.LEAVE_IMG.name()));
                    final List<String> imgUrls = agentImages.stream().map(AgentImage::getImageUrl).collect(Collectors.toList());
                    final LeaveApplicationsVO leaveApplicationsVO = sumCount(leaveApplications.getEmployeeCode(), LeaveApplyType.LEAVE.name());
                    responseList.add(H5LeaveListVO.builder()
                            .id(leaveApplications.getId())
                            .leaveType(LeaveApplyType.LEAVE.name())
                            .employeeCode(leaveApplications.getEmployeeCode())
                            .employeeName(leaveApplications.getEmployeeName())
                            .beginTime(leaveApplications.getBeginTime().format(DateTimeFormatter.ofPattern(DatePatterns.YYYY_MM_DD_HH_MM)))
                            .endTime(leaveApplications.getEndTime().format(DateTimeFormatter.ofPattern(DatePatterns.YYYY_MM_DD_HH_MM)))
                            .auditRemark(leaveApplications.getExplainDetails())
                            .leaveTime(leaveApplications.getDuration())
                            .applyTime(leaveApplications.getCreateTime().format(DateTimeFormatter.ofPattern(DatePatterns.YYYY_MM_DD_HH_MM)))
                            .leaveCount(leaveApplicationsVO.getApplyCount())
                            .leavePassCount(leaveApplicationsVO.getApplyAuditCount())
                            .sickLeave(leaveApplicationsVO.getSickLeave())
                            .marriageLeave(leaveApplicationsVO.getMarriageLeave())
                            .funeralLeave(leaveApplicationsVO.getFuneralLeave())
                            .maternityLeave(leaveApplicationsVO.getMaternityLeave())
                            .paternityLeave(leaveApplicationsVO.getPaternityLeave())
                            .personalLeave(leaveApplicationsVO.getPersonalLeave())
                            .businessTravelLeave(leaveApplicationsVO.getBusinessTravelLeave())
                            .annualLeave(leaveApplicationsVO.getAnnualLeave())
                            .imageUrls(imgUrls)
                            .leaveName(leaveApplications.getLeaveType())
                            .auditOutcome(leaveApplications.getAuditOutcome())
                            .auditId(row.getId())
                            .build());

                }
            }
            //查询销假记录
            if (row.getApplicationType().equals(LeaveApplyType.REVOKE.name())) {
                LeaveRevoke leaveRevoke = leaveRevokeMapper.selectOne(Wrappers.lambdaQuery(LeaveRevoke.class)
                        .eq(LeaveRevoke::getId, row.getLeaveId()).last("limit 1"));

                if (null != leaveRevoke) {

                    responseList.add(H5LeaveListVO.builder()
                            .id(leaveRevoke.getId())
                            .leaveType(LeaveApplyType.REVOKE.name())
                            .employeeCode(leaveRevoke.getEmployeeCode())
                            .employeeName(leaveRevoke.getEmployeeName())
                            .beginTime(leaveRevoke.getBeginTime().format(DateTimeFormatter.ofPattern(DatePatterns.YYYY_MM_DD_HH_MM)))
                            .endTime(leaveRevoke.getEndTime().format(DateTimeFormatter.ofPattern(DatePatterns.YYYY_MM_DD_HH_MM)))
                            .auditRemark(leaveRevoke.getExplainDetails())
                            .applyTime(leaveRevoke.getCreateTime().format(DateTimeFormatter.ofPattern(DatePatterns.YYYY_MM_DD_HH_MM)))
                            .leaveName(leaveRevoke.getLeaveType())
                            .auditId(row.getId())
                            .build());

                }
            }
            //查询补卡记录
            if (row.getApplicationType().equals(LeaveApplyType.APPOINT.name())) {
                AgentAttendance agentAttendances = agentAttendanceMapper.selectOne(Wrappers.lambdaQuery(AgentAttendance.class)
                        .eq(AgentAttendance::getId, row.getLeaveId()).last("limit 1"));

                if (null != agentAttendances) {
                    List<AgentImage> agentImages = agentImageMapper.selectList(Wrappers.lambdaQuery(AgentImage.class)
                            .eq(AgentImage::getImageKey, agentAttendances.getId())
                            .eq(AgentImage::getImageType, ImageType.APPOINT_IMG.name()));
                    final List<String> imageUrls = agentImages.stream().map(AgentImage::getImageUrl).collect(Collectors.toList());

                    List<AgentAttendance> attendancesCount = agentAttendanceMapper.selectList(Wrappers.lambdaQuery(AgentAttendance.class)
                            .in(AgentAttendance::getEmployeeCode, agentAttendances.getEmployeeCode())
                            .and(wrapper -> wrapper
                                    .like(AgentAttendance::getAuditType, "%APPOINT_HALF%")
                                    .or()
                                    .like(AgentAttendance::getAuditType, "%APPOINT_ALL%"))
                            .ge(AgentAttendance::getCheckInTime, LocalDate.now().withDayOfMonth(1))
                            .le(AgentAttendance::getCheckInTime, LocalDate.now().withDayOfMonth(LocalDate.now().lengthOfMonth())));
                    Map<String, List<AgentAttendance>> empMap = attendancesCount.stream().collect(Collectors.groupingBy(AgentAttendance::getEmployeeCode));
                    String auditType = agentAttendances.getAuditType().contains(SignInType.APPOINT_ALL.name()) ? SignInType.APPOINT_ALL.name() : SignInType.APPOINT_HALF.name();
                    responseList.add(H5LeaveListVO.builder()
                            .id(agentAttendances.getId())
                            .leaveType(LeaveApplyType.APPOINT.name())
                            .appointType(auditType)
                            .employeeCode(agentAttendances.getEmployeeCode())
                            .employeeName(agentAttendances.getEmployeeName())
                            .beginTime(null == agentAttendances.getCheckInTime() ? null : agentAttendances.getCheckInTime().format(DateTimeFormatter.ofPattern(DatePatterns.YYYY_MM_DD_HH_MM)))
                            .endTime(null == agentAttendances.getCheckOutTime() ? null : agentAttendances.getCheckOutTime().format(DateTimeFormatter.ofPattern(DatePatterns.YYYY_MM_DD_HH_MM)))
                            .auditRemark(agentAttendances.getAuditRemark())
                            .applyTime(agentAttendances.getCreatedTime().format(DateTimeFormatter.ofPattern(DatePatterns.YYYY_MM_DD_HH_MM)))
                            .locationName(agentAttendances.getLocationName())
                            .leaveCount(null == empMap.get(agentAttendances.getEmployeeCode()) ? "0" : String.valueOf(empMap.get(agentAttendances.getEmployeeCode()).size()))
                            .businessLabel(agentAttendances.getBusinessLabel())
                            .auditOutcome(agentAttendances.getAuditOutcome())
                            .signInType(agentAttendances.getSignInType())
                            .imageUrls(imageUrls)
                            .auditId(row.getId())
                            .build());
                }
            }
        }

        //排序
        if (CollectionUtils.isNotEmpty(responseList)) {
            if (request.getIsSub()){
                //正序
                responseList.sort(Comparator.comparing(H5LeaveListVO::getApplyTime));
            }else{
                //倒序
                responseList.sort(Comparator.comparing(H5LeaveListVO::getApplyTime).reversed());
            }
        }
        //分页
        PageInfo<H5LeaveListVO> pageInfo = new PageInfo<>();
        pageInfo.setTotal(responseList.size());
        pageInfo.setCurrent(request.getCurrent());
        pageInfo.setSize(request.getSize());
        pageInfo.setRecords(responseList.stream().skip((request.getCurrent() - 1) * request.getSize()).limit(request.getSize()).collect(Collectors.toList()));
        return pageInfo;
    }

    @Override
    public void h5Audit(AuditRequest request) {
        String auditType = request.getFlag().equals(AttendanceCheckType.AUDIT_FAILURE.name())
                ? AttendanceCheckType.AUDIT_FAILURE.name():
                AttendanceCheckType.UNDER_REVIEW.name();
        String name = "流程结束";
        try {
            EmployeeVO employeeInfo = employeeApi.getEmployeeInfo(AgentOrgType.valueOf(RequestContextHolder.getOrgType()), RequestContextHolder.getEmployeeCode());
            name = employeeInfo.getName();
        }catch (Exception e){
            log.info("H5审核查询当前代理人信息错误,",e);
        }
        RepairLeaveApproval repairLeaveApproval = repairLeaveApprovalMapper.selectOne(Wrappers.lambdaQuery(RepairLeaveApproval.class)
                .eq(RepairLeaveApproval::getId, request.getAuditorId())
                .last("limit 1"));
        if (request.getStatus().equals(LeaveApplyType.LEAVE.name())
                && request.getFlag().equals(AttendanceCheckType.AUDIT_FAILURE.name())) {
            LeaveApplications leaveApplications = leaveApplicationsMapper.selectOne(Wrappers.lambdaQuery(LeaveApplications.class)
                    .eq(LeaveApplications::getId, request.getId()).last("limit 1"));
            leaveApplicationsMapper.updateByPrimaryKeySelective(LeaveApplications.builder()
                    .id(request.getId())
                    .audit(auditType)
                    .auditTime(LocalDateTime.now())
                    .updateTime(LocalDateTime.now())
                    .auditorId(RequestContextHolder.getEmployeeCode())
                    .auditOutcome(request.getReason())
                    .auditor(name)
                    .build());
            repairLeaveApprovalMapper.updateByLeaveId(RepairLeaveApproval.builder()
                    .leaveId(request.getId())
                    .applicationType(LeaveApplyType.LEAVE.name())
                    .leaveType(auditType)
                    .build());
            attendanceSettingService.messageSave(request.getEmployeeCode(),leaveApplications.getEmployeeName(), request.getReason(), MessageContentType.LEAVE_AUDIT_FAILURE, LocalDate.now());
            return;
        }else if (request.getStatus().equals(LeaveApplyType.APPOINT.name())
                && request.getFlag().equals(AttendanceCheckType.AUDIT_FAILURE.name())){
            AgentAttendance agentAttendances = agentAttendanceMapper.selectOne(Wrappers.lambdaQuery(AgentAttendance.class)
                    .eq(AgentAttendance::getId, request.getId()).last("limit 1"));
            agentAttendanceMapper.updateByPrimaryKeySelective(AgentAttendance.builder()
                    .id(request.getId())
                    .audit(auditType)
                    .auditTime(LocalDateTime.now())
                    .updateTime(LocalDateTime.now())
                    .auditorId(RequestContextHolder.getEmployeeCode())
                    .auditor(name)
                    .auditOutcome(request.getReason())
                    .build());
            repairLeaveApprovalMapper.updateByLeaveId(RepairLeaveApproval.builder()
                    .leaveId(request.getId())
                    .applicationType(LeaveApplyType.APPOINT.name())
                    .leaveType(auditType)
                    .build());
                attendanceSettingService.messageSave(request.getEmployeeCode(),agentAttendances.getEmployeeName(),request.getReason(),MessageContentType.REISSUE_AUDIT_FAILURE,LocalDate.now());
                return;
        }else if (request.getStatus().equals(LeaveApplyType.REVOKE.name())
                    && request.getFlag().equals(AttendanceCheckType.AUDIT_FAILURE.name())){
                LeaveRevoke leaveRevoke = leaveRevokeMapper.selectById(request.getId());
                leaveRevokeMapper.updateByPrimaryKeySelective(LeaveRevoke.builder()
                        .id(request.getId())
                        .audit(auditType)
                        .auditTime(LocalDateTime.now())
                        .updateTime(LocalDateTime.now())
                        .auditorId(RequestContextHolder.getEmployeeCode())
                        .auditor(name)
                        .auditOutcome(request.getReason())
                        .build());
                leaveApplicationsMapper.updateByPrimaryKeySelective(LeaveApplications.builder()
                        .revoked(Boolean.FALSE)
                        .updateTime(LocalDateTime.now())
                        .id(leaveRevoke.getLeaveApplicationsId())
                        .build());
            repairLeaveApprovalMapper.updateByLeaveId(RepairLeaveApproval.builder()
                    .leaveId(request.getId())
                    .applicationType(LeaveApplyType.REVOKE.name())
                    .leaveType(auditType)
                    .build());
                attendanceSettingService.messageSave2(request.getEmployeeCode(),request.getReason(),MessageContentType.LEAVE_REVOKE_AUDIT_FAILURE);
                return;
            }



        if (request.getStatus().equals(LeaveApplyType.LEAVE.name())) {
            final String reviewersCode = repairLeaveApproval.getLeaveCode();
            //查找上级
            EmployeeOrgVO employeeOrgVO = employeeOrgApi.queryEmployeeOrgInfo(reviewersCode);
            if (null == employeeOrgVO) {
                log.info("org.queryEmployeeOrgInfo 返回属性为空");
                throw new BadRequestException("没有审核权限");
            }
            if (!RequestContextHolder.getEmployeeCode().equals(employeeOrgVO.getBusinessTeamLeaderCode())
                    && !RequestContextHolder.getEmployeeCode().equals(employeeOrgVO.getBusinessAreaLeaderCode())
                    && !RequestContextHolder.getEmployeeCode().equals(employeeOrgVO.getBusinessDeptLeaderCode())) {
                log.info("当前人员非组长,非部长，非区长,{}", reviewersCode);
                throw new BadRequestException("没有审核权限");
            }

            // 判断当前人员角色
            boolean isAreaLeader = RequestContextHolder.getEmployeeCode().equals(employeeOrgVO.getBusinessAreaLeaderCode());
            boolean isDeptLeader = RequestContextHolder.getEmployeeCode().equals(employeeOrgVO.getBusinessDeptLeaderCode());
            boolean isTeamLeader = RequestContextHolder.getEmployeeCode().equals(employeeOrgVO.getBusinessTeamLeaderCode());

            if (isTeamLeader){
                //如果没有区长没有部长直接提交量子
                if (StringUtils.isEmpty(employeeOrgVO.getBusinessAreaLeaderCode())
                        && StringUtils.isEmpty(employeeOrgVO.getBusinessDeptLeaderCode())){
                    repairLeaveApprovalMapper.updateByPrimaryKeySelective(RepairLeaveApproval.builder()
                            .id(repairLeaveApproval.getId())
                            .sub(Boolean.FALSE)
                            .leaveType(AttendanceCheckType.UNDER_REVIEW.name())
                            .build());
                    attendanceSettingService.applicationForm(request.getId(),LeaveApplyType.LEAVE);
                }else{
                    leaveApplicationsMapper.updateByPrimaryKeySelective(LeaveApplications.builder()
                            .id(request.getId())
                            .audit(auditType)
                            .auditTime(LocalDateTime.now())
                            .updateTime(LocalDateTime.now())
                            .auditor(employeeOrgVO.getBusinessDeptLeaderName())
                            .auditorId(employeeOrgVO.getBusinessDeptLeaderCode())
                            .build());
                    repairLeaveApprovalMapper.updateByPrimaryKeySelective(RepairLeaveApproval.builder()
                            .id(repairLeaveApproval.getId())
                            .sub(Boolean.FALSE)
                            .leaveType(AttendanceCheckType.UNDER_REVIEW.name())
                            .build());
                    //加部长审批 判断是否有部长，没有部长直接区长
                    repairLeaveApprovalMapper.insertSelective(RepairLeaveApproval.builder()
                            .applicationType(LeaveApplyType.LEAVE.name())
                            .leaveId(request.getId())
                            .leaveCode(request.getEmployeeCode())
                            .approvalCode(null == employeeOrgVO.getBusinessDeptLeaderCode()
                                    ? employeeOrgVO.getBusinessAreaLeaderCode():employeeOrgVO.getBusinessDeptLeaderCode())
                            .createdTime(LocalDateTime.now())
                            .sub(Boolean.TRUE)
                            .leaveType(AttendanceCheckType.TO_BE_REVIEWED.name())
                            .createdUser(1)
                            .build());
                    attendanceSettingService.messageSaveByLeader(null == employeeOrgVO.getBusinessDeptLeaderCode()
                            ? employeeOrgVO.getBusinessAreaLeaderCode():employeeOrgVO.getBusinessDeptLeaderCode(),MessageContentType.LEADER_LEAVE_COMMIT,employeeOrgVO.getEmployeeName());
                }
            }

            if (isDeptLeader){
                //如果没有区长直接提交量子
                if (StringUtils.isEmpty(employeeOrgVO.getBusinessAreaLeaderCode())) {
                    repairLeaveApprovalMapper.updateByPrimaryKeySelective(RepairLeaveApproval.builder()
                            .id(repairLeaveApproval.getId())
                            .sub(Boolean.FALSE)
                            .leaveType(AttendanceCheckType.UNDER_REVIEW.name())
                            .build());
                    attendanceSettingService.applicationForm(request.getId(), LeaveApplyType.LEAVE);
                }else{
                    leaveApplicationsMapper.updateByPrimaryKeySelective(LeaveApplications.builder()
                            .id(request.getId())
                            .audit(auditType)
                            .auditTime(LocalDateTime.now())
                            .updateTime(LocalDateTime.now())
                            .auditor(employeeOrgVO.getBusinessAreaLeaderName())
                            .auditorId(employeeOrgVO.getBusinessAreaLeaderCode())
                            .build());
                    repairLeaveApprovalMapper.updateByPrimaryKeySelective(RepairLeaveApproval.builder()
                            .id(repairLeaveApproval.getId())
                            .sub(Boolean.FALSE)
                            .leaveType(AttendanceCheckType.UNDER_REVIEW.name())
                            .build());
                    //加区长审批
                    repairLeaveApprovalMapper.insertSelective(RepairLeaveApproval.builder()
                            .applicationType(LeaveApplyType.LEAVE.name())
                            .leaveId(request.getId())
                            .leaveType(AttendanceCheckType.TO_BE_REVIEWED.name())
                            .leaveCode(request.getEmployeeCode())
                            .approvalCode(employeeOrgVO.getBusinessAreaLeaderCode())
                            .createdTime(LocalDateTime.now())
                            .createdUser(1)
                            .sub(Boolean.TRUE)
                            .build());
                    attendanceSettingService.messageSaveByLeader(employeeOrgVO.getBusinessAreaLeaderCode(),MessageContentType.LEADER_LEAVE_COMMIT,employeeOrgVO.getEmployeeName());
                }
            }

            if (isAreaLeader){
                repairLeaveApprovalMapper.updateByPrimaryKeySelective(RepairLeaveApproval.builder()
                        .id(repairLeaveApproval.getId())
                        .sub(Boolean.FALSE)
                        .leaveType(AttendanceCheckType.UNDER_REVIEW.name())
                        .build());
                attendanceSettingService.applicationForm(request.getId(),LeaveApplyType.LEAVE);
            }
        }
        else if (request.getStatus().equals(LeaveApplyType.REVOKE.name())) {
            final String reviewersCode = repairLeaveApproval.getLeaveCode();
            //查找上级
            EmployeeOrgVO employeeOrgVO = employeeOrgApi.queryEmployeeOrgInfo(reviewersCode);
            if (null == employeeOrgVO) {
                log.info("org.queryEmployeeOrgInfo 返回属性为空");
                throw new BadRequestException("没有审核权限");
            }
            if (!RequestContextHolder.getEmployeeCode().equals(employeeOrgVO.getBusinessTeamLeaderCode())
                    && !RequestContextHolder.getEmployeeCode().equals(employeeOrgVO.getBusinessAreaLeaderCode())
                    && !RequestContextHolder.getEmployeeCode().equals(employeeOrgVO.getBusinessDeptLeaderCode())) {
                log.info("当前人员非组长,非部长，非区长,{}", reviewersCode);
                throw new BadRequestException("没有审核权限");
            }

            // 判断当前人员角色
            boolean isAreaLeader = RequestContextHolder.getEmployeeCode().equals(employeeOrgVO.getBusinessAreaLeaderCode());
            boolean isDeptLeader = RequestContextHolder.getEmployeeCode().equals(employeeOrgVO.getBusinessDeptLeaderCode());
            boolean isTeamLeader = RequestContextHolder.getEmployeeCode().equals(employeeOrgVO.getBusinessTeamLeaderCode());

            if (isTeamLeader){
                //如果没有区长没有部长直接提交量子
                if (StringUtils.isEmpty(employeeOrgVO.getBusinessAreaLeaderCode())
                        && StringUtils.isEmpty(employeeOrgVO.getBusinessDeptLeaderCode())){
                    repairLeaveApprovalMapper.updateByPrimaryKeySelective(RepairLeaveApproval.builder()
                            .id(repairLeaveApproval.getId())
                            .sub(Boolean.FALSE)
                            .leaveType(AttendanceCheckType.PASS_THE_AUDIT.name())
                            .build());
                    leaveRevokeMapper.updateByPrimaryKeySelective(LeaveRevoke.builder()
                            .id(request.getId())
                            .audit(AttendanceCheckType.PASS_THE_AUDIT.name())
                            .auditor("流程结束")
                            .build());
                    //说明最后了需要拆分
                    auditRevoke(AuditRequest.builder().id(request.getId()).flag("1").build());
                    splitRecord(request.getId());
                    attendanceSettingService.applicationForm(request.getId(),LeaveApplyType.REVOKE);
                }else{
                    leaveRevokeMapper.updateByPrimaryKeySelective(LeaveRevoke.builder()
                            .id(request.getId())
                            .audit(auditType)
                            .auditTime(LocalDateTime.now())
                            .updateTime(LocalDateTime.now())
                            .auditor(employeeOrgVO.getBusinessDeptLeaderName())
                            .auditorId(employeeOrgVO.getBusinessDeptLeaderCode())
                            .build());
                    repairLeaveApprovalMapper.updateByPrimaryKeySelective(RepairLeaveApproval.builder()
                            .id(repairLeaveApproval.getId())
                            .sub(Boolean.FALSE)
                            .leaveType(AttendanceCheckType.UNDER_REVIEW.name())
                            .build());
                    //加部长审批 判断是否有部长，没有部长直接区长
                    repairLeaveApprovalMapper.insertSelective(RepairLeaveApproval.builder()
                            .applicationType(LeaveApplyType.REVOKE.name())
                            .leaveId(request.getId())
                            .leaveCode(request.getEmployeeCode())
                            .approvalCode(null == employeeOrgVO.getBusinessDeptLeaderCode()
                                    ? employeeOrgVO.getBusinessAreaLeaderCode():employeeOrgVO.getBusinessDeptLeaderCode())
                            .createdTime(LocalDateTime.now())
                            .sub(Boolean.TRUE)
                            .leaveType(AttendanceCheckType.TO_BE_REVIEWED.name())
                            .createdUser(1)
                            .build());
                    attendanceSettingService.messageSaveByLeader(null == employeeOrgVO.getBusinessDeptLeaderCode()
                            ? employeeOrgVO.getBusinessAreaLeaderCode():employeeOrgVO.getBusinessDeptLeaderCode(),MessageContentType.LEADER_LEAVE_REVOKE_COMMIT,employeeOrgVO.getEmployeeName());
                }
            }

            if (isDeptLeader){
                //如果没有区长直接提交量子
                if (StringUtils.isEmpty(employeeOrgVO.getBusinessAreaLeaderCode())) {
                    repairLeaveApprovalMapper.updateByPrimaryKeySelective(RepairLeaveApproval.builder()
                            .id(repairLeaveApproval.getId())
                            .sub(Boolean.FALSE)
                            .leaveType(AttendanceCheckType.PASS_THE_AUDIT.name())
                            .build());
                    leaveRevokeMapper.updateByPrimaryKeySelective(LeaveRevoke.builder()
                            .id(request.getId())
                            .auditor("流程结束")
                            .updateTime(LocalDateTime.now())
                            .audit(AttendanceCheckType.PASS_THE_AUDIT.name())
                            .build());
                    //说明最后了需要拆分
                    auditRevoke(AuditRequest.builder().id(request.getId()).flag("1").build());
                    splitRecord(request.getId());
                    attendanceSettingService.applicationForm(request.getId(), LeaveApplyType.REVOKE);
                }else{
                    leaveRevokeMapper.updateByPrimaryKeySelective(LeaveRevoke.builder()
                            .id(request.getId())
                            .audit(auditType)
                            .auditTime(LocalDateTime.now())
                            .updateTime(LocalDateTime.now())
                            .auditor(employeeOrgVO.getBusinessAreaLeaderName())
                            .auditorId(employeeOrgVO.getBusinessAreaLeaderCode())
                            .build());
                    repairLeaveApprovalMapper.updateByPrimaryKeySelective(RepairLeaveApproval.builder()
                            .id(repairLeaveApproval.getId())
                            .sub(Boolean.FALSE)
                            .leaveType(AttendanceCheckType.UNDER_REVIEW.name())
                            .build());
                    //加区长审批
                    repairLeaveApprovalMapper.insertSelective(RepairLeaveApproval.builder()
                            .applicationType(LeaveApplyType.REVOKE.name())
                            .leaveId(request.getId())
                            .leaveType(AttendanceCheckType.TO_BE_REVIEWED.name())
                            .leaveCode(request.getEmployeeCode())
                            .approvalCode(employeeOrgVO.getBusinessAreaLeaderCode())
                            .createdTime(LocalDateTime.now())
                            .createdUser(1)
                            .sub(Boolean.TRUE)
                            .build());
                    attendanceSettingService.messageSaveByLeader(employeeOrgVO.getBusinessAreaLeaderCode(),MessageContentType.LEADER_LEAVE_REVOKE_COMMIT,employeeOrgVO.getEmployeeName());
                }
            }

            if (isAreaLeader){
                leaveRevokeMapper.updateByPrimaryKeySelective(LeaveRevoke.builder()
                        .id(request.getId())
                        .auditor("流程结束")
                        .updateTime(LocalDateTime.now())
                        .audit(AttendanceCheckType.PASS_THE_AUDIT.name())
                        .build());
                repairLeaveApprovalMapper.updateByPrimaryKeySelective(RepairLeaveApproval.builder()
                        .id(repairLeaveApproval.getId())
                        .sub(Boolean.FALSE)
                        .leaveType(AttendanceCheckType.PASS_THE_AUDIT.name())
                        .build());
                //说明最后了需要拆分
                auditRevoke(AuditRequest.builder().id(request.getId()).flag("1").build());
                splitRecord(request.getId());
                attendanceSettingService.applicationForm(request.getId(),LeaveApplyType.REVOKE);
            }
        }
        else{
            final String reviewersCode = repairLeaveApproval.getLeaveCode();
            //查找上级
            EmployeeOrgVO employeeOrgVO = employeeOrgApi.queryEmployeeOrgInfo(reviewersCode);
            if (null == employeeOrgVO) {
                log.info("org.queryEmployeeOrgInfo 返回属性为空");
                throw new BadRequestException("没有审核权限");
            }


            // 判断当前人员角色
            boolean isAreaLeader = RequestContextHolder.getEmployeeCode().equals(employeeOrgVO.getBusinessAreaLeaderCode());
            boolean isDeptLeader = RequestContextHolder.getEmployeeCode().equals(employeeOrgVO.getBusinessDeptLeaderCode());
            boolean isTeamLeader = RequestContextHolder.getEmployeeCode().equals(employeeOrgVO.getBusinessTeamLeaderCode());

            if (isTeamLeader){
                if (StringUtils.isEmpty(employeeOrgVO.getBusinessAreaLeaderCode()) && StringUtils.isEmpty(employeeOrgVO.getBusinessDeptLeaderCode())){
                    repairLeaveApprovalMapper.updateByPrimaryKeySelective(RepairLeaveApproval.builder()
                            .id(repairLeaveApproval.getId())
                            .sub(Boolean.FALSE)
                            .leaveType(AttendanceCheckType.UNDER_REVIEW.name())
                            .build());
                    attendanceSettingService.applicationForm(request.getId(),LeaveApplyType.APPOINT);
                }else {
                    agentAttendanceMapper.updateByPrimaryKeySelective(AgentAttendance.builder()
                            .id(request.getId())
                            .audit(auditType)
                            .auditTime(LocalDateTime.now())
                            .updateTime(LocalDateTime.now())
                            .auditor(employeeOrgVO.getBusinessDeptLeaderName())
                            .auditorId(employeeOrgVO.getBusinessDeptLeaderCode())
                            .build());
                    repairLeaveApprovalMapper.updateByPrimaryKeySelective(RepairLeaveApproval.builder()
                            .id(repairLeaveApproval.getId())
                            .sub(Boolean.FALSE)
                            .leaveType(AttendanceCheckType.UNDER_REVIEW.name())
                            .build());
                    repairLeaveApprovalMapper.insertSelective(RepairLeaveApproval.builder()
                            .applicationType(LeaveApplyType.APPOINT.name())
                            .leaveId(request.getId())
                            .leaveType(AttendanceCheckType.TO_BE_REVIEWED.name())
                            .leaveCode(request.getEmployeeCode())
                            .approvalCode(null == employeeOrgVO.getBusinessDeptLeaderCode()
                                    ? employeeOrgVO.getBusinessAreaLeaderCode():employeeOrgVO.getBusinessDeptLeaderCode())
                            .createdTime(LocalDateTime.now())
                            .createdUser(1)
                            .sub(Boolean.TRUE)
                            .build());
                    attendanceSettingService.messageSaveByLeader(null == employeeOrgVO.getBusinessDeptLeaderCode()
                            ? employeeOrgVO.getBusinessAreaLeaderCode():employeeOrgVO.getBusinessDeptLeaderCode(),MessageContentType.LEADER_APPOINT_COMMIT,employeeOrgVO.getEmployeeName());
                }
            }

            if (isDeptLeader){
                //如果没有区长直接提交量子
                if (StringUtils.isEmpty(employeeOrgVO.getBusinessAreaLeaderCode())) {
                    repairLeaveApprovalMapper.updateByPrimaryKeySelective(RepairLeaveApproval.builder()
                            .id(repairLeaveApproval.getId())
                            .sub(Boolean.FALSE)
                            .leaveType(AttendanceCheckType.UNDER_REVIEW.name())
                            .build());
                    attendanceSettingService.applicationForm(request.getId(), LeaveApplyType.APPOINT);
                }else {
                    agentAttendanceMapper.updateByPrimaryKeySelective(AgentAttendance.builder()
                            .id(request.getId())
                            .audit(auditType)
                            .auditTime(LocalDateTime.now())
                            .updateTime(LocalDateTime.now())
                            .auditor(employeeOrgVO.getBusinessAreaLeaderName())
                            .auditorId(employeeOrgVO.getBusinessAreaLeaderCode())
                            .build());
                    repairLeaveApprovalMapper.updateByPrimaryKeySelective(RepairLeaveApproval.builder()
                            .id(repairLeaveApproval.getId())
                            .sub(Boolean.FALSE)
                            .leaveType(AttendanceCheckType.UNDER_REVIEW.name())
                            .build());
                    repairLeaveApprovalMapper.insertSelective(RepairLeaveApproval.builder()
                            .applicationType(LeaveApplyType.APPOINT.name())
                            .leaveId(request.getId())
                            .leaveType(AttendanceCheckType.TO_BE_REVIEWED.name())
                            .leaveCode(request.getEmployeeCode())
                            .approvalCode(employeeOrgVO.getBusinessAreaLeaderCode())
                            .createdTime(LocalDateTime.now())
                            .createdUser(1)
                            .sub(Boolean.TRUE)
                            .build());
                    attendanceSettingService.messageSaveByLeader(employeeOrgVO.getBusinessAreaLeaderCode(),MessageContentType.LEADER_APPOINT_COMMIT,employeeOrgVO.getEmployeeName());

                }
            }

            if (isAreaLeader){
                repairLeaveApprovalMapper.updateByPrimaryKeySelective(RepairLeaveApproval.builder()
                        .id(repairLeaveApproval.getId())
                        .sub(Boolean.FALSE)
                        .leaveType(AttendanceCheckType.UNDER_REVIEW.name())
                        .build());
                attendanceSettingService.applicationForm(request.getId(),LeaveApplyType.APPOINT);
            }
        }
    }

    @Override
    public void jobSupplement() {
        final List<LeaveApplications> leaveApplications = leaveApplicationsMapper.selectList(new LambdaQueryWrapper<LeaveApplications>()
                .eq(LeaveApplications::getCompanyCode,"P00001")
                .eq(LeaveApplications::getRevoked,Boolean.FALSE)
                .eq(LeaveApplications::getAudit, AttendanceCheckType.TO_BE_REVIEWED.name()));
        log.info("请假待审核数据：{}",JsonUtil.toJSON(leaveApplications));

        final List<AgentAttendance> agentAttendances = agentAttendanceMapper.selectList(new LambdaQueryWrapper<AgentAttendance>()
                .eq(AgentAttendance::getAudit, AttendanceCheckType.TO_BE_REVIEWED.name())
                .like(AgentAttendance::getOrgCode, "%P00001%")
                .like(AgentAttendance::getAuditType, "%APPOINT_HALF%"));
        log.info("补卡待审核数据：{}",JsonUtil.toJSON(agentAttendances));

        final List<RepairLeaveApproval> leaveApprovals = repairLeaveApprovalMapper.selectList(new LambdaQueryWrapper<RepairLeaveApproval>()
                .eq(RepairLeaveApproval::getApplicationType, LeaveApplyType.LEAVE.name())
                .eq(RepairLeaveApproval::getLeaveType, AttendanceCheckType.TO_BE_REVIEWED.name())
                .eq(RepairLeaveApproval::getSub, Boolean.TRUE));
        log.info("请假待审核数据：{}",JsonUtil.toJSON(leaveApprovals));

        final List<RepairLeaveApproval> appoints = repairLeaveApprovalMapper.selectList(new LambdaQueryWrapper<RepairLeaveApproval>()
                        .eq(RepairLeaveApproval::getApplicationType, LeaveApplyType.APPOINT.name())
                        .eq(RepairLeaveApproval::getLeaveType, AttendanceCheckType.TO_BE_REVIEWED.name())
                        .eq(RepairLeaveApproval::getSub, Boolean.TRUE));
        log.info("补卡待审核数据：{}",JsonUtil.toJSON(appoints));

        final List<Long> leaveApprovalsCollect = leaveApprovals.stream().map(RepairLeaveApproval::getLeaveId).collect(Collectors.toList());
        final List<Long> appointsCollect = appoints.stream().map(RepairLeaveApproval::getLeaveId).collect(Collectors.toList());

        //leaveApplications - leaveApprovalsCollect  agentAttendances - appointsCollect
        leaveApplications.removeIf(x -> leaveApprovalsCollect.contains(x.getId()));
        agentAttendances.removeIf(x -> appointsCollect.contains(x.getId()));

        //这两个list分批调用接口，每一条数据间隔3秒 记录调用批次
        AtomicInteger atomicInteger = new AtomicInteger(0);
        AtomicInteger atomicInteger2 = new AtomicInteger(0);
        leaveApplications.forEach(x -> {
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            log.info("请假批次：{}",atomicInteger.incrementAndGet());
            attendanceSettingService.applicationForm(x.getId(), LeaveApplyType.LEAVE);
        });
        agentAttendances.forEach(x -> {
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            log.info("补卡批次：{}",atomicInteger2.incrementAndGet());
            attendanceSettingService.applicationForm(x.getId(), LeaveApplyType.APPOINT);
        });
    }

    public void compress(){
        ExecutorService executorService = Executors.newFixedThreadPool(3);

        CompletableFuture<Void> ThreadA = CompletableFuture.runAsync(() ->{
            log.info("请假图片压缩线程开始");
            final List<LeaveApplications> leaveApplications = leaveApplicationsMapper.selectList(new LambdaQueryWrapper<LeaveApplications>()
                    .eq(LeaveApplications::getCompanyCode,"P00001")
                    .eq(LeaveApplications::getRevoked,Boolean.FALSE)
                    .eq(LeaveApplications::getAudit, AttendanceCheckType.TO_BE_REVIEWED.name()));
            final List<AgentImage> leaveImages = agentImageMapper.selectList(new LambdaQueryWrapper<AgentImage>()
                    .eq(AgentImage::getImageType, ImageType.LEAVE_IMG.name())
                    .in(AgentImage::getImageKey, leaveApplications.stream().map(LeaveApplications::getId).collect(Collectors.toList()))
            );
            List<AgentImage> leaveApplicationList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(leaveImages)) {
                leaveImages.forEach(poster -> {
                    try {
                        leaveApplicationList.add(AgentImage.builder()
                                .imageKey(poster.getImageKey())
                                .imageBytes(PictureCompressionUtil.compressPic(poster.getImageUrl()))
                                .build());
                    } catch (IOException e) {
                        log.error("压缩图片异常：",e);
                    }
                });

                leaveApplicationList.forEach(info ->{
                    FileGetUrlsVO fileGetUrlsVO = fileApi.upload4Base64(FileBase64Request
                            .builder()
                            .fileName(UUID.randomUUID() + ".jpg")
                            .source("admin")
                            .filePath("/invitation/excel")
                            .fileBase64String(Base64.encode(info.getImageBytes()))
                            .build());
                    agentImageMapper.updateImage(fileGetUrlsVO.getFileUrlList().get(0).getOuterUrl(),info.getImageKey(),ImageType.LEAVE_IMG.name());
                });
            }
            log.info("请假图片压缩线程结束");
        },executorService);

        CompletableFuture<Void> ThreadB = CompletableFuture.runAsync(() ->{
            log.info("补卡图片压缩线程开始");

            final List<AgentAttendance> agentAttendances = agentAttendanceMapper.selectList(new LambdaQueryWrapper<AgentAttendance>()
                    .eq(AgentAttendance::getAudit, AttendanceCheckType.TO_BE_REVIEWED.name())
                    .like(AgentAttendance::getOrgCode, "%P00001%")
                    .like(AgentAttendance::getAuditType, "%APPOINT_HALF%"));
            List<AgentImage> agentAttendanceList = new ArrayList<>();
            final List<AgentImage> attendImages = agentImageMapper.selectList(new LambdaQueryWrapper<AgentImage>()
                    .eq(AgentImage::getImageType, ImageType.APPOINT_IMG.name())
                    .in(AgentImage::getImageKey, agentAttendances.stream().map(AgentAttendance::getId).collect(Collectors.toList()))
            );
            if (CollectionUtils.isNotEmpty(attendImages)) {
                attendImages.forEach(poster -> {
                    try {
                        agentAttendanceList.add(AgentImage.builder()
                                .imageKey(poster.getImageKey())
                                .imageBytes(PictureCompressionUtil.compressPic(poster.getImageUrl()))
                                .build());
                    } catch (IOException e) {
                        log.error("压缩图片异常：",e);
                    }
                });

                agentAttendanceList.forEach(info ->{
                    FileGetUrlsVO fileGetUrlsVO = fileApi.upload4Base64(FileBase64Request
                            .builder()
                            .fileName(UUID.randomUUID() + ".jpg")
                            .source("admin")
                            .filePath("/invitation/excel")
                            .fileBase64String(Base64.encode(info.getImageBytes()))
                            .build());
                    agentImageMapper.updateImage(fileGetUrlsVO.getFileUrlList().get(0).getOuterUrl(),info.getImageKey(),ImageType.APPOINT_IMG.name());
                });
            }
            log.info("补卡图片压缩线程结束");
        },executorService);

        CompletableFuture.allOf(ThreadA, ThreadB).join();

        executorService.shutdown();
        log.info("图片压缩全部线程结束");

    }

    @Override
    public void addRevoke(LeaveRevokeAddRequest request) {
        final EmployeeVO emp = employeeApi.getEmployeeByEmployeeCode(AgentOrgType.valueOf(RequestContextHolder.getOrgType())
                , RequestContextHolder.getEmployeeCode());
        LocalDateTime beginTime = getBeginTime(request.getBeginTime());
        LocalDateTime endTime = getEndTime(request.getEndTime());

        if (beginTime.isAfter(endTime)) {
            throw new ApiException(400, "开始日期不可晚于结束日期");
        }

        LeaveApplications leaveApplications = leaveApplicationsMapper.selectById(request.getLeaveApplicationsId());
        LocalDateTime begin = beginTime.getHour() == 12 ? beginTime.plus(Duration.ofSeconds(1)) : beginTime;
        LocalDateTime dateTime = endTime.getHour() == 12 ? endTime.minus(Duration.ofSeconds(1)) : endTime;

        if (!((begin.isEqual(leaveApplications.getBeginTime()) || begin.isAfter(leaveApplications.getBeginTime())) && (dateTime.isEqual(leaveApplications.getEndTime()) || dateTime.isBefore(leaveApplications.getEndTime())))) {
            throw new BadRequestException("时间选择有误，请确认");
        }


        LeaveRevoke leaveRevoke = new LeaveRevoke();
        leaveRevoke.setLeaveApplicationsId(request.getLeaveApplicationsId());
        leaveRevoke.setBeginTime(begin);
        leaveRevoke.setEndTime(dateTime);
        leaveRevoke.setDuration(request.getDuration());
        leaveRevoke.setExplainDetails(request.getExplainDetails());
        leaveRevoke.setLeaveType(request.getLeaveType());
        leaveRevoke.setEmployeeCode(emp.getCode());
        leaveRevoke.setEmployeeName(emp.getName());
        leaveRevoke.setCompanyCode(emp.getTopCode());
        leaveRevoke.setCompanyName(emp.getTopName());
        leaveRevoke.setCompanyInstCode(emp.getOrgCode());
        leaveRevoke.setCompanyInstName(emp.getOrgName());
        leaveRevoke.setOrgType(emp.getOrgType().name());
        leaveRevoke.setAudit(AttendanceCheckType.TO_BE_REVIEWED.name());
        leaveRevoke.setApplyTime(LocalDateTime.now());
        leaveRevoke.setDeleted(Boolean.FALSE);
        leaveRevokeMapper.insert(leaveRevoke);
        Long id = leaveRevoke.getId();


        RepairLeaveApproval repBuild = new RepairLeaveApproval();
        boolean lzFlag = false;
        final String employeeCode = RequestContextHolder.getEmployeeCode();
        if ("BG".equals(request.getOrgTypeStr()) && !employeeCode.startsWith("S")) {
            EmployeeOrgVO employeeOrgVO = employeeOrgApi.queryEmployeeOrgInfo(employeeCode);
            //没有上级的情况
            lzFlag = null == employeeOrgVO ||
                    (StringUtils.isEmpty(employeeOrgVO.getBusinessDeptLeaderCode()) &&
                            StringUtils.isEmpty(employeeOrgVO.getBusinessAreaLeaderCode()) &&
                            StringUtils.isEmpty(employeeOrgVO.getBusinessTeamLeaderCode())
                    );
            if (!lzFlag) {
                //判断申请人角色
                boolean isAreaLeader1 = employeeCode.equals(employeeOrgVO.getBusinessAreaLeaderCode());
                boolean isDeptLeader1 = employeeCode.equals(employeeOrgVO.getBusinessDeptLeaderCode());
                boolean isTeamLeader1 = employeeCode.equals(employeeOrgVO.getBusinessTeamLeaderCode());

                //申请人是区长，直接提交量子
                if (isAreaLeader1){
                    leaveRevokeMapper.updateByPrimaryKeySelective(LeaveRevoke.builder()
                            .id(id)
                            .audit(AttendanceCheckType.PASS_THE_AUDIT.name())
                            .auditTime(LocalDateTime.now())
                            .auditor("流程结束")
                            .build());
                    //说明最后了需要拆分
                    auditRevoke(AuditRequest.builder().id(id).flag("1").build());
                    splitRecord(id);
                    attendanceSettingService.applicationForm(id,LeaveApplyType.REVOKE);
                    return;
                }
                //申请人是部长，没有区长
                if (isDeptLeader1 && StringUtils.isEmpty(employeeOrgVO.getBusinessAreaLeaderCode())){
                    leaveRevokeMapper.updateByPrimaryKeySelective(LeaveRevoke.builder()
                            .id(id)
                            .audit(AttendanceCheckType.PASS_THE_AUDIT.name())
                            .auditTime(LocalDateTime.now())
                            .auditor("流程结束")
                            .build());
                    //说明最后了需要拆分
                    auditRevoke(AuditRequest.builder().id(id).flag("1").build());
                    splitRecord(id);
                    attendanceSettingService.applicationForm(id,LeaveApplyType.REVOKE);
                    return;
                }
                //申请人是组长，没有区长部长
                if (isTeamLeader1 && StringUtils.isEmpty(employeeOrgVO.getBusinessAreaLeaderCode())
                        && StringUtils.isEmpty(employeeOrgVO.getBusinessDeptLeaderCode())){
                    leaveRevokeMapper.updateByPrimaryKeySelective(LeaveRevoke.builder()
                            .id(id)
                            .audit(AttendanceCheckType.PASS_THE_AUDIT.name())
                            .auditTime(LocalDateTime.now())
                            .auditor("流程结束")
                            .build());
                    //说明最后了需要拆分
                    auditRevoke(AuditRequest.builder().id(id).flag("1").build());
                    splitRecord(id);
                    attendanceSettingService.applicationForm(id,LeaveApplyType.REVOKE);
                    return;
                }


                //有组长的情况
                if ((StringUtils.isNotEmpty(employeeOrgVO.getBusinessTeamLeaderCode())) && !isTeamLeader1) {
                    leaveRevoke.setAuditor(employeeOrgVO.getBusinessTeamLeaderName());
                    leaveRevoke.setAuditorId(employeeOrgVO.getBusinessTeamLeaderCode());
                    repBuild = RepairLeaveApproval.builder()
                            .leaveId(id)
                            .applicationType(LeaveApplyType.REVOKE.name())
                            .leaveType(AttendanceCheckType.TO_BE_REVIEWED.name())
                            .leaveCode(RequestContextHolder.getEmployeeCode())
                            .approvalCode(employeeOrgVO.getBusinessTeamLeaderCode())
                            .createdTime(LocalDateTime.now())
                            .createdUser(1)
                            .sub(Boolean.TRUE)
                            .build();
                    attendanceSettingService.messageSaveByLeader(employeeOrgVO.getBusinessTeamLeaderCode(), MessageContentType.LEADER_LEAVE_REVOKE_COMMIT, emp.getName());
                }else
                    //有部长
                    if (StringUtils.isNotEmpty(employeeOrgVO.getBusinessDeptLeaderCode()) && !isDeptLeader1) {
                        leaveRevoke.setAuditor(employeeOrgVO.getBusinessDeptLeaderName());
                        leaveRevoke.setAuditorId(employeeOrgVO.getBusinessDeptLeaderCode());
                        repBuild = RepairLeaveApproval.builder()
                                .leaveId(id)
                                .applicationType(LeaveApplyType.REVOKE.name())
                                .leaveType(AttendanceCheckType.TO_BE_REVIEWED.name())
                                .leaveCode(RequestContextHolder.getEmployeeCode())
                                .approvalCode(employeeOrgVO.getBusinessDeptLeaderCode())
                                .createdTime(LocalDateTime.now())
                                .createdUser(1)
                                .sub(Boolean.TRUE)
                                .build();
                        attendanceSettingService.messageSaveByLeader(employeeOrgVO.getBusinessDeptLeaderCode(), MessageContentType.LEADER_LEAVE_REVOKE_COMMIT, employeeOrgVO.getEmployeeName());

                    }else
                        //有区长
                        if (StringUtils.isNotEmpty(employeeOrgVO.getBusinessAreaLeaderCode())) {
                            leaveRevoke.setAuditor(employeeOrgVO.getBusinessAreaLeaderName());
                            leaveRevoke.setAuditorId(employeeOrgVO.getBusinessAreaLeaderCode());
                            repBuild = RepairLeaveApproval.builder()
                                    .leaveId(id)
                                    .applicationType(LeaveApplyType.REVOKE.name())
                                    .leaveType(AttendanceCheckType.TO_BE_REVIEWED.name())
                                    .leaveCode(RequestContextHolder.getEmployeeCode())
                                    .approvalCode(employeeOrgVO.getBusinessAreaLeaderCode())
                                    .createdTime(LocalDateTime.now())
                                    .createdUser(1)
                                    .sub(Boolean.TRUE)
                                    .build();
                            attendanceSettingService.messageSaveByLeader(employeeOrgVO.getBusinessAreaLeaderCode(), MessageContentType.LEADER_LEAVE_REVOKE_COMMIT, emp.getName());

                        }
            }

        }
        //调用量子
        if ("BG".equals(request.getOrgTypeStr()) && !employeeCode.startsWith("S")){
            if (lzFlag){
                leaveRevokeMapper.updateByPrimaryKeySelective(LeaveRevoke.builder()
                        .id(id)
                        .audit(AttendanceCheckType.PASS_THE_AUDIT.name())
                        .auditTime(LocalDateTime.now())
                        .auditor("流程结束")
                        .build());
                //说明最后了需要拆分
                auditRevoke(AuditRequest.builder().id(id).flag("1").build());
                splitRecord(id);
                attendanceSettingService.applicationForm(id,LeaveApplyType.REVOKE);
            }else{
                leaveRevokeMapper.updateByPrimaryKeySelective(leaveRevoke);
                repairLeaveApprovalMapper.insertSelective(repBuild);
            }
        }
    }

    /**  开始时间只有大于等于  结束日期只有小于等于
     *1.如果这个申请记录的 开始时间等于原纪录的开始时间 那么拆分的记录开始时间为新申请的结束时间
     *  如果结束日期相等就不新增记录
     *
     *2.如果这个申请记录的 开始时间大于原纪录的申请时间
     *    拆分为两条记录  那么拆分的记录开始时间为原开始时间  结束时间为新申请日期的开始时间
     *                  如果结束日期相等就不新增第二条记录
     *    第二条  拆分的记录开始时间为新申请的结束时间  结束时间为原纪录的结束时间
     *
     */
    void splitRecord(Long revokeId) {

        List<LeaveApplications> leaveList = new ArrayList<>();

        LeaveRevoke leaveRevoke = leaveRevokeMapper.selectById(revokeId);
        LeaveApplications leaveApplications = leaveApplicationsMapper.selectById(leaveRevoke.getLeaveApplicationsId());
        List<AgentImage> agentImages = agentImageMapper.selectList(Wrappers.lambdaQuery(AgentImage.class)
                .eq(AgentImage::getImageKey, leaveApplications.getId())
                .eq(AgentImage::getImageType, ImageType.LEAVE_IMG.name()));

        LocalDateTime newBeginTime = leaveRevoke.getBeginTime();
        LocalDateTime newEndTime = leaveRevoke.getEndTime();

        LocalDateTime originalBeginTime = leaveApplications.getBeginTime();
        LocalDateTime originalEndTime = leaveApplications.getEndTime();

        if (!(newBeginTime.isEqual(originalBeginTime) && newEndTime.isEqual(originalEndTime))){

            if (newBeginTime.isEqual(originalBeginTime)) {
                // 如果新开始时间等于原始开始时间
                if (!newEndTime.isEqual(originalEndTime)) {
                    // 如果新结束时间不等于原始结束时间，新增一条记录
                    LeaveApplications leave = BeanCopier.copyObject(leaveApplications, LeaveApplications.class);
                    leave.setBeginTime(newEndTime);
                    leave.setEndTime(originalEndTime);
                    leaveList.add(leave);
                }
            } else if (newBeginTime.isAfter(originalBeginTime)) {
                // 如果新开始时间晚于原始开始时间，拆分为两条记录
                LeaveApplications one = BeanCopier.copyObject(leaveApplications, LeaveApplications.class);
                one.setBeginTime(originalBeginTime);
                one.setEndTime(newBeginTime);
                leaveList.add(one);
                if (!newEndTime.isEqual(originalEndTime)) {
                    LeaveApplications two = BeanCopier.copyObject(leaveApplications, LeaveApplications.class);
                    // 如果新结束时间不等于原始结束时间，新增第二条记录
                    two.setBeginTime(newEndTime);
                    two.setEndTime(originalEndTime);
                    leaveList.add(two);
                }
            }

            for (LeaveApplications applications : leaveList) {
                LocalDateTime beginTime = applications.getBeginTime();
                LocalDateTime endTime = applications.getEndTime();
                if (endTime.toLocalTime().equals(LocalTime.MIDNIGHT)) {
                    applications.setEndTime(endTime.minusSeconds(1));
                }
                if (endTime.toLocalTime().equals(LocalTime.of(12, 00, 01))) {
                    applications.setEndTime(endTime.minusSeconds(2));
                }
                if (beginTime.toLocalTime().equals(LocalTime.of(23, 59, 59))) {
                    applications.setBeginTime(beginTime.plusSeconds(1));
                }
                if (beginTime.toLocalTime().equals(LocalTime.of(11, 59, 59))) {
                    applications.setBeginTime(beginTime.plusSeconds(2));
                }
                double days = calculateDays(applications.getBeginTime().toLocalDate(), applications.getEndTime().toLocalDate(), amORpm(applications.getBeginTime()).trim(), amORpm(applications.getEndTime()).trim());
                applications.setDuration(String.valueOf(days));
                applications.setId(null);
                applications.setRevoked(Boolean.FALSE);
            }

            leaveApplicationsMapper.insertBatchSomeColumn(leaveList);

            if (CollectionUtils.isNotEmpty(agentImages)){
                List<AgentImage> addImages = new ArrayList<>();
                for (LeaveApplications applications : leaveList) {
                    Long id = applications.getId();
                    for (AgentImage agentImage : agentImages) {
                        AgentImage add = BeanCopier.copyObject(agentImage, AgentImage.class);
                        add.setImageKey(String.valueOf(id));
                        add.setId(null);
                        addImages.add(add);
                    }
                }
                agentImageMapper.insertBatchSomeColumn(addImages);
            }
        }

        leaveApplicationsMapper.updateByPrimaryKeySelective(LeaveApplications.builder().revoked(Boolean.TRUE).id(leaveRevoke.getLeaveApplicationsId()).build());

    }

    void auditRevoke(AuditRequest request) {
        LeaveRevoke leaveRevoke = leaveRevokeMapper.selectById(request.getId());
        //通过
        if (FLAG.equals(request.getFlag())) {
            //销假通过消息
            attendanceSettingService.messageSave2(leaveRevoke.getEmployeeCode(), "", MessageContentType.LEAVE_REVOKE_PASS_THE_AUDIT);
        } else {
            //销假不通过消息
            attendanceSettingService.messageSave2(leaveRevoke.getEmployeeCode(), request.getReason(), MessageContentType.LEAVE_REVOKE_AUDIT_FAILURE);
        }

    }

    public double calculateDays(LocalDate startDate, LocalDate endDate, String hourStart, String hourEnd) {
        double days = 0;
        if (startDate.isAfter(endDate)) {
            days = 0;
        } else if (startDate.isEqual(endDate)) {
            days = 1;
            if (hourStart.equals("上午") && hourEnd.equals("上午")) {
                days -= 0.5;
            } else if (hourStart.equals("下午") && hourEnd.equals("上午")) {
                days -= 1;
            } else if (hourStart.equals("下午") && hourEnd.equals("下午")) {
                days -= 0.5;
            }
        }  else if (startDate != null && endDate != null) {
            days = ChronoUnit.DAYS.between(startDate, endDate);
            if ((hourStart.equals("上午") && hourEnd.equals("上午")) || (hourStart.equals("下午") && hourEnd.equals("下午"))) {
                days += 0.5;
            } else if (hourStart.equals("上午") && hourEnd.equals("下午")) {
                days += 1;
            }
        }
        return days;
    }

    @Override
    public PageInfo<LeaveApplicationsQueryVO> querySubmit(LeaveRevokeQueryRequest request) {
        LocalDate yinBaoStartDate = attendanceSignInImageConfig.getYinBaoStartTime().toInstant().atZone(ZoneId.of("UTC")).toLocalDate();
        Page<LeaveApplicationsQueryVO> pageInfo = leaveApplicationsMapper.querySubmitPage(new Page<>(request.getCurrent(), request.getSize()), request);

        if (CollectionUtils.isNotEmpty(pageInfo.getRecords())){
            DateTimeFormatter
                    formatter = DateTimeFormatter.ofPattern(DatePatterns.YYYY_MM_DD_HH_MM_SS);
            Map<String, List<AgentImage>> signImageMap = new HashMap<>();
            Map<String, List<AgentImage>> leveImageMap = new HashMap<>();
            List<Long> ids = pageInfo.getRecords().stream().map(LeaveApplicationsQueryVO::getId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(ids)) {
                List<AgentImage> agentImages = agentImageMapper.selectList(Wrappers.lambdaQuery(AgentImage.class)
                        .in(AgentImage::getImageKey, ids)
                        .in(AgentImage::getImageType, Arrays.asList("APPOINT_IMG", "LEAVE_IMG")));
                leveImageMap = agentImages.stream().filter(x -> x.getImageType().equals(ImageType.LEAVE_IMG.name())).collect(Collectors.groupingBy(AgentImage::getImageKey));
                signImageMap = agentImages.stream().filter(x -> x.getImageType().contains(ImageType.APPOINT_IMG.name())).collect(Collectors.groupingBy(AgentImage::getImageKey));
            }


            for (LeaveApplicationsQueryVO record : pageInfo.getRecords()) {

                switch (record.getType()) {
                    case "APPOINT":
                        imageData(signImageMap, record, record.getId());
                        break;

                    case "LEAVE":
                        imageData(leveImageMap, record, record.getId());
                        makeDetails(formatter, record);
                        LocalDateTime createTime = LocalDateTime.parse(record.getCreateTime(), formatter);
                        if (createTime.toLocalDate().isBefore(yinBaoStartDate)){
                            record.setNewFlag(Boolean.FALSE);
                        }else {
                            record.setNewFlag(Boolean.TRUE);
                        }
                        break;

                    case "REVOKE":
                        makeDetails(formatter, record);
                        LeaveApplications leaveApplications = leaveApplicationsMapper.selectById(record.getLeaveApplicationsId());
                        if (null != leaveApplications) {
                            LeaveApplicationsVO leaveApplicationsVO = BeanCopier.copyObject(leaveApplications, LeaveApplicationsVO.class);
                            LocalDateTime begin = leaveApplicationsVO.getBeginTime();
                            LocalDateTime end = leaveApplicationsVO.getEndTime();
                            List<AgentImage> agentImages = agentImageMapper.selectList(Wrappers.lambdaQuery(AgentImage.class)
                                    .eq(AgentImage::getImageKey, record.getLeaveApplicationsId())
                                    .eq(AgentImage::getImageType, "LEAVE_IMG"));
                            List<String> imageList = new ArrayList<>();
                            agentImages.forEach(x -> imageList.add(x.getImageUrl()));
                            leaveApplicationsVO.setImageList(imageList);
                            String amORpm1 = amORpm2(begin);
                            String amORpm2 = amORpm2(end);
                            leaveApplicationsVO.setStrTime(begin.toLocalDate() + amORpm1);
                            leaveApplicationsVO.setFinishTime(end.toLocalDate() + amORpm2);
                            if (begin.toLocalDate().isEqual(end.toLocalDate()) && !amORpm1.equals(amORpm2) ){
                                leaveApplicationsVO.setStrTime(String.valueOf(begin.toLocalDate()));
                                leaveApplicationsVO.setFinishTime(null);
                            }else if (begin.toLocalDate().isEqual(end.toLocalDate()) && amORpm1.equals(amORpm2)){
                                leaveApplicationsVO.setStrTime(begin.toLocalDate() + amORpm1);
                                leaveApplicationsVO.setFinishTime(null);
                            }
                            record.setLeaveApplicationsVO(leaveApplicationsVO);
                        }
                        break;

                    default:
                        break;
                }
            }
        }

        return PageUtil.convert(pageInfo, revoke -> BeanCopier.copyObject(revoke, LeaveApplicationsQueryVO.class));
    }

    @Override
    public Boolean checkOne(String leaveType, String employeeCode) {
        final EmployeeVO emp = employeeApi.getEmployeeByEmployeeCode(AgentOrgType.valueOf(RequestContextHolder.getOrgType())
                , RequestContextHolder.getEmployeeCode());
        LeaveCheckConfig configuration =leaveCheckConfigMapper.selectOne(new LambdaQueryWrapper<LeaveCheckConfig>()
                .eq(LeaveCheckConfig::getOrgCode, emp.getOrgCode())
                .eq(LeaveCheckConfig::getLeaveType, leaveType)
                .eq(LeaveCheckConfig::getYears,LocalDate.now().getYear())
                .eq(LeaveCheckConfig::getDeleted,Boolean.FALSE)
                .orderByDesc(LeaveCheckConfig::getCreatedTime)
                .last("limit 1"));
        if (configuration == null){
            configuration = leaveCheckConfigMapper.selectOne(new LambdaQueryWrapper<LeaveCheckConfig>()
                    .eq(LeaveCheckConfig::getOrgCode, emp.getOrgCode())
                    .eq(LeaveCheckConfig::getLeaveType,leaveType)
                    .lt(LeaveCheckConfig::getYears,LocalDate.now().getYear())
                    .eq(LeaveCheckConfig::getDeleted, Boolean.FALSE)
                    .orderByDesc(LeaveCheckConfig::getYears)
                    .orderByDesc(LeaveCheckConfig::getCreatedTime)
                    .last("limit 1"));
        }
        if (configuration != null){
            //入职时间
            LocalDateTime entryTime = emp.getEntryTime();
            long between = ChronoUnit.DAYS.between(entryTime, LocalDateTime.now());
            double betweenYears = (double) between/365;

            //在查看是固定配额还是司龄配置
            String quotaConfig = configuration.getQuotaConfig();
            if (FixedQuotaType.FIXED_QUOTA.name().equals(quotaConfig) && FixedQuotaType.SINGLE_COMPLETION.name().equals(configuration.getDaysConfig())){
                return Boolean.TRUE;
            }else if (FixedQuotaType.SENIORITY.name().equals(quotaConfig)){
                //司龄
                List<EntryConfig> entryConfigList = JsonUtil.toList(configuration.getEntryConfig(), List.class, EntryConfig.class);
                //最后一个匹配开区间
                for (int i = 0 ; i < entryConfigList.size() ; i++ ){
                    EntryConfig entryConfig = entryConfigList.get(i);
                    double lessEqual = Double.parseDouble(entryConfig.getLessEqual());
                    double less = Double.parseDouble(entryConfig.getLess());
                    if (i != entryConfigList.size() -1){
                        //说明匹配区间
                        if (lessEqual<= betweenYears && betweenYears < less  && FixedQuotaType.SINGLE_COMPLETION.name().equals(configuration.getDaysConfig())){
                            return Boolean.TRUE;
                        }
                    }else {
                        if (lessEqual<= betweenYears && FixedQuotaType.SINGLE_COMPLETION.name().equals(configuration.getDaysConfig())){
                            return Boolean.TRUE;
                        }
                    }
                }

            }
        }
        return Boolean.FALSE;
    }

    @Override
    public List<EmployeeLeaveQueryVO> employeeLeaveQuery(EmployeeLeaveQueryRequest request) {
        leaveApplicationsMapper.selectList(Wrappers.lambdaQuery(LeaveApplications.class)
                .eq(LeaveApplications::getEmployeeCode, request.getEmployeeCode())
                .eq(LeaveApplications::getLeaveType, request.getLeaveType())
                .eq(LeaveApplications::getRevoked, Boolean.FALSE)
                .nested( nest ->{
                    nest.or(or ->{
                        or.between(LeaveApplications::getBeginTime, request.getBeginTime(), request.getEndTime());
                    }).or(or ->{
                        or.between(LeaveApplications::getBeginTime, request.getBeginTime(), request.getEndTime());
                    })
                })
                .orderByDesc(LeaveApplications::getCreateTime));
        return Collections.emptyList();
    }

    private void makeDetails(DateTimeFormatter formatter, LeaveApplicationsQueryVO record) {
        LocalDateTime begin = LocalDateTime.parse(record.getBeginTime(), formatter);
        LocalDateTime end = LocalDateTime.parse(record.getEndTime(), formatter);
        String amORpm1 = amORpm2(begin);
        String amORpm2 = amORpm2(end);
        record.setBeginTime(begin.toLocalDate() + amORpm1);
        record.setEndTime(end.toLocalDate() + amORpm2);
        if (begin.toLocalDate().isEqual(end.toLocalDate()) && !amORpm1.equals(amORpm2) ){
            record.setBeginTime(String.valueOf(begin.toLocalDate()));
            record.setEndTime(null);
        }else if (begin.toLocalDate().isEqual(end.toLocalDate()) && amORpm1.equals(amORpm2)){
            record.setBeginTime(begin.toLocalDate() + amORpm1);
            record.setEndTime(null);
        }

        //是否能销假
        LocalDate now = LocalDate.now();


        //请假的必须是通过   销假必须是不通过
        if (begin.getYear()==now.getYear()){
            if (begin.getMonthValue() < now.getMonthValue()) {
                //  当前日期大于15日，不允许销上个月及之前的假，屏蔽销假入口
                if (now.getDayOfMonth() >= 15 || begin.getMonthValue() != now.getMonthValue() - 1) {
                    record.setFlag(Boolean.FALSE);
                } else {
                    record.setFlag(Boolean.TRUE);
                }
            } else {
                /*(record.getType().equals(LeaveApplyType.REVOKE.name()) && record.getAudit().equals(AttendanceCheckType.AUDIT_FAILURE.name()))
                        || (*/
                if (record.getType().equals(LeaveApplyType.LEAVE.name()) && record.getAudit().equals(AttendanceCheckType.PASS_THE_AUDIT.name())) {
                    record.setFlag(Boolean.TRUE);
                } else {
                    record.setFlag(Boolean.FALSE);
                }
            }
        }else {
            record.setFlag(Boolean.FALSE);
        }
        //查看是否销假
        if (record.getType().equals(LeaveApplyType.LEAVE.name())){

            LeaveRevoke leaveRevoke = leaveRevokeMapper.selectOne(Wrappers.lambdaQuery(LeaveRevoke.class)
                    .eq(LeaveRevoke::getLeaveApplicationsId, record.getId())
                    .orderByDesc(LeaveRevoke::getId).last("limit 1 "));
            if (leaveRevoke!=null && !leaveRevoke.getAudit().equals(AttendanceCheckType.AUDIT_FAILURE.name())){
                record.setFlag(Boolean.FALSE);
            }
        }


    }
    private Map<String,Double> setDuration(List<LeaveApplications> leaveApplicationsList) {
        Map<String, Double> result = new HashMap<>();
        Double applyCount = 0.0;
        Double duration = 0.0;
        if (CollectionUtils.isNotEmpty(leaveApplicationsList)){
            //审核通过
            List<LeaveApplications> passList = leaveApplicationsList.stream().filter(x -> x.getAudit().equals(AttendanceCheckType.PASS_THE_AUDIT.name())).collect(Collectors.toList());

            for (LeaveApplications applications : leaveApplicationsList) {
                LocalDateTime start = applications.getBeginTime();
                LocalDateTime end = applications.getEndTime();
                if (applications.getBeginTime().getMonthValue() != LocalDate.now().getMonthValue()){
                    //说明是跨月
                    start = LocalDateTime.now().with(TemporalAdjusters.firstDayOfMonth()).with(LocalTime.MIN);
                }
                if (applications.getEndTime().getMonthValue() != LocalDate.now().getMonthValue()){
                    //说明是跨月
                    end = LocalDateTime.now().with(TemporalAdjusters.lastDayOfMonth()).with(LocalTime.MAX);
                }

                double lastDays = calculateDays(start.toLocalDate(), end.toLocalDate(), amORpm(start).trim(), amORpm(end).trim());
                applyCount += lastDays;
            }
            if (CollectionUtils.isNotEmpty(passList)){
                for (LeaveApplications applications : passList) {
                    LocalDateTime start = applications.getBeginTime();
                    LocalDateTime end = applications.getEndTime();
                    if (applications.getBeginTime().getMonthValue() != LocalDate.now().getMonthValue()){
                        //说明是跨月
                        start = LocalDateTime.now().with(TemporalAdjusters.firstDayOfMonth()).with(LocalTime.MIN);
                    }
                    if (applications.getEndTime().getMonthValue() != LocalDate.now().getMonthValue()){
                        //说明是跨月
                        end = LocalDateTime.now().with(TemporalAdjusters.lastDayOfMonth()).with(LocalTime.MAX);
                    }

                    double lastDays = calculateDays(start.toLocalDate(), end.toLocalDate(), amORpm(start).trim(), amORpm(end).trim());
                    duration += lastDays;
                }
            }
        }
        result.put("applyCount",applyCount);
        result.put("duration",duration);
        return result;
    }

    private Double setYearsDuration(List<LeaveApplications> leaveApplicationsList) {
        //所有通过的天数
        Double applyCount = 0.0;
        if (CollectionUtils.isNotEmpty(leaveApplicationsList)){
            //过滤不通过的
            List<LeaveApplications> yearList = leaveApplicationsList.stream().filter(x -> !x.getAudit().equals(AttendanceCheckType.AUDIT_FAILURE.name())).collect(Collectors.toList());

            for (LeaveApplications leaveApplications : yearList) {
                LocalDateTime start = leaveApplications.getBeginTime();
                LocalDateTime end = leaveApplications.getEndTime();

               /* if (start.getYear() != LocalDate.now().getYear()){
                    //说明是跨年
                    start = LocalDateTime.now().with(TemporalAdjusters.firstDayOfYear()).with(LocalTime.MIN);
                }
                if (end.getYear() != LocalDate.now().getYear()){
                    //说明是跨年
                    end = LocalDateTime.now().with(TemporalAdjusters.lastDayOfYear()).with(LocalTime.MAX);
                }
*/
                double lastDays = calculateDays(start.toLocalDate(), end.toLocalDate(), amORpm(start).trim(), amORpm(end).trim());
                applyCount += lastDays;
            }

        }
        return applyCount;
    }

    private Double setMonthsDuration(List<LeaveApplications> leaveApplicationsList) {
        //所有通过的天数
        Double applyCount = 0.0;
        if (CollectionUtils.isNotEmpty(leaveApplicationsList)){
            //过滤不通过的
            List<LeaveApplications> yearList = leaveApplicationsList.stream().filter(x -> !x.getAudit().equals(AttendanceCheckType.AUDIT_FAILURE.name())).collect(Collectors.toList());

            for (LeaveApplications leaveApplications : yearList) {
                LocalDateTime start = leaveApplications.getBeginTime();
                LocalDateTime end = leaveApplications.getEndTime();

                if (start.getMonth() != LocalDate.now().getMonth()){
                    //说明是跨月
                    start = LocalDateTime.now().with(TemporalAdjusters.firstDayOfMonth()).with(LocalTime.MIN);
                }
                if (end.getMonth() != LocalDate.now().getMonth()){
                    //说明是跨月
                    end = LocalDateTime.now().with(TemporalAdjusters.lastDayOfMonth()).with(LocalTime.MAX);
                }

                double lastDays = calculateDays(start.toLocalDate(), end.toLocalDate(), amORpm(start).trim(), amORpm(end).trim());
                applyCount += lastDays;
            }

        }
        return applyCount;
    }
}
