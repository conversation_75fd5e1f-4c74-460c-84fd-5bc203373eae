package com.hqins.agent.postsale.config;

import cn.jpush.api.JPushClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import javax.annotation.PostConstruct;

@Configuration
@ConfigurationProperties
public class JPushConfig {
    @Autowired
    private AppMessageConfig appMessageConfig;

    private JPushClient jPushClient;

    /**
     * 推送客户端
     * @return
     */
    @PostConstruct
    public void initJPushClient() {
        jPushClient = new JPushClient(appMessageConfig.getSecret(), appMessageConfig.getAppKey());
    }

    /**
     * 获取推送客户端
     * @return
     */
    public JPushClient getJPushClient() {
        return jPushClient;
    }
}