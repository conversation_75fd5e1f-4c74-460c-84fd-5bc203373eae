package com.hqins.agent.postsale.web.controller;

import com.hqins.agent.postsale.dto.response.AggMessageVO;
import com.hqins.agent.postsale.model.enums.MessageSource;
import com.hqins.agent.postsale.model.enums.MessageType;
import com.hqins.agent.postsale.model.request.*;
import com.hqins.agent.postsale.model.vo.MessageTitleMainVO;
import com.hqins.agent.postsale.model.vo.MessageVO;
import com.hqins.agent.postsale.model.vo.PopUpWindowVO;
import com.hqins.agent.postsale.model.vo.ViewedTitleVO;
import com.hqins.agent.postsale.service.CoreMessageService;
import com.hqins.agent.postsale.service.MessageService;
import com.hqins.common.base.ApiResult;
import com.hqins.common.base.constants.Strings;
import com.hqins.common.base.enums.AgentOrgType;
import com.hqins.common.base.page.PageInfo;
import com.hqins.common.utils.JsonUtil;
import com.hqins.common.web.RequestContextHolder;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

@Api(tags = "消息表管理")
@RestController
@RequestMapping("/messages")
@Slf4j
public class MessageController {

    @Autowired
    private MessageService messageService;

    @Autowired
    private CoreMessageService coreMessageService;

    @ApiOperation("分页查询消息")
    @GetMapping
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<PageInfo<MessageVO>> list(@ApiParam("来源") @RequestParam("source") MessageSource source,
                                               @ApiParam("消息类型") @RequestParam("messageType") MessageType messageType,
                                               @ApiParam(Strings.CURRENT_PAGE_MSG) @RequestParam("current") long current,
                                               @ApiParam(Strings.SIZE_PAGE_MSG) @RequestParam("size") long size) {
        MessageQueryRequest messageRequest = MessageQueryRequest.builder()
                .current(current)
                .size(size)
                .source(source)
                .messageType(messageType)
                .build();
        return ApiResult.ok(messageService.list(messageRequest));
    }

    @ApiOperation("查询未读消息数量")
    @GetMapping("/getViewedCount")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<Integer> getViewedCount(@ApiParam("来源") @RequestParam("source") MessageSource source) {
        return ApiResult.ok(messageService.getViewedCount(source));
    }

    @ApiOperation("添加消息")
    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    public ApiResult<Void> add(@Valid @RequestBody MessageAddRequest request) {
        log.info("添加消息 请求入参为：{}", JsonUtil.toJSON(request));
        messageService.add(request);
        return ApiResult.ok();
    }

    @ApiOperation("批量添加消息")
    @PostMapping("/batchadd")
    @ResponseStatus(HttpStatus.CREATED)
    public ApiResult<Void> batchAdd(@Valid @RequestBody BatchMessageAddRequest request) {
        messageService.batchAdd(request);
        return ApiResult.ok();
    }

    @ApiOperation("删除消息")
    @DeleteMapping("/{id:" + Strings.REGEX_ID + "}/{messageType}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public ApiResult<Void> delete(@ApiParam("消息ID") @PathVariable("id") Long id,@ApiParam("消息类型") @PathVariable("messageType") MessageType messageType) {
        MessageDeletedRequest request = MessageDeletedRequest.builder()
                .id(id)
                .deleted(true)
                .messageType(messageType)
                .build();
        messageService.deleted(request);
        return ApiResult.ok();
    }

    @ApiOperation("发送微信公众号消息")
    @PostMapping("/wxMessage")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<Void> wxMessage(@RequestBody @Valid List<WxMessageRequest> wxMessageRequest) {
        messageService.wxMessage(wxMessageRequest);
        return ApiResult.ok();
    }

    @ApiOperation("获取消息总分组")
    @GetMapping("/message-group/{messageSource}")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<MessageTitleMainVO> messageGroup(@PathVariable("messageSource") MessageSource messageSource) {
        return ApiResult.ok(messageService.messageGroup(RequestContextHolder.getAgentId(),
                RequestContextHolder.getEmployeeCode(),
                AgentOrgType.valueOf(RequestContextHolder.getOrgType()),
                messageSource));
    }

    @ApiOperation("批量已读消息")
    @PutMapping("/viewed")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<Void> viewed(@RequestBody @Valid MessageViewedRequest request) {
        messageService.viewed(request);
        return ApiResult.ok();
    }
    @ApiOperation("根据消息类型已读消息")
    @PutMapping("/viewed/{messageType}/{source}")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<Void> viewed(@PathVariable("messageType") MessageType messageType,@PathVariable("source")MessageSource source) {
        messageService.viewedAllByType(messageType,source);
        return ApiResult.ok();
    }

    @ApiOperation("未读消息展示横幅")
    @GetMapping("/viewed-title/{messageSource}")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<ViewedTitleVO> viewedTitle(@PathVariable("messageSource") MessageSource messageSource) {
        return ApiResult.ok(messageService.viewedTitle(RequestContextHolder.getEmployeeCode(), RequestContextHolder.getAgentId(),messageSource));
    }

    @ApiOperation("聚合消息发送接口")
    @PostMapping("/aggrSend")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<String> aggrSendMsg(@RequestBody  AggrSendMessageRequest aggrSendMessageRequest) {
        return ApiResult.ok(messageService.aggrSendMsg(aggrSendMessageRequest));
    }


    @ApiOperation("孤儿单消息发送")
    @PostMapping("/orphanMessage")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<Void> orphanMessage(@RequestBody  OrphanMessageRequest request) {
        messageService.orphanMessage(request);
        return ApiResult.ok();
    }

    @ApiOperation("首页消息聚合查询")
    @GetMapping("/aggMessages")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<Map<MessageType,AggMessageVO>> aggMessages() {
        return ApiResult.ok(messageService.aggMessages());
    }

    @ApiOperation("核心MQ消息测试接口")
    @GetMapping("/testCoreMQ")
    @ResponseStatus(HttpStatus.OK)
    public String testCoreMQ(@ApiParam("消息业务类型") @RequestParam("messageContentType") String messageContentType,
                                         @ApiParam("mq报文") @RequestParam("jsonStr") String jsonStr) {
        coreMessageService.testCoreMQ( messageContentType, jsonStr);
        return "SUCCESS";
    }

    @ApiOperation("查询消息模板")
    @GetMapping("/qyeryMessageTemp")
    @ResponseStatus(HttpStatus.OK)
    public List<String> qyeryMessageTemp(@ApiParam("模板key") @RequestParam("tempKey") String tempKey) {
        return messageService.qyeryMessageTemp(tempKey);
    }


    @ApiOperation("招募消息提醒")
    @PostMapping("/recruitMessage")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<Void> recruitMessage(@Valid @RequestBody RecruitMessageRequest request) {
        messageService.recruitMessage(request);
        return ApiResult.ok();
    }

    @ApiOperation("销管调用消息接口")
    @PostMapping("/aggrSend/xg")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<String> aggrSendMsgByXg(@RequestBody  AggrSendMessageRequest aggrSendMessageRequest) {
        log.info("销管调用消息接口 请求入参为：{}", JsonUtil.toJSON(aggrSendMessageRequest));
        return ApiResult.ok(messageService.aggrSendMsgXg(aggrSendMessageRequest));
    }

    @ApiOperation("捷报弹窗标识")
    @PostMapping("/pop-up-window")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<List<PopUpWindowVO>> popUpWindow(@RequestParam("employeeCode") String employeeCode) {
        log.info("popUpWindow：{}", employeeCode);
        return ApiResult.ok(messageService.popUpWindow(employeeCode));
    }
}
