package com.hqins.agent.postsale.service.impl;

import cn.jpush.api.utils.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hqins.agent.postsale.app.PushBean;
import com.hqins.agent.postsale.dao.entity.PushReport;
import com.hqins.agent.postsale.dao.mapper.PushReportMapper;
import com.hqins.agent.postsale.model.vo.PushMessageVO;
import com.hqins.agent.postsale.service.IJPushService;
import com.hqins.agent.postsale.service.JPushService;
import com.hqins.common.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;


@Service
@Slf4j
public class JPushServiceImpl implements JPushService {
    private final PushReportMapper pushReportMapper;
    private final IJPushService ijPushService;

    public JPushServiceImpl(PushReportMapper pushReportMapper, IJPushService ijPushService) {
        this.pushReportMapper = pushReportMapper;
        this.ijPushService = ijPushService;
    }


    @Override
    public String pushMessage(PushMessageVO pushReport) {
        log.info("PUSH发送接口,入参pushReport:{}", JsonUtil.toJSON(pushReport));
        PushReport pushReports = pushReportMapper.selectOne(new LambdaQueryWrapper<PushReport>()
                .eq(PushReport::getEmployeeCode, pushReport.getEmployeeCode())
                .eq(StringUtils.isNotEmpty(pushReport.getOrgType()),PushReport::getOrgType, pushReport.getOrgType())
                .last("limit 1")
        );
        if (pushReports == null) {
            log.info("pushMessage,push发送接口异常:未上报RID");
            return "未上报RID";
        }
        String rid1 = pushReports.getRid();
        String osType = pushReports.getOsType();
        Map<String,String> extrasMap = new HashMap<>();

        extrasMap.put("url",pushReport.getUrlData());
        extrasMap.put("newView",pushReport.getNewView());
        if ("IOS".equals(osType)) {
            return ijPushService.pushIos(PushBean.builder()
                    .title(pushReport.getPushTitle())
                    .alert(pushReport.getPushAlert())
                    .extras(extrasMap)
                    .build(),pushReport.getApnsProduction(), rid1);
        } else if ("Android".equals(osType)) {
            return ijPushService.pushAndroid(PushBean.builder()
                    .title(pushReport.getPushTitle())
                    .alert(pushReport.getPushAlert())
                    .extras(extrasMap)
                    .build(), rid1);
        }
        return "pushMessage,push发送接口异常:未知操作系统类型";
    }
}
