package com.hqins.agent.postsale.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hqins.agent.postsale.dao.entity.LeaveApplications;
import com.hqins.agent.postsale.dto.response.AuditResponse;
import com.hqins.agent.postsale.dto.response.MeAttendanceAndLeaveResponse;
import com.hqins.agent.postsale.model.request.*;
import com.hqins.agent.postsale.model.vo.*;
import com.hqins.common.base.page.PageInfo;
import org.springframework.scheduling.annotation.Async;

import javax.validation.Valid;
import java.util.List;


/**
 * 请假记录表业务接口
 * <AUTHOR>
 * @since 2023-07-14
 */
public interface LeaveApplicationsService extends IService<LeaveApplications> {

    /**
    * 新增请假记录表
    * @param leaveApplicationsAddRequest 请假记录表新增请求
    * @return 请假记录表id
    */
    void add(LeaveApplicationsAddRequest leaveApplicationsAddRequest);


    Boolean check(String beginTime, String endTime , String leaveType,double days);

    /**
    * 分页查询请假记录表数据
    * @param leaveApplicationsQueryRequest 分页查询请求参数
    * @return 请假记录表分页信息
    */
    PageInfo<LeaveAttendanceAuditQueryVO> query(LeaveApplicationsQueryRequest leaveApplicationsQueryRequest);

    LeaveApplicationsVO sumCount(String employeeCode,String type);

    AuditResponse audit(AuditRequest request);

    MeAttendanceAndLeaveResponse attendanceAndLeave(String employeeCode);

    PageInfo<H5LeaveListVO> h5Query(LeaveApplicationsQueryRequest leaveApplicationsQueryRequest);

    void h5Audit(AuditRequest request);

    @Async("executor")
    void jobSupplement();
    @Async("executor")
    void compress();

    void addRevoke(LeaveRevokeAddRequest request);

    PageInfo<LeaveApplicationsQueryVO> querySubmit(LeaveRevokeQueryRequest request);

    Boolean checkOne(String leaveType, String employeeCode);

    List<EmployeeLeaveQueryVO> employeeLeaveQuery(@Valid EmployeeLeaveQueryRequest request);
}