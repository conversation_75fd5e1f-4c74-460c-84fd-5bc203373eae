package com.hqins.agent.postsale.dto.response;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;


@ApiModel(value = "申请OA返回对象", description = "申请OA返回对象")
@Data
public class ApplyOARespondBean  implements Serializable {

    @ApiModelProperty(value = "OA requestId", example = "OA requestId")
    private String requestId;

    @ApiModelProperty(value = "服务执行状态(true:服务执行成功,false:服务执行识别)",required=true,example="true")
    public Boolean FLAG=false;

    @ApiModelProperty(value = "服务执行结果信息",required=true,example="服务执行完成")
    public String MESSAGE="";

    @ApiModelProperty(value = "服务执行异常信息")
    public String EXCEPTION;

}
