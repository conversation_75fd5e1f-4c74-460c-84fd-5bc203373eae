package com.hqins.agent.postsale.dao.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hqins.agent.postsale.dao.entity.AgentAttendance;
import com.hqins.agent.postsale.model.request.AttendanceQueryRequest;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

/**
 * 人员出勤表数据访问接口
 * <AUTHOR>
 * @since 2023-03-02
 */
@Mapper
public interface AgentAttendanceMapper extends BatchBaseMapper<AgentAttendance> {

    Long insertSelective(AgentAttendance agentAttendance);

    int updateByPrimaryKeySelective(AgentAttendance agentAttendance);

    int updateDeleteByPrimaryKey(AgentAttendance agentAttendance);


    Integer selectCountAll(@Param("employeeCode") String employeeCode,@Param("startTime")  String startTime, @Param("endTime") String endTime, @Param("dayList")Set<String> dayList);

    Integer selectNormalCount(@Param("employeeCode")String employeeCode, @Param("auditList") List<String> auditList, @Param("startTime")String startTime, @Param("endTime")String endTime, @Param("dayList")Set<String> dayList);

    Integer selectChecktime(@Param("date") LocalDate date ,@Param("employeeCode") String employeeCode);

    List<AgentAttendance> selectNormalTeamCount(@Param("employeeCode")List<String> employeeCode, @Param("auditList") List<String> auditList, @Param("startTime")String startTime, @Param("endTime")String endTime,@Param("dayList") Set<String> dayList);

    Integer selectNoneTeamCount(@Param("employeeCode")String employeeCode, @Param("startTime")String startTime, @Param("endTime")String endTime,@Param("dayList") Set<String> dayList);

    Integer selectSignCheck(@Param("startTime")LocalDateTime beginTime, @Param("endTime")LocalDateTime endTime, @Param("employeeCode")String employeeCode);

    /**
     * 随机查询limitCount个自动审核通过的记录
     */
    List<AgentAttendance> randQueryByOrgCode(@Param("orgCode")String orgCode,@Param("limitCount")int limitCount,
                                             @Param("startTime")LocalDateTime startTime,@Param("endTime")LocalDateTime endTime);

    List<AgentAttendance> selectExcelDataList(@Param("startTime")String startTime, @Param("endTime")String endTime, @Param("partnerCodes")List<String> partnerCodes);

    /**
     * 查询考勤记录
     *
     * @param page 分页参数
     * @param request 查询条件
     * @param partnerCodes 合伙人机构代码列表
     * @param partners 合伙人机构代码字符串，格式为 'code1', 'code2', ...
     * @param checkTypeList 审核状态列表
     * @return 分页结果
     */
    Page<AgentAttendance> examineQuery(
            @Param("page") Page<AgentAttendance> page,
            @Param("request") AttendanceQueryRequest request,
            @Param("partnerCodes") List<String> partnerCodes,
            @Param("partners") String partners,
            @Param("checkTypeList") List<String> checkTypeList);
}

