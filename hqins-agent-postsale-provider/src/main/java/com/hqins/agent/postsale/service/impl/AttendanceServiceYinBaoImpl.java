package com.hqins.agent.postsale.service.impl;


import com.alibaba.fastjson.JSONObject;
import com.aliyun.openservices.shade.org.apache.commons.lang3.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.hqins.agent.org.model.api.EmployeeApi;
import com.hqins.agent.org.model.api.EmployeeOrgApi;
import com.hqins.agent.org.model.vo.EmployeeOrgVO;
import com.hqins.agent.org.model.vo.EmployeeVO;
import com.hqins.agent.postsale.config.AttendanceConfig;
import com.hqins.agent.postsale.dao.entity.*;
import com.hqins.agent.postsale.dao.mapper.*;
import com.hqins.agent.postsale.dto.enums.*;
import com.hqins.agent.postsale.dto.request.AttendanceSignInRequest;
import com.hqins.agent.postsale.dto.request.CheckOutRequest;
import com.hqins.agent.postsale.dto.request.DataStatisticsRequest;
import com.hqins.agent.postsale.dto.request.ReYinBaoSignRequest;
import com.hqins.agent.postsale.dto.response.*;
import com.hqins.agent.postsale.model.enums.AddressType;
import com.hqins.agent.postsale.model.enums.LeaveType;
import com.hqins.agent.postsale.model.enums.MessageContentType;
import com.hqins.agent.postsale.model.vo.LeaveApplicationsVO;
import com.hqins.agent.postsale.service.AttendanceService;
import com.hqins.agent.postsale.service.AttendanceSettingService;
import com.hqins.agent.postsale.service.AttendanceYinBaoService;
import com.hqins.agent.postsale.service.UtilsService;
import com.hqins.agent.postsale.utils.DateUtils;
import com.hqins.common.base.constants.DatePatterns;
import com.hqins.common.base.enums.AgentOrgType;
import com.hqins.common.base.errors.BadRequestException;
import com.hqins.common.helper.BeanCopier;
import com.hqins.common.utils.JsonUtil;
import com.hqins.common.utils.StringUtil;
import com.hqins.common.web.RequestContextHolder;
import jdk.nashorn.internal.ir.RuntimeNode;
import lombok.extern.slf4j.Slf4j;
import one.util.streamex.StreamEx;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Type;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


@Service
@Slf4j
public class AttendanceServiceYinBaoImpl implements AttendanceYinBaoService {
    private final AttendanceService attendanceService;
    private final AttendanceAddressMapper attendanceAddressMapper;
    private final EmployeeApi employeeApi;
    private final AgentAttendanceMapper agentAttendanceMapper;
    private final AgentImageMapper agentImageMapper;
    private final AttendanceConfig attendanceSignInImageConfig;
    private final AgentAttendanceExtMapper agentAttendanceExtMapper;
    private final LeaveApplicationsMapper leaveApplicationsMapper;
    private final AttendanceLeaveCheckMapper attendanceLeaveCheckMapper;

    private final EmployeeOrgApi employeeOrgApi;

    private final AttendanceSettingService attendanceSettingService;

    private final AgentAttendanceTimeMapper agentAttendanceTimeMapper;
    @Autowired
    private RepairLeaveApprovalMapper repairLeaveApprovalMapper;
    private final UtilsService utilsService;
    @Autowired
    private AttendanceConfig attendanceConfig;

    public AttendanceServiceYinBaoImpl(AttendanceService attendanceService, AttendanceAddressMapper attendanceAddressMapper, EmployeeApi employeeApi,
                                       AgentAttendanceMapper agentAttendanceMapper, AgentImageMapper agentImageMapper, AttendanceConfig attendanceSignInImageConfig,
                                       AgentAttendanceExtMapper agentAttendanceExtMapper, LeaveApplicationsMapper leaveApplicationsMapper,
                                       AttendanceLeaveCheckMapper attendanceLeaveCheckMapper, EmployeeOrgApi employeeOrgApi,
                                       AttendanceSettingService attendanceSettingService, AgentAttendanceTimeMapper agentAttendanceTimeMapper,
                                       UtilsService utilsService) {
        this.attendanceService = attendanceService;
        this.attendanceAddressMapper = attendanceAddressMapper;
        this.employeeApi = employeeApi;
        this.agentAttendanceMapper = agentAttendanceMapper;
        this.agentImageMapper = agentImageMapper;
        this.attendanceSignInImageConfig = attendanceSignInImageConfig;
        this.agentAttendanceExtMapper = agentAttendanceExtMapper;
        this.leaveApplicationsMapper = leaveApplicationsMapper;
        this.attendanceLeaveCheckMapper = attendanceLeaveCheckMapper;
        this.employeeOrgApi = employeeOrgApi;
        this.attendanceSettingService = attendanceSettingService;
        this.agentAttendanceTimeMapper = agentAttendanceTimeMapper;
        this.utilsService = utilsService;
    }

    private String getWorkDays(String ybMonthJson,String year,String month){
        ObjectMapper objectMapper = new ObjectMapper();
        Map<String, List<Integer>> monthMap;
        try {
            monthMap = objectMapper.readValue(ybMonthJson, Map.class);
            List<Integer> dayList = monthMap.get(String.valueOf(month));
            return JsonUtil.toJSON(dayList);
        } catch (JsonProcessingException e) {
            log.error("getWorkDays_error,ybMonthJson:{},month:{}",ybMonthJson,month,e);
            return utilsService.workDays(year,month);
        }
    }

    @Override
    public DataStatisticsResponse dataStatisticsYinBao(DataStatisticsRequest request) {
        String employeeCode = StringUtils.isEmpty(RequestContextHolder.getEmployeeCode()) ? request.getEmployeeCode() : RequestContextHolder.getEmployeeCode();
        DataStatisticsResponse dataStatisticsResponse = new DataStatisticsResponse();
        String orgType = StringUtils.isEmpty(request.getOrgTypeStr()) ? RequestContextHolder.getOrgType() : request.getOrgTypeStr();
        LocalDateTime monthStartTime = LocalDateTime.of(request.getYears(),request.getMonth(),1,0,0,0);
        LocalDateTime monthEndTime = LocalDateTime.of(request.getYears(),request.getMonth(),monthStartTime.with(TemporalAdjusters.lastDayOfMonth()).getDayOfMonth(),23,59,59);
        EmployeeVO employeeInfo;
        if (request.getEmployeeInfo() == null){
            employeeInfo = employeeApi.getEmployeeByEmployeeCode(AgentOrgType.valueOf(orgType), employeeCode);
        }else {
            employeeInfo = request.getEmployeeInfo();
        }
        dataStatisticsResponse.setIsBank(true);
        AgentAttendanceTime agentAttendanceTime;
        Map<Integer,AgentAttendanceTime> agentAttendanceTimeMap = request.getAgentAttendanceTimeMap();
        if (agentAttendanceTimeMap == null){
            agentAttendanceTimeMap = makeMonthTimerMap(request.getYears(),request.getMonth(),employeeInfo);
            agentAttendanceTime = agentAttendanceTimeMap.get(0);
        }else {
            agentAttendanceTime = request.getAgentAttendanceTimeMap().get(0);
        }
        //工作日
        String workDaysStr = getWorkDays(agentAttendanceTime.getYbMonthJson(),String.valueOf(request.getYears()),String.valueOf(request.getMonth()));
        dataStatisticsResponse.setYbMonthJson(workDaysStr);
        dataStatisticsResponse.setTimeMinInterval(agentAttendanceTime.getTimeMinInterval());
        //应出勤天数
        Gson gson = new Gson();
        Type listType = new TypeToken<List<Integer>>() {}.getType();
        List<Integer> workDayList = gson.fromJson(workDaysStr, listType);
        dataStatisticsResponse.setNeedAttendanceDays(String.valueOf(workDayList.size()));

        //设置打卡配置map
        dataStatisticsResponse.setDataStatisticsTimerMap(makeTimeResMap(agentAttendanceTimeMap));
        // 查询打卡数据
        List<MonthPunchIn> monthPunchIns ;
        if (request.getMonthPunchInList() == null){
            monthPunchIns = agentAttendanceExtMapper.selectCalendarDataByMonth(request, employeeCode,monthStartTime,monthEndTime);
        }else {
            monthPunchIns = request.getMonthPunchInList();
        }
        // 查询当月成功打卡地点数
        Integer monthPunchInCount = 0;
        if (request.getIsNeedMonthPunchInCount() == null || request.getIsNeedMonthPunchInCount()){
            monthPunchInCount = agentAttendanceExtMapper.selectCalendarByMonth(monthStartTime,monthEndTime, employeeCode);
        }
        dataStatisticsResponse.setMonthPunchInCount(monthPunchInCount);
        dataStatisticsResponse.setIsLeader(checkLeader(employeeCode));
        //查询补卡剩余次数
        final int agentRepairCount =
                (agentAttendanceTime == null || agentAttendanceTime.getAgentRepairCount() == null) ? 10 :agentAttendanceTime.getAgentRepairCount();
        final int leaderRepairCount =
                (agentAttendanceTime == null || agentAttendanceTime.getLeaderRepairCount() == null) ? 10 :agentAttendanceTime.getLeaderRepairCount();
        final List<MonthPunchIn> collect1 = monthPunchIns.stream().filter(info -> info.getAuditType().contains(SignInType.APPOINT_HALF.name())).collect(Collectors.toList());
        int count = agentRepairCount - collect1.size();
        if (dataStatisticsResponse.getIsLeader()){
            count = leaderRepairCount - collect1.size();
        }
        if (count <= 0){
            dataStatisticsResponse.setIsBk(Boolean.FALSE);
        }else {
            dataStatisticsResponse.setIsBk(Boolean.TRUE);
        }
        //根据打卡日期分组
        Map<Integer, List<MonthPunchIn>> data = monthPunchIns.stream().collect(Collectors.groupingBy(MonthPunchIn::getThatDay, Collectors.toList()));
        //对每天的数据进行排序 排序规则：每天的签到数据根据locationCode分组，组内根据signInTime正序排序，组与组之间的排序根据signInTime最早的排序
        Map<Integer, List<MonthPunchIn>> sortedData = new LinkedHashMap<>();
        for (Map.Entry<Integer, List<MonthPunchIn>> entry : data.entrySet()) {
            List<MonthPunchIn> sortedGroup = sortGroup(entry.getValue());
            sortedData.put(entry.getKey(), sortedGroup);
        }
        //查询请假记录
        List<LeaveApplicationsVO> leaveApplications;
        if (request.getLeaveApplicationsVOList() == null){
            leaveApplications = leaveApplicationsMapper.selectCalendarByMonth(monthStartTime,monthEndTime,employeeCode);
        }else {
            leaveApplications = request.getLeaveApplicationsVOList();
        }
        List<LeaveApplicationsVO> addBankleaveApplications = new ArrayList<>();
        dataStatisticsResponse.setMonthBankLeaveIns(new HashMap<>());
        for (LeaveApplicationsVO leaveApplication : leaveApplications) {
            //处理跨天或半天请假，拆为多条记录
            LocalDateTime beginTime = leaveApplication.getBeginTime();
            LocalDateTime endTime = leaveApplication.getEndTime();
            addBankleaveApplications.addAll(decomposeTimeInterval(beginTime,endTime,Double.parseDouble(leaveApplication.getDuration()),leaveApplication,agentAttendanceTime));
        }
        if (CollectionUtils.isNotEmpty(addBankleaveApplications)){
            Map<Integer, List<LeaveApplicationsVO>> bankLeaveMap = addBankleaveApplications.stream()
                    .filter(x->x.getBeginTime().getMonthValue() == request.getMonth())
                    .sorted(Comparator.comparing(LeaveApplicationsVO::getMorningOrAfternoon))
                    .collect(Collectors.groupingBy(o -> o.getBeginTime().getDayOfMonth()));
            dataStatisticsResponse.setMonthBankLeaveIns(bankLeaveMap);
        }else{
            dataStatisticsResponse.setMonthBankLeaveIns(new HashMap<>());
        }
        dataStatisticsResponse.setMonthPunchIns(sortedData);
        statisticsMonthAttendanceMonth(request,dataStatisticsResponse,agentAttendanceTimeMap);
        return dataStatisticsResponse;
    }

    @Override
    public boolean supplement(ReYinBaoSignRequest request) {
        final EmployeeVO emp = employeeApi.getEmployeeByEmployeeCode(AgentOrgType.valueOf(RequestContextHolder.getOrgType())
                , RequestContextHolder.getEmployeeCode());

        //校验
        checkAppointTime(emp,request);

        //查询归属渠道商
        String merchantName = "";
        String merchantCode = "";
        if (request.getAppointType().equals(AppointType.APPOINT.name())){
            AttendanceAddress attendanceAddress = new LambdaQueryChainWrapper<>(attendanceAddressMapper)
                    .eq(AttendanceAddress::getMerchantOrgCode, request.getLocationCode())
                    .last("limit 1")
                    .one();
            if (null != attendanceAddress){
                merchantName = attendanceAddress.getMerchantName();
                merchantCode = attendanceAddress.getMerchantCode();
            }
        }

        //数据填充
        EmployeeOrgVO employeeOrgVO = employeeOrgApi.queryEmployeeOrgInfo(RequestContextHolder.getEmployeeCode());
        Long id = request.getId();
        //SignInType.APPOINT_HALF.equals(request.getSignInType()) &&
        if ( id != null){
            //查询auditType拼接auditType的字段
            AgentAttendance agentAttendance = agentAttendanceMapper.selectById(id);
            if (agentAttendance.getAuditType().contains(SignInType.NO_ADDRESS.name()) || agentAttendance.getAuditType().contains(SignInType.NON_ASSIGNED.name())){
                if (agentAttendance.getAudit().equals(AttendanceCheckType.TO_BE_REVIEWED.name())){
                    return Boolean.FALSE;
                }
            }
            String auditType = agentAttendance.getAuditType();
            if (StringUtils.isNotEmpty(auditType)){
                auditType = auditType + "," + request.getSignInType().name();
            }else {
                auditType = request.getSignInType().name();
            }
            AgentAttendance build = AgentAttendance.builder()
                    .id(id)
                    .checkOutTime(request.getSignOutTime())
                    .checkInTime(request.getSignInTime())
                    .auditRemark(request.getAuditRemark())
                    .auditType(auditType)
                    .updateTime(LocalDateTime.now())
                    .audit(AttendanceCheckType.TO_BE_REVIEWED.name())
                    .build();
            if (null != employeeOrgVO && (StringUtils.isNotEmpty(employeeOrgVO.getBusinessTeamLeaderCode()))) {
                build.setReviewersCode(employeeOrgVO.getBusinessTeamLeaderCode());
            } else if (null != employeeOrgVO &&
                    (StringUtils.isEmpty(employeeOrgVO.getBusinessTeamLeaderCode()) &&
                            StringUtils.isNotEmpty(employeeOrgVO.getBusinessAreaLeaderCode()))) {
                build.setReviewersCode(employeeOrgVO.getBusinessDeptLeaderCode());
            } else if (null != employeeOrgVO &&
                    (StringUtils.isEmpty(employeeOrgVO.getBusinessTeamLeaderCode()) &&
                            StringUtils.isEmpty(employeeOrgVO.getBusinessAreaLeaderCode()) &&
                            StringUtils.isNotEmpty(employeeOrgVO.getBusinessAreaLeaderCode()))) {
                build.setReviewersCode(employeeOrgVO.getBusinessAreaLeaderCode());
            }
            agentAttendanceMapper.updateByPrimaryKeySelective(build);
        }
        else {
            AgentAttendance build = AgentAttendance.builder()
                    .employeeCode(RequestContextHolder.getEmployeeCode())
                    .orgType(RequestContextHolder.getOrgType())
                    .orgCode(emp.getOrgCode())
                    .employeeName(emp.getName())
                    .orgName(emp.getOrgName())
                    .topCode(emp.getTopCode())
                    .topName(emp.getTopName())
                    .companyName(merchantName)
                    .companyCode(merchantCode)
                    .locationCode(request.getLocationCode())
                    .locationName(request.getLocationName())
                    .latitude(request.getLatitude())
                    .longitude(request.getLongitude())
                    .signInType(request.getAppointType())
                    .checkOutTime(request.getSignOutTime())
                    .checkInTime(request.getSignInTime())
                    .auditRemark(request.getAuditRemark())
                    .updateTime(LocalDateTime.now())
                    .spotCheckType(SpotCheckType.UNTESTED.name())
                    .auditType(request.getSignInType().name())
                    .audit(AttendanceCheckType.TO_BE_REVIEWED.name())
                    .build();
            if (null !=employeeOrgVO && (StringUtils.isNotEmpty(employeeOrgVO.getBusinessTeamLeaderCode()))){
                build.setReviewersCode(employeeOrgVO.getBusinessTeamLeaderCode());
            }else if (null !=employeeOrgVO &&
                    (StringUtils.isEmpty(employeeOrgVO.getBusinessTeamLeaderCode()) &&
                            StringUtils.isNotEmpty(employeeOrgVO.getBusinessAreaLeaderCode()))){
                build.setReviewersCode(employeeOrgVO.getBusinessDeptLeaderCode());
            }else if (null !=employeeOrgVO &&
                    (StringUtils.isEmpty(employeeOrgVO.getBusinessTeamLeaderCode()) &&
                            StringUtils.isEmpty(employeeOrgVO.getBusinessAreaLeaderCode()) &&
                            StringUtils.isNotEmpty(employeeOrgVO.getBusinessAreaLeaderCode()))){
                build.setReviewersCode(employeeOrgVO.getBusinessAreaLeaderCode());
            }
            agentAttendanceMapper.insertSelective(build);
            id = build.getId();
        }

        //保存影像
        if (CollectionUtils.isNotEmpty(request.getImageUrls())) {
            agentImageMapper.delete(new LambdaQueryWrapper<AgentImage>()
                    .eq(AgentImage::getImageType, ImageType.APPOINT_IMG.name())
                    .eq(AgentImage::getImageKey, id.toString()));
            List<AgentImage> saveImageList = new ArrayList<>();
            for (String info : request.getImageUrls()) {
                final AgentImage build = AgentImage.builder()
                        .employeeCode(RequestContextHolder.getEmployeeCode())
                        .agentId(RequestContextHolder.getAgentId().toString())
                        .imageUrl(info)
                        .imageType(ImageType.APPOINT_IMG.name())
                        .imageKey(id.toString())
                        .build();
                saveImageList.add(build);
            }
            agentImageMapper.insertBatchSomeColumn(saveImageList);
        }


        if (!RequestContextHolder.getEmployeeCode().startsWith("S") ){
            //没有上级的情况
            RepairLeaveApproval repBuilder = new RepairLeaveApproval();
            boolean lzFlag = null == employeeOrgVO ||
                    (StringUtils.isEmpty(employeeOrgVO.getBusinessDeptLeaderCode()) &&
                            StringUtils.isEmpty(employeeOrgVO.getBusinessAreaLeaderCode()) &&
                            StringUtils.isEmpty(employeeOrgVO.getBusinessTeamLeaderCode()));

            if (!lzFlag) {
                //判断申请人角色
                boolean isAreaLeader1 = RequestContextHolder.getEmployeeCode().equals(employeeOrgVO.getBusinessAreaLeaderCode());
                boolean isDeptLeader1 = RequestContextHolder.getEmployeeCode().equals(employeeOrgVO.getBusinessDeptLeaderCode());
                boolean isTeamLeader1 = RequestContextHolder.getEmployeeCode().equals(employeeOrgVO.getBusinessTeamLeaderCode());

                //申请人是区长，直接提交量子
                if (isAreaLeader1) {
                    attendanceSettingService.applicationForm(id, LeaveApplyType.APPOINT);
                    return Boolean.TRUE;
                }
                //申请人是部长，没有区长
                if (isDeptLeader1 && StringUtils.isEmpty(employeeOrgVO.getBusinessAreaLeaderCode())) {
                    attendanceSettingService.applicationForm(id, LeaveApplyType.APPOINT);
                    return Boolean.TRUE;
                }
                //申请人是组长，没有区长部长
                if (isTeamLeader1 && StringUtils.isEmpty(employeeOrgVO.getBusinessAreaLeaderCode())
                        && StringUtils.isEmpty(employeeOrgVO.getBusinessDeptLeaderCode())) {
                    attendanceSettingService.applicationForm(id, LeaveApplyType.APPOINT);
                    return Boolean.TRUE;
                }

                //有组长的情况
                if (StringUtils.isNotEmpty(employeeOrgVO.getBusinessTeamLeaderCode()) && !isTeamLeader1) {
                    repBuilder = RepairLeaveApproval.builder()
                            .applicationType(LeaveApplyType.APPOINT.name())
                            .leaveType(AttendanceCheckType.TO_BE_REVIEWED.name())
                            .leaveCode(RequestContextHolder.getEmployeeCode())
                            .approvalCode(employeeOrgVO.getBusinessTeamLeaderCode())
                            .createdTime(LocalDateTime.now())
                            .createdUser(1)
                            .sub(Boolean.TRUE)
                            .build();
                    agentAttendanceMapper.updateByPrimaryKeySelective(AgentAttendance.builder()
                            .id(id)
                            .auditor(employeeOrgVO.getBusinessTeamLeaderName())
                            .build());
                    attendanceSettingService.messageSaveByLeader(employeeOrgVO.getBusinessTeamLeaderCode(), MessageContentType.LEADER_APPOINT_COMMIT, emp.getName());
                } else
                    //有部长
                    if (StringUtils.isNotEmpty(employeeOrgVO.getBusinessDeptLeaderCode()) && !isDeptLeader1) {
                        repBuilder = RepairLeaveApproval.builder()
                                .applicationType(LeaveApplyType.APPOINT.name())
                                .leaveType(AttendanceCheckType.TO_BE_REVIEWED.name())
                                .leaveCode(RequestContextHolder.getEmployeeCode())
                                .approvalCode(employeeOrgVO.getBusinessDeptLeaderCode())
                                .createdTime(LocalDateTime.now())
                                .createdUser(1)
                                .sub(Boolean.TRUE)
                                .build();
                        agentAttendanceMapper.updateByPrimaryKeySelective(AgentAttendance.builder()
                                .id(id)
                                 .auditor(employeeOrgVO.getBusinessDeptLeaderName())
                                .build());
                        attendanceSettingService.messageSaveByLeader(employeeOrgVO.getBusinessDeptLeaderCode(), MessageContentType.LEADER_APPOINT_COMMIT, emp.getName());

                    } else
                        //有区长
                        if (StringUtils.isNotEmpty(employeeOrgVO.getBusinessAreaLeaderCode())) {
                            repBuilder = RepairLeaveApproval.builder()
                                    .applicationType(LeaveApplyType.APPOINT.name())
                                    .leaveType(AttendanceCheckType.TO_BE_REVIEWED.name())
                                    .leaveCode(RequestContextHolder.getEmployeeCode())
                                    .approvalCode(employeeOrgVO.getBusinessAreaLeaderCode())
                                    .createdTime(LocalDateTime.now())
                                    .createdUser(1)
                                    .sub(Boolean.TRUE)
                                    .build();
                            agentAttendanceMapper.updateByPrimaryKeySelective(AgentAttendance.builder()
                                    .id(id)
                                    .auditor(employeeOrgVO.getBusinessAreaLeaderName())
                                    .build());
                            attendanceSettingService.messageSaveByLeader(employeeOrgVO.getBusinessAreaLeaderCode(), MessageContentType.LEADER_APPOINT_COMMIT, emp.getName());
                        }
            }
            //调用量子
            if (!RequestContextHolder.getEmployeeCode().startsWith("S")){
                if (lzFlag){
                    attendanceSettingService.applicationForm(id,LeaveApplyType.APPOINT);
                }else{
                    repBuilder.setLeaveId(id);
                    repairLeaveApprovalMapper.insertSelective(repBuilder);
                }
            }
        }

        //保存日志
        attendanceService.logData(JsonUtil.toJSON(request), RequestContextHolder.getEmployeeCode(), AttendanceNodeType.APPOINT, Boolean.TRUE,"");
        return Boolean.TRUE;
    }

    private void checkAppointTime(EmployeeVO emp , ReYinBaoSignRequest request) {
        LocalDateTime signInTimeReq  =  request.getSignInTime().withSecond(0);
        LocalDateTime signOutTimeReq = request.getSignOutTime().withSecond(0);
        String timeTypeReq = request.getTimeType();
        String companyInsCode = emp.getOrgCode();
        Boolean leader = request.getLeader();
        AttendanceLeaveCheck attendanceLeaveCheck = attendanceLeaveCheckMapper.selectOne(Wrappers.lambdaQuery(AttendanceLeaveCheck.class)
                .eq(AttendanceLeaveCheck::getDeleted, Boolean.FALSE)
                .last(" and FIND_IN_SET('" + emp.getCode() + "', attendance_codes)>0  limit 1"));
        if (attendanceLeaveCheck != null){
            throw new BadRequestException("当前功能已禁用，有问题请联系机构内勤");
        }

        request.setSignInType(SignInType.APPOINT_HALF);
        if (request.getId() != null){
            //只要是不通过就是ALL 这样判断好判断
            AgentAttendance agentAttendance = agentAttendanceMapper.selectById(request.getId());
            if (agentAttendance != null){
                if (agentAttendance.getAudit().equals(AttendanceCheckType.AUDIT_FAILURE.name())){
                    request.setSignInType(SignInType.APPOINT_ALL);
                }
            }else {
                request.setSignInType(SignInType.APPOINT_ALL);
            }
        }else {
            request.setSignInType(SignInType.APPOINT_ALL);
        }

        LocalDate nowDay= LocalDate.now().withDayOfMonth(1);
        AgentAttendanceTime agentAttendanceNewTime = agentAttendanceTimeMapper.selectOne(Wrappers.lambdaQuery(AgentAttendanceTime.class)
                .eq(AgentAttendanceTime::getOrgIns,companyInsCode)
                .le(AgentAttendanceTime::getYears,signInTimeReq.getYear())
                .le(AgentAttendanceTime::getCreatedTime,nowDay.withMonth(nowDay.getMonthValue()).plusMonths(1).minusDays(1)+" 23:59:59")
                .ge(AgentAttendanceTime::getCreatedTime, attendanceConfig.getYinBaoStartTime())
                .orderByDesc(AgentAttendanceTime::getCreatedTime)
                .last("limit 1"));


        //校验签退时间与签到间隔是否符合配置时间间隔
        LocalDate firstDay= LocalDate.of(signInTimeReq.getYear(), signInTimeReq.getMonthValue(), 1);
        AgentAttendanceTime agentAttendanceTime = agentAttendanceTimeMapper.selectOne(Wrappers.lambdaQuery(AgentAttendanceTime.class)
                .eq(AgentAttendanceTime::getOrgIns,companyInsCode)
                .le(AgentAttendanceTime::getYears,signInTimeReq.getYear())
                .le(AgentAttendanceTime::getCreatedTime,firstDay.withMonth(firstDay.getMonthValue()).plusMonths(1).minusDays(1)+" 23:59:59")
                .ge(AgentAttendanceTime::getCreatedTime, attendanceConfig.getYinBaoStartTime())
                .orderByDesc(AgentAttendanceTime::getCreatedTime)
                .last("limit 1"));

        if (null == agentAttendanceTime || !agentAttendanceTime.getPermissions()) {
            return;
        }
        if (agentAttendanceNewTime != null &&  agentAttendanceNewTime.getSignLimit() != null){
            Integer signLimit = agentAttendanceNewTime.getSignLimit();
            int months = Period.between(signInTimeReq.toLocalDate(), LocalDate.now()).getMonths();
            if (months > signLimit) {
                throw new BadRequestException("补卡时间间隔不能超过" + signLimit + "个月");
            }
        }

        //gt 大于 lt 小于 ge 大于等于 le 小于等于
        Integer agentRepairCount = querySupplementCount(DataStatisticsRequest.builder()
                .years(signInTimeReq.getYear())
                .month(signInTimeReq.getMonthValue())
                .day(signInTimeReq.getDayOfMonth())
                .build());
        Integer count =  request.getSignInType().equals(SignInType.APPOINT_ALL) ?  2 : 1 ;
        //获取signInTimeReq的时分秒
        if (agentRepairCount == 0 || (agentRepairCount - count) < 0) {
            throw new BadRequestException("补卡次数不足，无法提交");
        }

        //上午时间范围
        LocalTime startTime = LocalTime.parse(agentAttendanceTime.getMStartTime(),DateTimeFormatter.ofPattern(DatePatterns.HH_MM_SS));
        LocalTime endTime = LocalTime.parse(agentAttendanceTime.getMEndTime(),DateTimeFormatter.ofPattern(DatePatterns.HH_MM_SS));
        if ("LATE".equals(timeTypeReq)) {
            //下午时间范围
            startTime = LocalTime.parse(agentAttendanceTime.getAStartTime(),DateTimeFormatter.ofPattern(DatePatterns.HH_MM_SS));
            endTime = LocalTime.parse(agentAttendanceTime.getAEndTime(),DateTimeFormatter.ofPattern(DatePatterns.HH_MM_SS));
        }
        //补卡，最小时间间隔
        Integer timeMinInterval = agentAttendanceTime.getSignLimit();

        // 判断签到和签退时间是否都在配置的时间范围内
        boolean isSignInValid = !signInTimeReq.toLocalTime().isBefore(startTime) && !signInTimeReq.toLocalTime().isAfter(endTime);
//        boolean isSignOutValid = !signOutTimeReq.toLocalTime().isBefore(startTime) && !signOutTimeReq.toLocalTime().isAfter(endTime);
        // 最终结果
//        boolean isWithinConfiguredTime = isSignInValid && isSignOutValid;

        if (!isSignInValid) {
            throw new BadRequestException("补卡时间需满足"+startTime+" ~ "+endTime);
        }else {
            if (null != timeMinInterval) {
                long between = Duration.between(signInTimeReq, signOutTimeReq).toMinutes();
                long between2 = Duration.between(signInTimeReq.toLocalTime(), endTime).toMinutes();
                if (between2  >= timeMinInterval && between < agentAttendanceTime.getTimeMinInterval()) {
                    throw new BadRequestException("签退必须与签到间隔" + timeMinInterval + "分钟以上哦~");
                }
            }
        }

        //签到时间是否和请假数据重合
        LocalDateTime std = signInTimeReq.withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0).withNano(0);
        LocalDateTime end = signInTimeReq.with(TemporalAdjusters.lastDayOfMonth()).withHour(23).withMinute(59).withSecond(59).withNano(0);

        List<LeaveApplications> agentLeaves = leaveApplicationsMapper.selectList(new LambdaQueryWrapper<LeaveApplications>()
                .eq(LeaveApplications::getEmployeeCode, RequestContextHolder.getEmployeeCode())
                .eq(LeaveApplications::getOrgType, RequestContextHolder.getOrgType())
                .in(LeaveApplications::getAudit,
                        AttendanceCheckType.PASS_THE_AUDIT.name(),
                        AttendanceCheckType.TO_BE_REVIEWED.name(),
                        AttendanceCheckType.UNDER_REVIEW.name(),
                        AttendanceCheckType.AUTO_BYPASS.name())
                .ge(LeaveApplications::getBeginTime, std)
                .le(LeaveApplications::getEndTime, end)
                .eq(LeaveApplications::getRevoked,Boolean.FALSE)
                .orderByDesc(LeaveApplications::getBeginTime));
        if (CollectionUtils.isNotEmpty(agentLeaves)){
            agentLeaves.forEach(info ->{
                final LocalDateTime beginTime = info.getBeginTime();
                final LocalDateTime leaveEndTime = info.getEndTime();
                //判断signInTimeReq不在请假时间内
                if(signInTimeReq.isBefore(leaveEndTime) && signInTimeReq.isAfter(beginTime)){
                    throw new BadRequestException("当前时间段内"+info.getBeginTime().format(DateTimeFormatter.ofPattern(DatePatterns.YYYY_MM_DD_HH_MM_SS))+"~"+info.getEndTime().format(DateTimeFormatter.ofPattern(DatePatterns.YYYY_MM_DD_HH_MM_SS))+"有请假数据，暂无法操作~");
                }
            });
        }
    }

    @Override
    public Long signIn(AttendanceSignInRequest request) {
        //规则校验
        rule(null,request.getCompanyInsCode());

        final EmployeeVO emp = employeeApi.getEmployeeByEmployeeCode(AgentOrgType.valueOf(RequestContextHolder.getOrgType())
                , RequestContextHolder.getEmployeeCode());

        if (emp == null) {
            attendanceService.logData(JsonUtil.toJSON(request), RequestContextHolder.getEmployeeCode(), AttendanceNodeType.SIGN_IN, Boolean.FALSE,"人员信息不存在，请联系管理员");
            throw new BadRequestException("人员信息不存在，请联系管理员");
        }
        AttendanceLeaveCheck attendanceLeaveCheck = attendanceLeaveCheckMapper.selectOne(Wrappers.lambdaQuery(AttendanceLeaveCheck.class)
                .eq(AttendanceLeaveCheck::getDeleted, Boolean.FALSE)
                .last(" and FIND_IN_SET('" + emp.getCode() + "', attendance_codes)>0  limit 1"));
        if (attendanceLeaveCheck != null){
            throw new BadRequestException("当前功能已禁用，有问题请联系机构内勤");
        }

        //查询两个月内最新一条签到记录是否没有签退 是的话将状态变为漏签退
        LocalDate yinBaoStartDate = attendanceSignInImageConfig.getYinBaoStartTime().toInstant().atZone(ZoneId.of("UTC")).toLocalDate();
        AgentAttendance agentAttendances = agentAttendanceMapper.selectOne(new LambdaQueryWrapper<AgentAttendance>()
                .eq(AgentAttendance::getEmployeeCode, RequestContextHolder.getEmployeeCode())
                .ge(AgentAttendance::getCheckInTime,yinBaoStartDate)
                .ge(AgentAttendance::getCheckInTime, LocalDateTime.now().minusMonths(2).withHour(0).withMinute(0).withSecond(0))
                .isNull(AgentAttendance::getCheckOutTime)
                .orderByDesc(AgentAttendance::getCheckInTime).last(" limit 1"));
        if (null != agentAttendances) {
            String auditType = agentAttendances.getAuditType();
            //将auditType中的SIGN_IN去掉
            if (auditType.contains(SignInType.SIGN_IN.name())){
                auditType = auditType.replace(SignInType.SIGN_IN.name(), SignInType.MISSED_SIGNING.name());
            }else{
                auditType = auditType+","+SignInType.MISSED_SIGNING.name();
            }
            agentAttendances.setAuditType(auditType);
            agentAttendances.setAudit(AttendanceCheckType.TO_BE_REVIEWED.name());
            agentAttendanceMapper.updateByPrimaryKeySelective(agentAttendances);
        }

        //查询归属渠道商
        String companyName = "";
        String companyCode = "";
        if (request.getAppointType().equals(AppointType.APPOINT)
                && AddressType.MERCHANT.name().equals(request.getAddressType())){
            AttendanceAddress attendanceAddress = new LambdaQueryChainWrapper<>(attendanceAddressMapper)
                    .eq(AttendanceAddress::getMerchantOrgCode, request.getLocationCode())
                    .last("limit 1")
                    .one();
            companyName = attendanceAddress.getMerchantName();
            companyCode = attendanceAddress.getMerchantCode();
        }

        //保存打卡数据
        AgentAttendance agentAttendance = AgentAttendance.builder()
                .employeeCode(RequestContextHolder.getEmployeeCode())
                .orgType(RequestContextHolder.getOrgType())
                .orgCode(emp.getOrgCode())
                .employeeName(emp.getName())
                .addressType(request.getAddressType())
                .orgName(emp.getOrgName())
                .topCode(emp.getTopCode())
                .topName(emp.getTopName())
                .companyName(companyName)
                .companyCode(companyCode)
                .locationCode(request.getLocationCode())
                .locationName(request.getLocationName())
                .latitude(request.getLatitude())
                .longitude(request.getLongitude())
                .signInType(request.getAppointType().name())
                .spotCheckType(SpotCheckType.UNTESTED.name())
                .checkInTime(LocalDateTime.now())
                .updateTime(LocalDateTime.now())
                .audit(AttendanceCheckType.AUTO_BYPASS.name())
                .build();

        //判断是否需要审核
        if (request.getAttendanceType().contains(SignInType.NO_ADDRESS)
                || request.getAttendanceType().contains(SignInType.NON_ASSIGNED)
                || request.getAttendanceType().contains(SignInType.NON_SPECIFIED)) {
            agentAttendance.setAuditType(request.getAttendanceType().stream().map(SignInType::toString)
                    .collect(Collectors.joining(",")));
            agentAttendance.setAudit(AttendanceCheckType.TO_BE_REVIEWED.name());
        } else {
            //保存认证数据
            AttendanceAddress one = new LambdaQueryChainWrapper<>(attendanceAddressMapper)
                    .eq(AttendanceAddress::getMerchantOrgCode, request.getLocationCode())
                    .eq(AttendanceAddress::getStatus, Boolean.TRUE)
                    .eq(AttendanceAddress::getAuthenticationOrNot, Boolean.FALSE)
                    .one();
            if (null != one) {
                attendanceAddressMapper.updateByPrimaryKeySelective(AttendanceAddress.builder()
                        .id(one.getId())
                        .authenticationOrNot(Boolean.TRUE)
                        .authenticationEmployeeCode(emp.getCode())
                        .authenticationEmployeeName(emp.getName())
                        .authenticationTime(LocalDateTime.now())
                        .updateTime(LocalDateTime.now())
                        .build());
            }
            //范围内签到的数据为签到状态类型
            agentAttendance.setAuditType(SignInType.SIGN_IN.name());
        }
        agentAttendanceMapper.insertSelective(agentAttendance);

        //保存影像
        agentImageMapper.insertSelective(AgentImage.builder()
                .employeeCode(RequestContextHolder.getEmployeeCode())
                .agentId(RequestContextHolder.getAgentId().toString())
                .imageUrl(request.getImageUrl())
                .imageType(ImageType.SIGN_IN_IMG.name())
                .imageKey(agentAttendance.getId().toString())
                .build());
        //保存日志
        attendanceService.logData(JsonUtil.toJSON(request), RequestContextHolder.getEmployeeCode(), AttendanceNodeType.SIGN_IN, Boolean.TRUE,"");
        return agentAttendance.getId();
    }

    @Override
    public SignOutResponse signOut(CheckOutRequest request) {
        if (StringUtils.isEmpty(request.getEncryptedID())) {
            throw new BadRequestException("签退失败，缺少唯一ID");
        }
        String id = new String(Base64.getDecoder().decode(request.getEncryptedID()));

        //规则校验 没有时间区间校验版本
        outRule(id,request.getCompanyInsCode());

        final LocalDateTime now = LocalDateTime.now();
        final String employeeCode = RequestContextHolder.getEmployeeCode();
        //查询签退状态
        AgentAttendance one = new LambdaQueryChainWrapper<>(agentAttendanceMapper)
                .eq(AgentAttendance::getEmployeeCode, employeeCode)
                .eq(AgentAttendance::getId, id)
                .one();

        //更新签退时间
        AgentAttendance build = AgentAttendance.builder()
                .id(Long.valueOf(id))
                .checkOutTime(now)
                .updateTime(LocalDateTime.now())
                .outLatitude(request.getLatitude())
                .outLongitude(request.getLongitude())
                .build();
        //如果签到是正常签到 审核情况赋值自动审核通过
        List<String> strings = Arrays.asList(one.getAuditType().split(","));
        if (strings.contains(SignInType.SIGN_IN.name())
                && strings.size() == 1) {
            build.setAudit(AttendanceCheckType.AUTO_BYPASS.name());
        }

        if (strings.contains(SignInType.NO_ADDRESS.name())
                || strings.contains(SignInType.NON_ASSIGNED.name())
                || strings.contains(SignInType.NON_SPECIFIED.name())) {
            build.setAudit(AttendanceCheckType.TO_BE_REVIEWED.name());
        }

        //更新签到数据
        int i = agentAttendanceMapper.updateByPrimaryKeySelective(build);

        if (i <= 0) {
            throw new BadRequestException("签退失败，没有对应的签到记录");
        }

        //保存影像
        agentImageMapper.insertSelective(AgentImage.builder()
                .employeeCode(RequestContextHolder.getEmployeeCode())
                .agentId(RequestContextHolder.getAgentId().toString())
                .imageUrl(request.getImageUrl())
                .imageType(ImageType.SIGN_OUT_IMG.name())
                .imageKey(id)
                .build());

        //保存日志
        attendanceService.logData(JsonUtil.toJSON(request), RequestContextHolder.getEmployeeCode(), AttendanceNodeType.SIGN_OUT, Boolean.TRUE, "");

        return SignOutResponse.builder()
                .signOutTime(now)
                .signInTime(one.getCheckInTime())
                .label(request.getBusinessLabel())
                .build();
    }

    @Override
    public AttendanceListResponse dataDay(String orgCode) {
        LocalDateTime now = LocalDateTime.now();
        String employeeCode = RequestContextHolder.getEmployeeCode();
        AttendanceListResponse response = new AttendanceListResponse();
        now = now.withHour(23).withMinute(59).withSecond(59);

        //查询签到规则数据
        AgentAttendanceTime agentAttendanceTime = agentAttendanceTimeMapper.selectOne(new LambdaQueryWrapper<AgentAttendanceTime>()
                .eq(AgentAttendanceTime::getOrgIns, orgCode)
                .ge(AgentAttendanceTime::getCreatedTime,attendanceConfig.getYinBaoStartTime())
                .le(AgentAttendanceTime::getYears,now.getYear())
                .orderByDesc(AgentAttendanceTime::getYears)
                .orderByDesc(AgentAttendanceTime::getCreatedTime)
                .last("limit 1"));

        if (null != agentAttendanceTime){
            response.setMorningStipulateCheckInTime(agentAttendanceTime.getMStartTime()+"-"+agentAttendanceTime.getMEndTime());
            response.setAfterStipulateCheckInTime(agentAttendanceTime.getAStartTime()+"-"+agentAttendanceTime.getAEndTime());
            response.setTimeMinInterval(agentAttendanceTime.getTimeMinInterval());
        }else{
            EmployeeVO employeeInfo = employeeApi.getEmployeeInfo(AgentOrgType.valueOf(RequestContextHolder.getOrgType()), RequestContextHolder.getEmployeeCode());
            agentAttendanceTime = makeNewTime(EmployeeVO.builder().orgCode(orgCode).topCode(employeeInfo.getTopCode()).build());
        }

        //查询当前用户当月的签到数据
        List<AgentAttendance> agentAttendances = agentAttendanceExtMapper.selectYBSingData(employeeCode," "+agentAttendanceTime.getMStartTime(),
                " "+agentAttendanceTime.getMEndTime()," "+agentAttendanceTime.getAStartTime()," "+agentAttendanceTime.getAEndTime());
        if (CollectionUtils.isEmpty(agentAttendances)){
            return response;
        }

        List<AttendanceListResponse.ResponseList> attendanceListResponses = StreamEx.of(agentAttendances)
                .map(agentAttendance -> BeanCopier.copyObject(agentAttendance, AttendanceListResponse.ResponseList.class)).toList();
        //查询指定地点
        final List<String> collect = attendanceListResponses.stream().map(AttendanceListResponse.ResponseList::getLocationCode).collect(Collectors.toList());
        final List<AttendanceAddress> list = new LambdaQueryChainWrapper<>(attendanceAddressMapper)
                .in(AttendanceAddress::getMerchantOrgCode, collect)
                .list();
        //设置经纬度
        if (CollectionUtils.isNotEmpty(list)){
            Map<String, AttendanceAddress> collect1 = list.stream().collect(Collectors.toMap(AttendanceAddress::getMerchantOrgCode, Function.identity()));
            attendanceListResponses.forEach(info ->{
                AttendanceAddress attendanceAddress = collect1.get(info.getLocationCode());
                if (attendanceAddress == null){
                    return;
                }
                info.setTargetLatitude(attendanceAddress.getLatitude());
                info.setTargetLongitude(attendanceAddress.getLongitude());
                info.setCompanyInsCode(attendanceAddress.getCompanyInstCode());
            });
        }
        response.setResponseListList(attendanceListResponses);
        return response;
    }

    @Override
    public void rule(String id, String companyInsCode) {
        //校验签退时间与签到间隔是否符合配置时间间隔
        final LocalDateTime now = LocalDateTime.now();

        //签到时间是否和请假数据重合
        LocalDateTime std = LocalDateTime.now().withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0);
        LocalDateTime end = LocalDateTime.now().with(TemporalAdjusters.lastDayOfMonth()).withHour(23).withMinute(59).withSecond(59);

        List<LeaveApplications> agentLeaves = leaveApplicationsMapper.selectList(new LambdaQueryWrapper<LeaveApplications>()
                .eq(LeaveApplications::getEmployeeCode, RequestContextHolder.getEmployeeCode())
                .eq(LeaveApplications::getOrgType, RequestContextHolder.getOrgType())
                .in(LeaveApplications::getAudit, "PASS_THE_AUDIT", "TO_BE_REVIEWED", "UNDER_REVIEW", "AUTO_BYPASS")
                .ge(LeaveApplications::getBeginTime, std.withNano(0))
                .le(LeaveApplications::getEndTime, end.withNano(0))
                .eq(LeaveApplications::getRevoked,Boolean.FALSE)
                .orderByDesc(LeaveApplications::getBeginTime));
        if (CollectionUtils.isNotEmpty(agentLeaves)){
            agentLeaves.forEach(info ->{
                final LocalDateTime beginTime = info.getBeginTime();
                final LocalDateTime endTime = info.getEndTime();
                //判断signInTimeReq不在请假时间内
                if(LocalDateTime.now().isAfter(beginTime) && LocalDateTime.now().isBefore(endTime)){
                    throw new BadRequestException("当前时间段内"+info.getBeginTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))+"~"+info.getEndTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))+"有请假数据，暂无法操作~");
                }
            });
        }

        AgentAttendanceTime agentAttendanceTime = agentAttendanceTimeMapper.selectOne(new LambdaQueryWrapper<AgentAttendanceTime>()
                .eq(AgentAttendanceTime::getOrgIns, companyInsCode)
                .le(AgentAttendanceTime::getYears, String.valueOf(LocalDate.now().getYear()))
                .ge(AgentAttendanceTime::getCreatedTime, attendanceConfig.getYinBaoStartTime())
                .orderByDesc(AgentAttendanceTime::getCreatedTime)
                .last("limit 1"));

        //给默认规则
        if (null == agentAttendanceTime || !agentAttendanceTime.getPermissions()) {
            return;
        }

        //把上面三个时间转换成HH:mm:ss的时间格式
        LocalTime mStartTime = LocalTime.parse(agentAttendanceTime.getMStartTime(), DateTimeFormatter.ofPattern(DatePatterns.HH_MM_SS));
        LocalTime mEndTime = LocalTime.parse(agentAttendanceTime.getMEndTime(), DateTimeFormatter.ofPattern(DatePatterns.HH_MM_SS));
        LocalTime aStartTime = LocalTime.parse(agentAttendanceTime.getAStartTime(), DateTimeFormatter.ofPattern(DatePatterns.HH_MM_SS));
        LocalTime aEndTime = LocalTime.parse(agentAttendanceTime.getAEndTime(), DateTimeFormatter.ofPattern(DatePatterns.HH_MM_SS));

        //判断当前时间是出于上午还是下午
        boolean isMorning = now.toLocalTime().isAfter(mStartTime) && now.toLocalTime().isBefore(mEndTime);
        boolean isAfternoon = now.toLocalTime().isAfter(aStartTime) && now.toLocalTime().isBefore(aEndTime);
        if (!isMorning && !isAfternoon){
            throw new BadRequestException("当前时间不在考勤范围内");
        }
    }

    @Override
    public AgentAttendance noSignOut() {
        int i = 1;
        if (LocalDate.now().getDayOfMonth()==1){
            i=0;
        }
        LocalDateTime utc = attendanceConfig.getYinBaoStartTime().toInstant().atZone(ZoneId.of("UTC")).toLocalDateTime();

        AgentAttendance agentAttendance = agentAttendanceMapper.selectOne(new LambdaQueryWrapper<AgentAttendance>()
                .eq(AgentAttendance::getEmployeeCode, RequestContextHolder.getEmployeeCode())
                .ge(AgentAttendance::getCheckInTime, LocalDateTime.now().minusMonths(2).minusDays(i).withHour(0).withMinute(0).withSecond(0))
                .le(AgentAttendance::getCheckInTime, LocalDateTime.now().minusDays(1).withHour(23).withMinute(59).withSecond(59))
                .ge(AgentAttendance::getCheckInTime,utc.withHour(0).withMinute(0).withSecond(0))
//                .ge(AgentAttendance::getCheckInTime,attendanceConfig.getYinBaoStartTime())
//                .like(AgentAttendance::getAuditType, SignInType.MISSED_SIGNING.name())
//                .isNull(AgentAttendance::getCheckOutTime)
                .orderByDesc(AgentAttendance::getCheckInTime).last(" limit 1"));
        if (null == agentAttendance){
            return null;
        }

        if (agentAttendance.getCheckOutTime()!=null){
            return null;
        }
        return agentAttendance;
    }

    @Override
    public Integer querySupplementCount(DataStatisticsRequest request) {
        String employeeCode =  StringUtils.isEmpty(request.getEmployeeCode())? RequestContextHolder.getEmployeeCode() : request.getEmployeeCode();
        String orgType =StringUtils.isEmpty(request.getOrgType()) ? RequestContextHolder.getOrgType():  request.getOrgType();
        List<String> str = agentAttendanceExtMapper.selectCountByReissueACard(employeeCode, request.getYears(), request.getMonth());

        String lastDayOfMonth = getLastDayOfMonth(String.valueOf(request.getYears()), String.format("%02d", request.getMonth()), String.format("%02d",request.getDay()));
        String date = request.getYears() +"-"+ String.format("%02d", request.getMonth())+"-" + lastDayOfMonth + " 23:59:59";
        final EmployeeVO employeeInfo = employeeApi.getEmployeeInfo(AgentOrgType.valueOf(orgType), employeeCode);
        AgentAttendanceTime twoQuery = agentAttendanceTimeMapper.selectOne(new LambdaQueryWrapper<AgentAttendanceTime>()
                .eq(AgentAttendanceTime::getOrgIns, employeeInfo.getOrgCode())
                .le(AgentAttendanceTime::getYears, request.getYears())
                .le(AgentAttendanceTime::getCreatedTime,date)
                .ge(AgentAttendanceTime::getCreatedTime,attendanceConfig.getYinBaoStartTime())
                .orderByDesc(AgentAttendanceTime::getCreatedTime)
                .last("limit 1"));
        final int agentRepairCount = twoQuery == null ? 10 : twoQuery.getAgentRepairCount();
        final int leaderRepairCount = twoQuery == null ? 10 : twoQuery.getLeaderRepairCount();

        int sum = str.stream()
                .mapToInt(info -> {
                    if (info.contains(SignInType.APPOINT_ALL.name())) return 2;
                    if (info.contains(SignInType.APPOINT_HALF.name())) return 1;
                    return 0;
                })
                .sum();
        int count = agentRepairCount - sum;
        final Boolean aBoolean = checkLeader(employeeCode);
        if (aBoolean) {
            count = leaderRepairCount - sum;
        }
        return Math.max(count, 0);

    }
    public static String getLastDayOfMonth(String year, String month, String day) {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.YEAR, Integer.parseInt(year));
        calendar.set(Calendar.MONTH, Integer.parseInt(month) - 1); // 月份是从0开始的，所以要减1
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
        return String.valueOf(calendar.get(Calendar.DAY_OF_MONTH));
    }

    public void outRule(String id, String companyInsCode) {
        //签到时间是否和请假数据重合
        LocalDateTime std = LocalDateTime.now().withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0);
        LocalDateTime end = LocalDateTime.now().with(TemporalAdjusters.lastDayOfMonth()).withHour(23).withMinute(59).withSecond(59);

        List<LeaveApplications> agentLeaves = leaveApplicationsMapper.selectList(new LambdaQueryWrapper<LeaveApplications>()
                .eq(LeaveApplications::getEmployeeCode, RequestContextHolder.getEmployeeCode())
                .eq(LeaveApplications::getOrgType, RequestContextHolder.getOrgType())
                .in(LeaveApplications::getAudit, "PASS_THE_AUDIT", "TO_BE_REVIEWED", "UNDER_REVIEW", "AUTO_BYPASS")
                .ge(LeaveApplications::getBeginTime, std.withNano(0))
                .le(LeaveApplications::getEndTime, end.withNano(0))
                .eq(LeaveApplications::getRevoked,Boolean.FALSE)
                .orderByDesc(LeaveApplications::getBeginTime));
        if (CollectionUtils.isNotEmpty(agentLeaves)){
            agentLeaves.forEach(info ->{
                final LocalDateTime beginTime = info.getBeginTime();
                final LocalDateTime endTime = info.getEndTime();
                //判断signInTimeReq不在请假时间内
                if(LocalDateTime.now().isAfter(beginTime) && LocalDateTime.now().isBefore(endTime)){
                    throw new BadRequestException("当前时间段内"+info.getBeginTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))+"~"+info.getEndTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))+"有请假数据，暂无法操作~");
                }
            });
        }

        AgentAttendanceTime agentAttendanceTime = agentAttendanceTimeMapper.selectOne(new LambdaQueryWrapper<AgentAttendanceTime>()
                .eq(AgentAttendanceTime::getOrgIns, companyInsCode)
                .le(AgentAttendanceTime::getYears, String.valueOf(LocalDate.now().getYear()))
                .ge(AgentAttendanceTime::getCreatedTime, attendanceConfig.getYinBaoStartTime())
                .orderByDesc(AgentAttendanceTime::getCreatedTime)
                .last("limit 1"));

        if (null == agentAttendanceTime || !agentAttendanceTime.getPermissions()) {
            return;
        }

        //签退数据校验
        if (StringUtil.isNotEmpty(id)) {
            AgentAttendance agentAttendance = agentAttendanceMapper.selectById(id);
            if (agentAttendance == null) {
                throw new BadRequestException("签到数据不存在");
            }
            if (agentAttendance.getCheckOutTime() != null) {
                throw new BadRequestException("已签退");
            }

            if (null != agentAttendanceTime.getTimeMinInterval()) {
                LocalDateTime checkInTime = agentAttendance.getCheckInTime();
                if (null != checkInTime) {
                    long between = Duration.between(checkInTime, LocalDateTime.now()).toMinutes();
                    if (between < agentAttendanceTime.getTimeMinInterval()) {
                        throw new BadRequestException("签退必须与签到间隔" + agentAttendanceTime.getTimeMinInterval() + "分钟以上哦~");
                    }
                }
            }
        }
    }

    private void statisticsMonthAttendanceMonth(DataStatisticsRequest request,DataStatisticsResponse dataStatisticsResponse,
                                                Map<Integer,AgentAttendanceTime> agentAttendanceTimeMap){
        Boolean isLeader = dataStatisticsResponse.getIsLeader();
        //签到数据
        Map<Integer, List<MonthPunchIn>> monthPunchIns = dataStatisticsResponse.getMonthPunchIns();
        //银保-请假数据
        Map<Integer, List<LeaveApplicationsVO>> monthBankLeaveIns = dataStatisticsResponse.getMonthBankLeaveIns();
        int lastDay;
        LocalDate today = LocalDate.now();
        if (request.getYears() > today.getYear() || (request.getYears() == today.getYear() && today.getMonthValue() < request.getMonth())){
            //未来月不统计
            dataStatisticsResponse.setNormalAttendance(0);
            dataStatisticsResponse.setUnusualAttendance(0);
            dataStatisticsResponse.setLeaveAttendance(0);
            return;
        }
        if (LocalDate.of(request.getYears(), request.getMonth(), 1).isEqual(today.with(TemporalAdjusters.firstDayOfMonth()))){
            //当前月
            lastDay = today.getDayOfMonth();
        }else{
            lastDay = LocalDate.of(request.getYears(),request.getMonth(),1).with(TemporalAdjusters.lastDayOfMonth()).getDayOfMonth();
        }
        double normalAttendance = 0;   //正常考勤天数
        double unusualAttendance = 0;  //异常打卡次数
        double leaveAttendance = 0;    //请假天数
        double successDaKaDays = 0;    //成功打卡天数
        //打卡上午范围12:00:59
        LocalTime noon = LocalTime.of(12,1,0);
        AgentAttendanceTime agentAttendanceTime ;
        Map<Integer,Integer> workDateMap = getWorkMap(agentAttendanceTimeMap.get(0).getYbMonthJson(),String.valueOf(request.getYears()),String.valueOf(request.getMonth()));
        boolean isWorkDay; //是否为工作日
        for (int day = 1; day <= lastDay ; day++){
            agentAttendanceTime = agentAttendanceTimeMap.get(day);
            if (workDateMap.containsKey(day)){
                //工作日
                isWorkDay = true;
            }else {
                //非工作日
                isWorkDay = false;
            }
            //客户经理打卡次数
            int agentClockCount = agentAttendanceTime.getAgentClockCount() == null ? 2 : agentAttendanceTime.getAgentClockCount();
            //主管打卡次数
            int leaderClockCount = agentAttendanceTime.getLeaderClockCount() == null ? 2 : agentAttendanceTime.getLeaderClockCount();
            //需要打卡次数
            int needClockCount = isLeader ? leaderClockCount : agentClockCount;
            //打卡数据
            List<MonthPunchIn> monthPunchInList = CollectionUtils.isEmpty(monthPunchIns.get(day)) ? new ArrayList<>(0) : monthPunchIns.get(day);

            //上午是否有正常打卡
            boolean shangWuCheckDaka = false;
            //下午是否有正常打卡
            boolean xiaWuCheckDaka = false;

            //上午是否有审核通过的请假
            boolean shangWuQingJia = false;
            //下午是否有审核通过的请假
            boolean xiaWuQingJia = false;

            for (MonthPunchIn monthPunchIn : monthPunchInList){
                String checkType = monthPunchIn.getCheckType();
                String spotCheckType = monthPunchIn.getSpotCheckType();
                String signInType = monthPunchIn.getSignInType();
                //判断是否有正常打卡
                if (
                        (StringUtil.isNotEmpty(monthPunchIn.getSignInTime()) && StringUtil.isNotEmpty(monthPunchIn.getSignOutTime()))
                                &&
                                (   AttendanceCheckType.PASS_THE_AUDIT.name().equals(checkType)
                                        || AttendanceCheckType.AUTO_BYPASS.name().equals(checkType)
                                        || (AttendanceCheckType.TO_BE_REVIEWED.name().equals(checkType) && SpotCheckType.SPOT_CHECK.name().equals(spotCheckType))
                                )
                ){
                    //签到、签退都有 && (自动审核通过 || 审核通过 || 抽擦待审核)
                    if (DateUtils.convertLocalDateTime(monthPunchIn.getSignInTime()).toLocalTime().isBefore(noon)) {
                        shangWuCheckDaka = true;
                    } else {
                        xiaWuCheckDaka = true;
                    }
                }
                //异常打卡次数
                if (isWorkDay){
                    if (signInType.contains(SignInType.MISSED_SIGNING.name())){
                        //存在漏签退
                        unusualAttendance += 1;
                    }else if (AttendanceCheckType.TO_BE_REVIEWED.name().equals(checkType)
                            || AttendanceCheckType.UNDER_REVIEW.name().equals(checkType)
                            || AttendanceCheckType.AUDIT_FAILURE.name().equals(checkType) ){
                        //待审核，审核中，审核不通过
                        unusualAttendance += 1;
                    }
                }
            }
            //请假列表
            List<LeaveApplicationsVO> leaveApplicationsVOList = CollectionUtils.isEmpty(monthBankLeaveIns.get(day)) ? new ArrayList<>(0) : monthBankLeaveIns.get(day);
            for (LeaveApplicationsVO leaveApplicationsVO : leaveApplicationsVOList){
                if (AttendanceCheckType.PASS_THE_AUDIT.name().equals(leaveApplicationsVO.getAudit())){
                    leaveAttendance += 0.5;
                }
                if (LeaveType.PERSONAL_LEAVE.name().equals(leaveApplicationsVO.getLeaveType())){
                    continue;
                }
                if (AttendanceCheckType.PASS_THE_AUDIT.name().equals(leaveApplicationsVO.getAudit())){
                    //请假，审核通过
                    if (leaveApplicationsVO.getMorningOrAfternoon() == 0){
                        shangWuQingJia = true;
                    }
                    if (leaveApplicationsVO.getMorningOrAfternoon() == 1){
                        xiaWuQingJia = true;
                    }
                }
            }
            //正常考勤天数
            if (isWorkDay && needClockCount == 1 && (shangWuCheckDaka || shangWuQingJia || xiaWuCheckDaka || xiaWuQingJia)){
                //工作日，需要打1次卡，上下午打过卡或请假
                normalAttendance += 1;
            }
            if (isWorkDay && needClockCount == 2 && (shangWuCheckDaka || shangWuQingJia)){
                //工作日，需要打2次卡，（上午正常打卡 || 上午请假）
                normalAttendance += 0.5;
            }
            if (isWorkDay && needClockCount == 2 && (xiaWuCheckDaka || xiaWuQingJia)){
                //工作日，需要打2次卡，（下午正常打卡 || 下午请假）
                normalAttendance += 0.5;
            }

            //成功打卡天数
            if (isWorkDay && needClockCount == 1 && (shangWuCheckDaka || xiaWuCheckDaka)){
                //工作日，需要打1次卡，上下午打过卡
                successDaKaDays += 1;
            }
            if (isWorkDay && needClockCount == 2 && shangWuCheckDaka){
                //需要打2次卡，上午正常打卡
                successDaKaDays += 0.5;
            }
            if (isWorkDay && needClockCount == 2 && xiaWuCheckDaka){
                //工作日，需要打2次卡，下午正常打卡
                successDaKaDays += 0.5;
            }
        }
        dataStatisticsResponse.setNormalAttendance(normalAttendance);
        dataStatisticsResponse.setUnusualAttendance(unusualAttendance);
        dataStatisticsResponse.setLeaveAttendance(leaveAttendance);
        dataStatisticsResponse.setSuccessDaKaDays(successDaKaDays);
    }

    private Map<Integer,Integer> getWorkMap(String ybMonthJson,String year,String month){
        String workDaysStr = getWorkDays(ybMonthJson,year,month);
        List<Integer> numbers = JSONObject.parseObject(workDaysStr, List.class);
        Map<Integer, Integer> numberMap = numbers.stream().collect(Collectors.toMap(num -> num,num -> num));
        return numberMap;
    }

    /**
     * 银保分割时间段 ，以半天为单位
     */
    public static List<LeaveApplicationsVO> decomposeTimeInterval(LocalDateTime start, LocalDateTime end,double duration,
                                                                  LeaveApplicationsVO leaveApplicationsVO,AgentAttendanceTime agentAttendanceTime) {
        List<LeaveApplicationsVO> leaveApplicationsVOList = new ArrayList<>();
        if (duration == 0.5){
            if (start.toLocalTime().isBefore(LocalTime.NOON)) {
                leaveApplicationsVO.setMorningOrAfternoon(0);
            } else {
                leaveApplicationsVO.setMorningOrAfternoon(1);
            }
            leaveApplicationsVOList.add(leaveApplicationsVO);
            return leaveApplicationsVOList;
        }
        DateTimeFormatter formatterDate = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        DateTimeFormatter formatterDateTime = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        boolean isShangWu = start.toLocalTime().isBefore(LocalTime.NOON);
        int starIndex = isShangWu ? 1 : 2;
        for (int i = 1 ; i <= duration / 0.5 ; i++){
            String dateStr = start.format(formatterDate);
            if (starIndex % 2 == 1){
                //上午
                String startStr = dateStr + " " + agentAttendanceTime.getMStartTime();
                String endStr = dateStr + " " + agentAttendanceTime.getMEndTime();
                LeaveApplicationsVO vo = BeanCopier.copyObject(leaveApplicationsVO,LeaveApplicationsVO.class);
                vo.setBeginTime(LocalDateTime.parse(startStr,formatterDateTime));
                vo.setEndTime(LocalDateTime.parse(endStr,formatterDateTime));
                vo.setMorningOrAfternoon(0);
                leaveApplicationsVOList.add(vo);
            }else{
                //下午
                String startStr = dateStr + " " + agentAttendanceTime.getAStartTime();
                String endStr = dateStr + " " + agentAttendanceTime.getAEndTime();
                LeaveApplicationsVO vo = BeanCopier.copyObject(leaveApplicationsVO,LeaveApplicationsVO.class);
                vo.setBeginTime(LocalDateTime.parse(startStr,formatterDateTime));
                vo.setEndTime(LocalDateTime.parse(endStr,formatterDateTime));
                vo.setMorningOrAfternoon(1);
                leaveApplicationsVOList.add(vo);
                start = start.plusDays(1);
            }
            starIndex++;
        }
        return leaveApplicationsVOList;
    }

    private AgentAttendanceTime makeNewTime(EmployeeVO employeeInfo){
        LocalDateTime nowTime = LocalDateTime.now();
        AgentAttendanceTime agentAttendanceTime = new AgentAttendanceTime();
        agentAttendanceTime.setOrgCode(employeeInfo.getTopCode());
        agentAttendanceTime.setOrgIns(employeeInfo.getOrgCode());
        agentAttendanceTime.setAudit(false);
        agentAttendanceTime.setMonthJson("");
        agentAttendanceTime.setYears(String.valueOf(nowTime.getYear()));
        agentAttendanceTime.setPermissions(true);
        agentAttendanceTime.setCreatedTime(nowTime);
        agentAttendanceTime.setUpdatedTime(nowTime);
        agentAttendanceTime.setUpdatedUser("system");
        agentAttendanceTime.setCreatedUser("system");
        agentAttendanceTime.setAgentClockCount(2);
        agentAttendanceTime.setLeaderClockCount(1);
        agentAttendanceTime.setAgentRepairCount(4);
        agentAttendanceTime.setLeaderRepairCount(4);
        agentAttendanceTime.setMStartTime("08:00:00");
        agentAttendanceTime.setMEndTime("12:00:00");
        agentAttendanceTime.setAStartTime("13:00:00");
        agentAttendanceTime.setAEndTime("20:00:00");
        agentAttendanceTime.setTimeMinInterval(30);
        agentAttendanceTime.setSignLimit(2);
        String workDayJson = utilsService.yearWorkDays(String.valueOf(nowTime.getYear()));
        agentAttendanceTime.setYbMonthJson(workDayJson);
        agentAttendanceTimeMapper.insert(agentAttendanceTime);
        return agentAttendanceTime;
    }

    public Map<Integer,AgentAttendanceTime> makeMonthTimerMap(Integer year,Integer month,EmployeeVO employeeInfo) {
        Map<Integer,AgentAttendanceTime> timeMap = new HashMap<>();
        LocalDateTime yinBaoStartTime = attendanceSignInImageConfig.getYinBaoStartTime().toInstant().atZone(ZoneId.of("UTC")).toLocalDateTime();
        List<AgentAttendanceTime> agentAttendanceTimeList = agentAttendanceTimeMapper.selectList(new LambdaQueryWrapper<AgentAttendanceTime>()
                .eq(AgentAttendanceTime::getOrgIns, employeeInfo.getOrgCode())
                .le(AgentAttendanceTime::getYears, String.valueOf(year))
                .ge(AgentAttendanceTime::getCreatedTime, yinBaoStartTime)
                .orderByDesc(AgentAttendanceTime::getYears)
                .orderByDesc(AgentAttendanceTime::getCreatedTime)
        );
        LocalDate monthStartDate = LocalDate.of(year,month,1);
        LocalDate monthEndDate = LocalDate.of(year,month,monthStartDate.with(TemporalAdjusters.lastDayOfMonth()).getDayOfMonth());
        int days = monthEndDate.getDayOfMonth();
        if (CollectionUtils.isEmpty(agentAttendanceTimeList)){
            AgentAttendanceTime agentAttendanceTime = makeNewTime(employeeInfo);
            for (int day = 1; day <= days; day++){
                timeMap.put(day,AgentAttendanceTime.builder()
                        .agentClockCount(2)
                        .leaderClockCount(2)
                        .mStartTime("08:00:00")
                        .mEndTime("12:00:00")
                        .aStartTime("13:00:00")
                        .aEndTime("20:00:00")
                        .ybMonthJson(agentAttendanceTime.getYbMonthJson())
                        .build());
            }
            timeMap.put(0,agentAttendanceTime);
            return timeMap;
        }
        AgentAttendanceTime agentAttendanceTime = agentAttendanceTimeList.get(0);
        timeMap.put(0,agentAttendanceTime);
        String ybMonthJson = agentAttendanceTime.getYbMonthJson();
        if (StringUtil.isEmpty(ybMonthJson) || !agentAttendanceTime.getYears().equals(String.valueOf(year))){
            //非当年有工作日配置，需要重新生成
            ybMonthJson = utilsService.yearWorkDays(String.valueOf(year));
            agentAttendanceTime.setYbMonthJson(ybMonthJson);
        }
        //agentAttendanceTimeList根据createdTime年月日分组成map
        for (int day = 1; day <= days; day++){
            monthStartDate = LocalDate.of(year,month,day);
            AgentAttendanceTime timeVo = AgentAttendanceTime.builder()
                    .agentClockCount(2)
                    .leaderClockCount(2)
                    .mStartTime("08:00:00")
                    .mEndTime("12:00:00")
                    .aStartTime("13:00:00")
                    .aEndTime("20:00:00")
                    .build();
            for (AgentAttendanceTime attendanceTime : agentAttendanceTimeList){
                LocalDate createdTime = attendanceTime.getCreatedTime().toLocalDate();
                if (createdTime.isBefore(monthStartDate) || createdTime.equals(monthStartDate)){
                    timeVo = attendanceTime;
                    if (StringUtil.isNotEmpty(attendanceTime.getYbMonthJson())){
                        timeVo.setYbMonthJson(ybMonthJson);
                    }
                    break;
                }
            }
            timeMap.put(day,timeVo);
        }
        return timeMap;
    }

    private Map<Integer, DataStatisticsTimerVO> makeTimeResMap(Map<Integer,AgentAttendanceTime> agentAttendanceTimeMap){
        Map<Integer, DataStatisticsTimerVO> resMap = new HashMap<>();
        for (Integer day : agentAttendanceTimeMap.keySet()){
            DataStatisticsTimerVO dataStatisticsTimerVO = new DataStatisticsTimerVO();
            AgentAttendanceTime agentAttendanceTime = agentAttendanceTimeMap.get(day);
            dataStatisticsTimerVO.setAgentClockCount(agentAttendanceTime.getAgentClockCount());
            dataStatisticsTimerVO.setLeaderClockCount(agentAttendanceTime.getLeaderClockCount());
            dataStatisticsTimerVO.setMStartTime(agentAttendanceTime.getMStartTime());
            dataStatisticsTimerVO.setMEndTime(agentAttendanceTime.getMEndTime());
            dataStatisticsTimerVO.setAStartTime(agentAttendanceTime.getAStartTime());
            dataStatisticsTimerVO.setAEndTime(agentAttendanceTime.getAEndTime());
            resMap.put(day,dataStatisticsTimerVO);
        }
        return resMap;
    }

    private Boolean checkLeader(String employeeCode){
        EmployeeOrgVO employeeOrgVO = employeeOrgApi.queryEmployeeOrgInfo(employeeCode);
        if (null == employeeOrgVO) {
            return false;
        }
        return employeeCode.equals(employeeOrgVO.getBusinessTeamLeaderCode())
                || employeeCode.equals(employeeOrgVO.getBusinessAreaLeaderCode())
                || employeeCode.equals(employeeOrgVO.getBusinessDeptLeaderCode());
    }

    private List<MonthPunchIn> sortGroup(List<MonthPunchIn> monthPunchIns) {
        return monthPunchIns.stream().sorted(Comparator.comparing(MonthPunchIn::getSignInTime)).collect(Collectors.toList());
    }


}
