package com.hqins.agent.postsale.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2023/10/25 09:25
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class KmReviewParamterFormPublicResponse  implements Serializable {

    /**
     * 返回的id
     */
    private String result;

    @JsonProperty("BATCHID")
    private String batchId;

    @ApiModelProperty(value = "OA requestId", example = "OA requestId")
    private String requestId;

    @ApiModelProperty(value = "服务执行状态(true:服务执行成功,false:服务执行识别)")
    @JsonProperty("FLAG")
    public Boolean flag;

    @ApiModelProperty(value = "服务执行结果信息")
    @JsonProperty("MESSAGE")
    public String message="";

    @ApiModelProperty(value = "服务执行异常信息")
    @JsonProperty("EXCEPTION")
    public String exception;
}
