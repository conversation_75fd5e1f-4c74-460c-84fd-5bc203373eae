package com.hqins.agent.postsale.dto.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OAFormDataVo  implements Serializable {

    @ApiModelProperty(value = "数据表单code")
    public String fieldName;

    @ApiModelProperty(value = "数据表单值")
    public String fieldvalue;


}