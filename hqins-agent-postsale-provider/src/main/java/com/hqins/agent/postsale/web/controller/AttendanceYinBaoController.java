package com.hqins.agent.postsale.web.controller;

import cn.hutool.json.JSONUtil;
import com.hqins.agent.postsale.config.AttendanceConfig;
import com.hqins.agent.postsale.dao.entity.AgentAttendance;
import com.hqins.agent.postsale.dto.request.AttendanceSignInRequest;
import com.hqins.agent.postsale.dto.request.CheckOutRequest;
import com.hqins.agent.postsale.dto.request.DataStatisticsRequest;
import com.hqins.agent.postsale.dto.request.ReYinBaoSignRequest;
import com.hqins.agent.postsale.dto.response.AttendanceListResponse;
import com.hqins.agent.postsale.dto.response.SignOutResponse;
import com.hqins.agent.postsale.service.AttendanceYinBaoService;
import com.hqins.common.base.ApiResult;
import com.hqins.common.web.RequestContextHolder;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import java.util.Date;

/**
 * 出勤控制器
 * <AUTHOR>
 */
@Slf4j
@RestController
@Api(tags = "出勤系统控制器（新银保）")
@RequestMapping("/yinbao/attendance")
public class AttendanceYinBaoController {
    private final AttendanceYinBaoService attendanceYinBaoService;
    private final AttendanceConfig attendanceConfig;

    public AttendanceYinBaoController(AttendanceYinBaoService attendanceYinBaoService, AttendanceConfig attendanceConfig) {
        this.attendanceYinBaoService = attendanceYinBaoService;
        this.attendanceConfig = attendanceConfig;
    }

    @ApiOperation("批量补卡申请")
    @PostMapping("/supplement")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<Boolean> supplement(@RequestBody ReYinBaoSignRequest request) {
        log.info("attendanceYinBaoService supplement 入参 {} ,employee:{} ", JSONUtil.toJsonStr(request), RequestContextHolder.getEmployeeCode());
        return ApiResult.ok(attendanceYinBaoService.supplement(request));
    }

    @ApiOperation("银保-【签到】打卡")
    @PostMapping("/sign-in")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<Long> signIn(@RequestBody AttendanceSignInRequest request) {
        log.info("attendanceYinBaoService signIn 入参 {} ,employee:{} ", JSONUtil.toJsonStr(request), RequestContextHolder.getEmployeeCode());
        return ApiResult.ok(attendanceYinBaoService.signIn(request));
    }

    @ApiOperation("银保-【签退】打卡")
    @PostMapping("/sign-out")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<SignOutResponse> signOut(@RequestBody CheckOutRequest request) {
        log.info("attendanceYinBaoService signOut 入参 {} ,employee:{} ", JSONUtil.toJsonStr(request), RequestContextHolder.getEmployeeCode());
        return ApiResult.ok(attendanceYinBaoService.signOut(request));
    }

    @ApiOperation("银保-签到页面查询")
    @GetMapping("/data-day")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<AttendanceListResponse> dataDay(@RequestParam("orgCode")  String orgCode) {
        log.info("attendanceYinBaoService signOut 入参 {} ,employee:{} ", orgCode, RequestContextHolder.getEmployeeCode());
        return ApiResult.ok(attendanceYinBaoService.dataDay(orgCode));
    }


    @ApiOperation("查询隔天漏签退的数据")
    @GetMapping("/no-sign-out")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<AgentAttendance> noSignOut() {
        log.info("attendanceYinBaoService noSignOut ,employee:{} ", RequestContextHolder.getEmployeeCode());
        return ApiResult.ok(attendanceYinBaoService.noSignOut());
    }

    @ApiOperation("查询补卡次数")
    @PostMapping("/supplement-count")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<Integer> querySupplementCount(@RequestBody DataStatisticsRequest request) {
        log.info("attendanceYinBaoService querySupplementCount 入参 {} ,employee:{} ", JSONUtil.toJsonStr(request), RequestContextHolder.getEmployeeCode());
        return ApiResult.ok(attendanceYinBaoService.querySupplementCount(request));
    }


    @ApiOperation("查询新旧数据分割时间")
    @GetMapping("/new-time")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<Date> querySupplementCount() {
        return ApiResult.ok(attendanceConfig.getYinBaoStartTime());
    }
}
