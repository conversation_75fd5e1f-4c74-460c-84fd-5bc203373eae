package com.hqins.agent.postsale.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hqins.agent.marketing.api.PosterApi;
import com.hqins.agent.marketing.api.ScheduleApi;
import com.hqins.agent.marketing.model.request.taskagenda.ContactByAppointmentRequest;
import com.hqins.agent.org.model.api.EmployeeApi;
import com.hqins.agent.org.model.vo.EmployeeVO;
import com.hqins.agent.postsale.api.PolicyWrapper;
import com.hqins.agent.postsale.api.UmApiWrapper;
import com.hqins.agent.postsale.config.MessageTemplateConfig;
import com.hqins.agent.postsale.config.TemplateConfig;
import com.hqins.agent.postsale.dao.entity.InsurancePolicyIntention;
import com.hqins.agent.postsale.dao.entity.RenewalContactRecord;
import com.hqins.agent.postsale.dao.entity.RenewalSucceed;
import com.hqins.agent.postsale.dao.mapper.InsurancePolicyIntentionMapper;
import com.hqins.agent.postsale.dao.mapper.RenewalContactRecordMapper;
import com.hqins.agent.postsale.dao.mapper.RenewalSucceedMapper;
import com.hqins.agent.postsale.dto.InvalidPolicyRemindMsgDTO;
import com.hqins.agent.postsale.dto.NonPaymentDTO;
import com.hqins.agent.postsale.dto.NonPaymentValue;
import com.hqins.agent.postsale.dto.SimplePolicyDetailVO;
import com.hqins.agent.postsale.dto.request.PolicyRenewalContRequest;
import com.hqins.agent.postsale.dto.request.PolicyRenewalRequest;
import com.hqins.agent.postsale.dto.request.RecordSaveRequest;
import com.hqins.agent.postsale.dto.request.RenewalSucceedRequest;
import com.hqins.agent.postsale.dto.response.PolicyParticularResponse;
import com.hqins.agent.postsale.dto.response.PolicyReinstateResponse;
import com.hqins.agent.postsale.dto.response.RenewPolicyRisk;
import com.hqins.agent.postsale.dto.response.transRenew.RenewsSuccessShortDetailVO;
import com.hqins.agent.postsale.dto.response.transRenew.TransRenewsDetailsVO;
import com.hqins.agent.postsale.model.enums.MessageContentType;
import com.hqins.agent.postsale.model.enums.MessageSendChannel;
import com.hqins.agent.postsale.model.enums.PaymentFrequency;
import com.hqins.agent.postsale.model.enums.RenewPolicySearchType;
import com.hqins.agent.postsale.model.request.*;
import com.hqins.agent.postsale.model.vo.PayParameterVO;
import com.hqins.agent.postsale.model.vo.RenewSuccessSearchVO;
import com.hqins.agent.postsale.model.vo.RenewsSearchResponse;
import com.hqins.agent.postsale.model.vo.TransRenewsSearchVO;
import com.hqins.agent.postsale.service.MessageService;
import com.hqins.agent.postsale.service.RenewService;
import com.hqins.agent.postsale.utils.BeanConverter;
import com.hqins.agent.postsale.utils.DateUtils;
import com.hqins.common.base.constants.DatePatterns;
import com.hqins.common.base.enums.AgentOrgType;
import com.hqins.common.base.enums.IdType;
import com.hqins.common.base.errors.ApiException;
import com.hqins.common.base.errors.BadRequestException;
import com.hqins.common.base.page.PageInfo;
import com.hqins.common.helper.BeanCopier;
import com.hqins.common.helper.BeanFiller;
import com.hqins.common.utils.JsonUtil;
import com.hqins.common.utils.StringUtil;
import com.hqins.common.utils.TimeUtil;
import com.hqins.common.web.RequestContextHolder;
import com.hqins.ihome.health.api.EcServiceApi;
import com.hqins.ihome.health.model.request.QueryRealNameRequest;
import com.hqins.ihome.health.model.vo.EcRealNameInfoVO;
import com.hqins.policy.model.request.PolicyEsQueryRequestVo;
import com.hqins.policy.model.vo.es.EsQueryResponseVo;
import com.hqins.policy.model.vo.es.PolicyEsVo;
import com.hqins.policy.model.vo.es.RiskEsVo;
import com.hqins.thirdparty.api.RenewApi;
import com.hqins.thirdparty.model.enums.IdTypeMapping;
import com.hqins.thirdparty.model.enums.ThreePointSixIdTypeEnum;
import com.hqins.thirdparty.model.request.core.renew.*;
import com.hqins.thirdparty.model.request.ishare.transRenew.TransRenewListDTO;
import com.hqins.thirdparty.model.request.ishare.transRenew.TransRenewSuccessShortInfoRequest;
import com.hqins.thirdparty.model.vo.core.renew.*;
import com.hqins.thirdparty.model.vo.ishare.transRenew.*;
import com.hqins.um.model.dto.AccountInfoDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import one.util.streamex.StreamEx;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.ParseException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
@RefreshScope
@RequiredArgsConstructor
public class RenewServiceImpl implements RenewService {

    private final RenewApi renewApi;
    private final PolicyWrapper policyWrapper;
    private final InsurancePolicyIntentionMapper insurancePolicyIntentionMapper;
    private final UmApiWrapper umApiWrapper;
    private final MessageService messageService;
    private final PolicyServiceImpl policyServiceImpl;
    private final TemplateConfig templateConfig;
    private final RenewalContactRecordMapper renewalContactRecordMapper;
    private final RenewalSucceedMapper renewalSucceedMapper;
    private final EmployeeApi employeeApi;
    private final EcServiceApi ecServiceApi;
    private final PosterApi posterApi;
    private final ScheduleApi scheduleApi;

    @Value("${logReinstateUrl}")
    private String logReinstateUrl ;
    @Value("${logUrl}")
    private String logUrl ;

    @Override
    public List<RenewVO> list() {
        return BeanCopier.copyObject(renewApi.queryRenewList(), List.class);
    }

    @Override
    public List<RenewsSearchResponse> listByKey(QueryRenewRequest request) {
        String employeeCode = request.getEmployeeCode();
        if ("2".equals(request.getEmpFlag())){
            final EmployeeVO employeeInfo = employeeApi.getEmployeeInfo(AgentOrgType.valueOf(RequestContextHolder.getOrgType()), employeeCode);
            request.setEmployeeCode(employeeInfo.getJobNumber());
        }

        RenewsQueryRequest renewsQueryRequest = BeanCopier.copyObject(request, RenewsQueryRequest.class);

        List<RenewsSearchResponse> result = new ArrayList<>();
        List<RenewsSearchResponse> response;
        long startTime = System.currentTimeMillis();
        if (request.getPolicySearchType().equals(RenewPolicySearchType.TO_BE_CONTINUED.name())) {
//            return getRenewsToBeContinuedResponses(request);
            //查询待续交
            renewsQueryRequest.setPageNum(1);
            renewsQueryRequest.setPageCount(99999);
            renewsQueryRequest.setKey(request.getKey());
            long renewListByKeyStartTime = System.currentTimeMillis();
            renewApi.findRenewListByKey(renewsQueryRequest).forEach(info -> {
                if (CollectionUtils.isEmpty(info.getContList())) {
                    return;
                }
                info.getContList().forEach(cont -> {
                    if (CollectionUtils.isEmpty(cont.getInsuredList())) {
                        return;
                    }
                    String payToDate = cont.getPayToDate();
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                    LocalDate date = LocalDate.parse(payToDate, formatter);

                    final AirPayParameterVO build = AirPayParameterVO.builder()
                            .contNo(cont.getContNo())
                            .amount(cont.getSumDuePayMoney())
                            .certType(cont.getAppntIdType())
                            .certNo(cont.getAppntIdNo())
                            .payCount(cont.getPayCount())
                            .name(cont.getAppntName())
                            .nationalityCode(cont.getAppntNativePlace())
                            .payState(cont.getPayState())
                            .payToDate(cont.getPayToDate())
                            .isAvailable(cont.getIsAvailable())
                            .contState(cont.getContState())
                            .hangType(cont.getHangType())
                            .payMode(cont.getPayMode())
                            .build();

                    PayParameterVO payParameterVO = BeanCopier.copyObject(build, PayParameterVO.class);

                    Set<String> insuredNameList = new HashSet<>();
                    List<GWPolicyPerContPersonRisk> riskList = new ArrayList<>();
                    for (GWPolicyPerContPerson gwPolicyPerContPerson : cont.getInsuredList()) {
                        insuredNameList.add(gwPolicyPerContPerson.getInsuredName());
                        riskList.addAll(gwPolicyPerContPerson.getRiskList());
                    }
                    GWPolicyPerContPersonRisk maxPremRisk = Collections.max(riskList, Comparator.comparing( x -> Double.parseDouble(x.getPrem())));
                    RenewsSearchResponse renewsSearchResponse = RenewsSearchResponse.builder()
                            .mainRiskName(maxPremRisk.getRiskName())
                            .contNo(cont.getContNo())
                            .appntName(cont.getAppntName())
                            .insuredName(String.join("、", insuredNameList))
                            .payToDate(payToDate)
                            .sumDuePayMoney(cont.getSumDuePayMoney())
                            .endPayDate(date.plusDays(60).toString())
                            .appntMobile(cont.getAppntMobile())
                            .flag(Boolean.TRUE)
                            .payState(cont.getPayState())
                            .payCount(cont.getPayCount())
                            .airPayParameterVO(payParameterVO)
                            .build();

                    if (CollectionUtils.isNotEmpty(cont.getInsuredList()) && CollectionUtils.isNotEmpty(cont.getInsuredList().get(0).getRiskList())) {
                        renewsSearchResponse.setPayIntv(PaymentFrequency.fromValue(cont.getInsuredList().get(0).getRiskList().get(0).getPayIntv()).getName());
                    }
                    result.add(renewsSearchResponse);
                });
            });
            long renewListByKeyEndTime = System.currentTimeMillis();
            log.info("待续交核心APP028查询列表用时：" + ((renewListByKeyEndTime - renewListByKeyStartTime) / 1000.00) + "秒");
            List<String> contList = result.stream().map(RenewsSearchResponse::getContNo).collect(Collectors.toList());
            //ES渠道类型
            long esStartTime = System.currentTimeMillis();
            EsQueryResponseVo esQueryResponseVo = policyWrapper.queryPolicyList(PolicyEsQueryRequestVo.builder()
                    .contNoList(contList)
                    .size(contList.size())
                    .build());
            long esEndTime = System.currentTimeMillis();
            log.info("待续交ES查询列表用时：" + ((esEndTime - esStartTime) / 1000.00) + "秒");

            //提醒按钮
            long renewalSucceedStartTime = System.currentTimeMillis();
            List<RenewalSucceed> renewalSucceedList = renewalSucceedMapper.selectList(Wrappers.lambdaQuery(RenewalSucceed.class)
                    .eq(RenewalSucceed::getEmployeeCode, request.getEmployeeCode())
                    .eq(RenewalSucceed::getContStatus, RenewPolicySearchType.TO_BE_CONTINUED.name())
            );
            long renewalSucceedEndTime = System.currentTimeMillis();
            log.info("待续交提醒按钮查询列表用时：" + ((renewalSucceedEndTime - renewalSucceedStartTime) / 1000.00) + "秒");

            //意向度
            long insurancePolicyIntentionStartTime = System.currentTimeMillis();
            if (CollectionUtils.isEmpty(contList)) {
                contList.add("");
            }
            List<InsurancePolicyIntention> policyIntentionList = insurancePolicyIntentionMapper.selectList(Wrappers.lambdaQuery(InsurancePolicyIntention.class)
                    .in(InsurancePolicyIntention::getPolicyNo, contList));
            long insurancePolicyIntentionEndTime = System.currentTimeMillis();
            log.info("待续交意向度查询列表用时：" + ((insurancePolicyIntentionEndTime - insurancePolicyIntentionStartTime) / 1000.00) + "秒");

            long resultStartTime = System.currentTimeMillis();
            result.forEach(info -> {
                //ES渠道类型
                if (CollectionUtils.isNotEmpty(esQueryResponseVo.getDataList())) {
                    esQueryResponseVo.getDataList().forEach(data -> {
                        if (data.getContNo().equals(info.getContNo())) {
                            info.setPolicyChannel(data.getPolicyChannel());
                            info.setPayIntv(PaymentFrequency.fromValue(data.getPayIntv()).getName());
                            info.setCvaliDate(data.getCvaliDate());
                            info.setAppntNo(data.getAppntEsVo().getCustomerNo());
                            //险种名称 取主险M first one
                            /*final List<RiskEsVo> riskVoList = data.getRiskVoList();
                            if (CollectionUtils.isNotEmpty(riskVoList)) {
                                //todo 没用了
                                List<RiskEsVo> collect = riskVoList.stream().filter(risk -> "M".equals(risk.getPrimaryOrSecondary())).collect(Collectors.toList());
                                if (CollectionUtils.isNotEmpty(collect)) {
                                    RiskEsVo maxPremRisk = Collections.max(collect, Comparator.comparing(RiskEsVo::getPrem));
                                    info.setMainRiskName(maxPremRisk.getRiskName());
                                    //被保人 ES的被保人在险种里面
                                    info.setInsuredName(maxPremRisk.getInsuredName());
                                }
                            }*/
                        }
                    });
                }

                //意向度
                if (!PaymentFrequency.MONTHLY.getName().equals(PaymentFrequency.fromName(info.getPayIntv()).getName()) && CollectionUtils.isNotEmpty(policyIntentionList) && null != info.getCvaliDate() && StringUtils.isNotEmpty(info.getPayToDate())) {
                    InsurancePolicyIntention.IntentionType intentionTypeValue = getIntentionTypeValue(info.getCvaliDate(), info.getPayToDate());
                    if (null != intentionTypeValue) {
                        policyIntentionList.forEach(intention -> {
                            if (info.getContNo().equals(intention.getPolicyNo()) && intentionTypeValue.name().equals(intention.getIntentionType().name())) {
                                info.setIntention(intention.getRenewalIntention().toString());
                            }
                        });
                    }
                }

                //提醒按钮
                if (CollectionUtils.isNotEmpty(renewalSucceedList)) {
                    info.setIsRemind(Boolean.FALSE);
                    renewalSucceedList.forEach(succeed -> {
                        if ((succeed.getSucceedMark() + succeed.getPayToDay()).equals(info.getContNo() + info.getPayToDate())) {
                            info.setIsRemind(Boolean.TRUE);
                        }
                    });
                } else {
                    info.setIsRemind(Boolean.FALSE);
                }
            });
            long resultEndTime = System.currentTimeMillis();
            log.info("处理数据用时：" + ((resultEndTime - resultStartTime) / 1000.00) + "秒");

            long responseStartTime = System.currentTimeMillis();
            response = result.stream()
                    .filter(p -> !PaymentFrequency.MONTHLY.getName().equals(p.getPayIntv()))
                    .collect(Collectors.toList());
            // 找出那些payIntv不为月交的元素
            response.addAll(result.stream()
                    .filter(p -> PaymentFrequency.MONTHLY.getName().equals(p.getPayIntv()))
                    .collect(Collectors.toMap(
                            RenewsSearchResponse::getContNo,
                            p -> p,
                            (p1, p2) -> {
                                LocalDate parse = LocalDate.parse(p1.getPayToDate());
                                LocalDate parse2 = LocalDate.parse(p2.getPayToDate());
                                if (parse.isBefore(parse2)) {
                                    return p1;
                                } else {
                                    return p2;
                                }
                            }
                    )).values());
            long responseEndTime = System.currentTimeMillis();
            log.info("过滤月交件用时：" + ((responseEndTime - responseStartTime) / 1000.00) + "秒");

            long endTime = System.currentTimeMillis();
            log.info("查询列表用时：" + ((endTime - startTime) / 1000.00) + "秒");

            return response.stream().filter(item -> StringUtils.isNotEmpty(item.getSumDuePayMoney())).collect(Collectors.toList());
        }else {
//            return  getRenewsPendingRecoveryResponses(request);
            long renewListByKeyStartTime = System.currentTimeMillis();
            GWRenewReinstateRspDTO renewReinstate = renewApi.findRenewReinstate(RenewsReinstateRequest.builder().agentCode(request.getEmployeeCode()).orgType(request.getOrgType()).build());
            long renewListByKeyEndTime = System.currentTimeMillis();
            log.info("核心待复效查询列表用时：" + ((renewListByKeyEndTime - renewListByKeyStartTime) / 1000.00) + "秒");

            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            if(CollectionUtils.isNotEmpty(renewReinstate.getStateList())){

                renewReinstate.getStateList().forEach(info->{
                    String invalidDate = info.getInvalidDate();
                    RenewsSearchResponse renewsSearchResponse = RenewsSearchResponse.builder()
                            .contNo(info.getContNo())
                            .expiryDate(invalidDate)
                            .failureCause(info.getInvalidReason())
                            .expirationDate(LocalDate.parse(invalidDate, formatter).plusYears(2).toString())
                            .flag(Boolean.FALSE)
                            .build();
                    result.add(renewsSearchResponse);
                });

                List<String> contList = result.stream().map(RenewsSearchResponse::getContNo).collect(Collectors.toList());
                //其他查询ES
                long esStartTime = System.currentTimeMillis();
                EsQueryResponseVo esQueryResponseVo = policyWrapper.queryPolicyList(PolicyEsQueryRequestVo.builder()
                        .contNoList(contList)
                        .size(contList.size())
                        .build());
                long esEndTime = System.currentTimeMillis();
                log.info("待复效查询ES列表用时：" + ((esEndTime - esStartTime) / 1000.00) + "秒");
                //提醒按钮
                List<RenewalSucceed> renewalSucceedList = renewalSucceedMapper.selectList(Wrappers.lambdaQuery(RenewalSucceed.class)
                        .eq(RenewalSucceed::getEmployeeCode, request.getEmployeeCode())
                        .eq(RenewalSucceed::getContStatus,RenewPolicySearchType.PENDING_RECOVERY.name())
                );

                result.forEach(info->{
                    //匹配保单详情
                    if (CollectionUtils.isNotEmpty(esQueryResponseVo.getDataList())){
                        esQueryResponseVo.getDataList().forEach(data->{
                            if (data.getContNo().equals(info.getContNo())){
                                info.setAirPayParameterVO(BeanCopier.copyObject(AirPayParameterVO.builder()
                                        .contNo(data.getContNo())
                                        .certType(data.getAppntEsVo().getIdType())
                                        .certNo(data.getAppntEsVo().getIdNo())
                                        .name(info.getAppntName())
                                        .build(),PayParameterVO.class));

                                //险种名称 取主险M first one
                                final List<RiskEsVo> riskVoList = data.getRiskVoList();
                                if (CollectionUtils.isNotEmpty(riskVoList)) {
                                    List<RiskEsVo> collect = riskVoList.stream().filter(risk -> "M".equals(risk.getPrimaryOrSecondary())).collect(Collectors.toList());
                                    if (CollectionUtils.isNotEmpty(collect)) {
                                        RiskEsVo maxPremRisk = Collections.max(collect, Comparator.comparing(RiskEsVo::getPrem));
                                        info.setMainRiskName(maxPremRisk.getRiskName());
                                        //被保人 ES的被保人在险种里面
                                        info.setInsuredName(maxPremRisk.getInsuredName());
                                    }
                                }
                                info.setAppntName(data.getAppntEsVo().getName());
                                info.setAppntNo(data.getAppntEsVo().getCustomerNo());
                                info.setAppntMobile(data.getAppntEsVo().getMobile());
                                info.setPrtNo(data.getProposalContNo());
                                info.setSumDuePayMoney(data.getSumPrem().toString());
                                info.setPayIntv(PaymentFrequency.fromValue(data.getPayIntv()).getName());
                                info.setPolicyChannel(data.getPolicyChannel());
                            }
                        });
                    }

                    //提醒按钮
                    if (CollectionUtils.isNotEmpty(renewalSucceedList)){
                        renewalSucceedList.forEach(succeed->{
                            if ((succeed.getSucceedMark()+succeed.getPayToDay()).equals(info.getContNo()+info.getPayToDate())){
                                info.setIsRemind(Boolean.TRUE);
                            }else {
                                info.setIsRemind(Boolean.FALSE);
                            }
                        });
                    }else {
                        info.setIsRemind(Boolean.FALSE);
                    }
                });

            }

        }
        long endTime = System.currentTimeMillis();
        log.info("查询列表用时：" + ((endTime - startTime) / 1000.00) + "秒");
        return result;
    }

    private List<RenewsSearchResponse> getRenewsPendingRecoveryResponses(QueryRenewRequest request) {
        List<RenewsSearchResponse> response = new ArrayList<>();
        long renewListByKeyStartTime = System.currentTimeMillis();
        GWRenewReinstateRspDTO renewReinstate = renewApi.findRenewReinstate(RenewsReinstateRequest.builder().agentCode(request.getEmployeeCode()).orgType(request.getOrgType()).build());
        long renewListByKeyEndTime = System.currentTimeMillis();
        log.info("核心待复效查询列表用时：" + ((renewListByKeyEndTime - renewListByKeyStartTime) / 1000.00) + "秒");

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        if(CollectionUtils.isEmpty(renewReinstate.getStateList())){
            return response;
        }

        List<String> contList = renewReinstate.getStateList().stream().map(GWRenewReinstateStateVO::getContNo).collect(Collectors.toList());
        //ES渠道类型
        long esStartTime = System.currentTimeMillis();
        EsQueryResponseVo esQueryResponseVo = policyWrapper.queryPolicyList(PolicyEsQueryRequestVo.builder()
                .contNoList(contList)
                .agentCodeList(Collections.singletonList(request.getEmployeeCode()))
                .size(contList.size())
                .build());
        Map<String, PolicyEsVo> esVoMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(esQueryResponseVo.getDataList())) {
            esVoMap = esQueryResponseVo.getDataList().stream().collect(Collectors.toMap(PolicyEsVo::getContNo, Function.identity(), (k1, k2) -> k1));
        }
        long esEndTime = System.currentTimeMillis();
        log.info("待续交ES查询列表用时：" + ((esEndTime - esStartTime) / 1000.00) + "秒");

        //提醒按钮
        long renewalSucceedStartTime = System.currentTimeMillis();
        List<RenewalSucceed> renewalSucceedList = renewalSucceedMapper.selectList(Wrappers.lambdaQuery(RenewalSucceed.class)
                .eq(RenewalSucceed::getEmployeeCode, request.getEmployeeCode())
                .eq(RenewalSucceed::getContStatus, RenewPolicySearchType.TO_BE_CONTINUED.name())
        );
        Map<String, RenewalSucceed> renewalSucceedMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(renewalSucceedList)){
            renewalSucceedMap = renewalSucceedList.stream().collect(Collectors.toMap(x -> x.getSucceedMark() + x.getPayToDay(), Function.identity(), (k1, k2) -> k1));
        }
        long renewalSucceedEndTime = System.currentTimeMillis();
        log.info("待续交提醒按钮查询列表用时：" + ((renewalSucceedEndTime - renewalSucceedStartTime) / 1000.00) + "秒");
        for (GWRenewReinstateStateVO reinstateStateVO : renewReinstate.getStateList()) {
            String invalidDate = reinstateStateVO.getInvalidDate();
            RenewsSearchResponse renewsSearchResponse = RenewsSearchResponse.builder()
                    .contNo(reinstateStateVO.getContNo())
                    .expiryDate(invalidDate)
                    .failureCause(reinstateStateVO.getInvalidReason())
                    .expirationDate(LocalDate.parse(invalidDate, formatter).plusYears(2).toString())
                    .flag(Boolean.FALSE)
                    .build();

            //ES渠道类型
            PolicyEsVo policyEsVo = esVoMap.get(renewsSearchResponse.getContNo());
            if (policyEsVo != null) {
                renewsSearchResponse.setAirPayParameterVO(BeanCopier.copyObject(AirPayParameterVO.builder()
                        .contNo(policyEsVo.getContNo())
                        .certType(policyEsVo.getAppntEsVo().getIdType())
                        .certNo(policyEsVo.getAppntEsVo().getIdNo())
                        .name(policyEsVo.getAppntEsVo().getName())
                        .build(),PayParameterVO.class));
                renewsSearchResponse.setAppntNo(policyEsVo.getAppntEsVo().getCustomerNo());
                renewsSearchResponse.setPolicyChannel(policyEsVo.getPolicyChannel());
                renewsSearchResponse.setPayIntv(PaymentFrequency.fromValue(policyEsVo.getPayIntv()).getName());
                renewsSearchResponse.setCvaliDate(policyEsVo.getCvaliDate());
                //险种名称 取主险M first one
                final List<RiskEsVo> riskVoList = policyEsVo.getRiskVoList();
                if (CollectionUtils.isNotEmpty(riskVoList)) {
                    List<RiskEsVo> collect = riskVoList.stream().filter(risk -> "M".equals(risk.getPrimaryOrSecondary())).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(collect)) {
                        RiskEsVo maxPremRisk = Collections.max(collect, Comparator.comparing(RiskEsVo::getPrem));
                        renewsSearchResponse.setMainRiskName(maxPremRisk.getRiskName());
                        //被保人 ES的被保人在险种里面
                        renewsSearchResponse.setInsuredName(maxPremRisk.getInsuredName());
                    }
                }
            }
            //提醒按钮
            RenewalSucceed renewalSucceed = renewalSucceedMap.get(renewsSearchResponse.getContNo() + renewsSearchResponse.getPayToDate());
            if (renewalSucceed != null) {
                renewsSearchResponse.setIsRemind(Boolean.TRUE);
            } else {
                renewsSearchResponse.setIsRemind(Boolean.FALSE);
            }
            response.add(renewsSearchResponse);
        }

        return response;
    }


    @NotNull
    private List<RenewsSearchResponse> getRenewsToBeContinuedResponses(QueryRenewRequest request) {
        List<RenewsSearchResponse> response = new ArrayList<>();
        List<RenewsSearchResponse> result = new ArrayList<>();
        //查询待续交
        long startTime = System.currentTimeMillis();
        RenewsQueryRequest renewsQueryRequest = BeanCopier.copyObject(request, RenewsQueryRequest.class);
        renewsQueryRequest.setPageNum(1);
        renewsQueryRequest.setPageCount(99999);
        renewsQueryRequest.setKey(request.getKey());
        long renewListByKeyStartTime = System.currentTimeMillis();
        List<GWRenewQueryRspStateDTO> renewListByKey = renewApi.findRenewListByKey(renewsQueryRequest);
        long renewListByKeyEndTime = System.currentTimeMillis();
        log.info("待续交核心APP028查询列表用时：" + ((renewListByKeyEndTime - renewListByKeyStartTime) / 1000.00) + "秒");

        if (CollectionUtils.isEmpty(renewListByKey) && CollectionUtils.isEmpty(renewListByKey.get(0).getContList())){
            return response;
        }

        List<String> contList = renewListByKey.get(0).getContList().stream().map(GWRenewQueryRspContDTO::getContNo).collect(Collectors.toList());
        //ES渠道类型
        long esStartTime = System.currentTimeMillis();
        EsQueryResponseVo esQueryResponseVo = policyWrapper.queryPolicyList(PolicyEsQueryRequestVo.builder()
                .contNoList(contList)
                .agentCodeList(Collections.singletonList(request.getEmployeeCode()))
                .size(contList.size())
                .build());
        Map<String, PolicyEsVo> esVoMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(esQueryResponseVo.getDataList())) {
            esVoMap = esQueryResponseVo.getDataList().stream().collect(Collectors.toMap(PolicyEsVo::getContNo, Function.identity(), (k1, k2) -> k1));
        }
        long esEndTime = System.currentTimeMillis();
        log.info("待续交ES查询列表用时：" + ((esEndTime - esStartTime) / 1000.00) + "秒");

        //提醒按钮
        long renewalSucceedStartTime = System.currentTimeMillis();
        List<RenewalSucceed> renewalSucceedList = renewalSucceedMapper.selectList(Wrappers.lambdaQuery(RenewalSucceed.class)
                .eq(RenewalSucceed::getEmployeeCode, request.getEmployeeCode())
                .eq(RenewalSucceed::getContStatus, RenewPolicySearchType.TO_BE_CONTINUED.name())
        );
        Map<String, RenewalSucceed> renewalSucceedMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(renewalSucceedList)){
            renewalSucceedMap = renewalSucceedList.stream().collect(Collectors.toMap(x -> x.getSucceedMark() + x.getPayToDay(), Function.identity(), (k1, k2) -> k1));
        }
        long renewalSucceedEndTime = System.currentTimeMillis();
        log.info("待续交提醒按钮查询列表用时：" + ((renewalSucceedEndTime - renewalSucceedStartTime) / 1000.00) + "秒");

        //意向度
        long insurancePolicyIntentionStartTime = System.currentTimeMillis();
        if (CollectionUtils.isEmpty(contList)) {
            contList.add("");
        }
        List<InsurancePolicyIntention> policyIntentionList = insurancePolicyIntentionMapper.selectList(Wrappers.lambdaQuery(InsurancePolicyIntention.class)
                .in(InsurancePolicyIntention::getPolicyNo, contList));
        Map<String, InsurancePolicyIntention> insurancePolicyIntentionMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(policyIntentionList)){
            insurancePolicyIntentionMap = policyIntentionList.stream().collect(Collectors.toMap(InsurancePolicyIntention::getPolicyNo, Function.identity(), (K1, K2) -> K1));
        }
        long insurancePolicyIntentionEndTime = System.currentTimeMillis();
        log.info("待续交意向度查询列表用时：" + ((insurancePolicyIntentionEndTime - insurancePolicyIntentionStartTime) / 1000.00) + "秒");

        long resultStartTime = System.currentTimeMillis();
        for (GWRenewQueryRspStateDTO renewQueryRspStateDTO : renewListByKey) {
            if (CollectionUtils.isNotEmpty(renewQueryRspStateDTO.getContList())) {
                List<GWRenewQueryRspContDTO> queryRspStateDTOContList = renewQueryRspStateDTO.getContList();
                if (CollectionUtils.isNotEmpty(queryRspStateDTOContList)){
                    for (GWRenewQueryRspContDTO cont : queryRspStateDTOContList) {

                        String payToDate = cont.getPayToDate();
                        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                        LocalDate date = LocalDate.parse(payToDate, formatter);

                        final AirPayParameterVO build = AirPayParameterVO.builder()
                                .contNo(cont.getContNo())
                                .amount(cont.getSumDuePayMoney())
                                .certType(cont.getAppntIdType())
                                .certNo(cont.getAppntIdNo())
                                .payCount(cont.getPayCount())
                                .name(cont.getAppntName())
                                .nationalityCode(cont.getAppntNativePlace())
                                .payState(cont.getPayState())
                                .payToDate(cont.getPayToDate())
                                .isAvailable(cont.getIsAvailable())
                                .contState(cont.getContState())
                                .hangType(cont.getHangType())
                                .payMode(cont.getPayMode())
                                .build();

                        PayParameterVO payParameterVO = BeanCopier.copyObject(build, PayParameterVO.class);

                        if (CollectionUtils.isNotEmpty(cont.getInsuredList())){
                            RenewsSearchResponse renewsSearchResponse = RenewsSearchResponse.builder()
                                    .mainRiskName(cont.getInsuredList().get(0).getRiskList().get(0).getRiskName())
                                    .contNo(cont.getContNo())
                                    .appntName(cont.getAppntName())
                                    .insuredName(cont.getInsuredList().get(0).getInsuredName())
                                    .payToDate(payToDate)
                                    .sumDuePayMoney(cont.getSumDuePayMoney())
                                    .endPayDate(date.plusDays(60).toString())
                                    .appntMobile(cont.getAppntMobile())
                                    .flag(Boolean.TRUE)
                                    .payState(cont.getPayState())
                                    .payCount(cont.getPayCount())
                                    .airPayParameterVO(payParameterVO)
                                    .build();


                            if (CollectionUtils.isNotEmpty(cont.getInsuredList()) &&
                                    CollectionUtils.isNotEmpty(cont.getInsuredList().get(0).getRiskList())) {
                                renewsSearchResponse.setPayIntv(PaymentFrequency.fromValue(cont.getInsuredList().get(0).getRiskList().get(0).getPayIntv()).getName());
                            }
                            //ES渠道类型
                            PolicyEsVo policyEsVo = esVoMap.get(renewsSearchResponse.getContNo());
                            if (policyEsVo != null) {
                                renewsSearchResponse.setPolicyChannel(policyEsVo.getPolicyChannel());
                                renewsSearchResponse.setAppntNo(policyEsVo.getAppntEsVo().getCustomerNo());
                                renewsSearchResponse.setPayIntv(PaymentFrequency.fromValue(policyEsVo.getPayIntv()).getName());
                                renewsSearchResponse.setCvaliDate(policyEsVo.getCvaliDate());
                                //险种名称 取主险M first one
                                final List<RiskEsVo> riskVoList = policyEsVo.getRiskVoList();
                                if (CollectionUtils.isNotEmpty(riskVoList)) {
                                    List<RiskEsVo> collect = riskVoList.stream().filter(risk -> "M".equals(risk.getPrimaryOrSecondary())).collect(Collectors.toList());
                                    if (CollectionUtils.isNotEmpty(collect)) {
                                        RiskEsVo maxPremRisk = Collections.max(collect, Comparator.comparing(RiskEsVo::getPrem));
                                        renewsSearchResponse.setMainRiskName(maxPremRisk.getRiskName());
                                        //被保人 ES的被保人在险种里面
                                        renewsSearchResponse.setInsuredName(maxPremRisk.getInsuredName());
                                    }
                                }
                            }

                            //意向度
                            if (!PaymentFrequency.MONTHLY.getName().equals(PaymentFrequency.fromName(renewsSearchResponse.getPayIntv()).getName())
                                    && CollectionUtils.isNotEmpty(policyIntentionList)
                                    && null != renewsSearchResponse.getCvaliDate()
                                    && StringUtils.isNotEmpty(renewsSearchResponse.getPayToDate())) {
                                InsurancePolicyIntention.IntentionType intentionTypeValue = getIntentionTypeValue(renewsSearchResponse.getCvaliDate(), renewsSearchResponse.getPayToDate());
                                if (null != intentionTypeValue) {
                                    InsurancePolicyIntention intention = insurancePolicyIntentionMap.get(renewsSearchResponse.getContNo());
                                    if (intention != null && intentionTypeValue.name().equals(intention.getIntentionType().name())){
                                        renewsSearchResponse.setIntention(intention.getRenewalIntention().toString());
                                    }

                                }
                            }
                            //提醒按钮
                            RenewalSucceed renewalSucceed = renewalSucceedMap.get(renewsSearchResponse.getContNo() + renewsSearchResponse.getPayToDate());
                            if (renewalSucceed != null) {
                                renewsSearchResponse.setIsRemind(Boolean.TRUE);
                            } else {
                                renewsSearchResponse.setIsRemind(Boolean.FALSE);
                            }

                            result.add(renewsSearchResponse);
                        }
                    }
                }
            }
        }
        long resultEndTime = System.currentTimeMillis();
        log.info("处理数据用时：" + ((resultEndTime - resultStartTime) / 1000.00) + "秒");

        long responseStartTime = System.currentTimeMillis();
        response = result.stream()
                .filter(p -> !PaymentFrequency.MONTHLY.getName().equals(p.getPayIntv()))
                .collect(Collectors.toList());
        // 找出那些payIntv不为月交的元素
        response.addAll(result.stream()
                .filter(p -> PaymentFrequency.MONTHLY.getName().equals(p.getPayIntv()))
                .collect(Collectors.toMap(
                        RenewsSearchResponse::getContNo,
                        p -> p,
                        (p1, p2) -> {
                            LocalDate parse = LocalDate.parse(p1.getPayToDate());
                            LocalDate parse2 = LocalDate.parse(p2.getPayToDate());
                            if (parse.isBefore(parse2)) {
                                return p1;
                            } else {
                                return p2;
                            }
                        }
                )).values());
        long responseEndTime = System.currentTimeMillis();
        log.info("过滤月交件用时：" + ((responseEndTime - responseStartTime) / 1000.00) + "秒");

        long endTime = System.currentTimeMillis();
        log.info("查询列表用时：" + ((endTime - startTime) / 1000.00) + "秒");

        return response.stream().filter(item -> StringUtils.isNotEmpty(item.getSumDuePayMoney())).collect(Collectors.toList());
    }


    @Override
    public DeductionVO deduction(String contNo) {
        return BeanCopier.copyObject(renewApi.queryLastPayInfo(contNo), DeductionVO.class);
    }

    @Override
    public AirPayVO airPay(AirPayRequest request) {
        return BeanCopier.copyObject(renewApi.airPay(BeanCopier.copyObject(request, com.hqins.thirdparty.model.request.core.renew.AirPayRequest.class)), AirPayVO.class);
    }

    @Override
    public void nonPayment(NonPaymentDTO nonPaymentDTO) {
        log.info("续期催缴提醒,入参nonPaymentDTO:{}", JsonUtil.toJSON(nonPaymentDTO));

        if (CollectionUtils.isEmpty(nonPaymentDTO.getDataContent().getValue())) {
            log.info("续期催缴提醒，保单数据为空，流程结束");
            return;
        }
        log.info("续期催缴提醒,核心推送保单数量为={}", nonPaymentDTO.getDataContent().getValue().size());

        List<String> contList = nonPaymentDTO.getDataContent().getValue().stream().map(NonPaymentValue::getContNo).distinct().collect(Collectors.toList());

        EsQueryResponseVo esQueryResponseVo = policyWrapper.queryPolicyList(PolicyEsQueryRequestVo.builder()
                .contNoList(contList)
                .size(contList.size())
                .build());
        Map<String, PolicyEsVo> policyEsVoMap;
        if (CollectionUtils.isNotEmpty(esQueryResponseVo.getDataList())) {
            policyEsVoMap = esQueryResponseVo.getDataList().stream().collect(Collectors.toMap(PolicyEsVo::getContNo, Function.identity(), (k1, k2) -> k1));
        } else {
            policyEsVoMap = null;
        }

        //批量查询人员信息
        Set<String> codes = new HashSet<>();
        nonPaymentDTO.getDataContent().getValue().forEach(nonpayment -> {
            if (StringUtil.isNotBlank(nonpayment.getAgentCode())) {
                codes.add(nonpayment.getAgentCode());
            }

            if (StringUtil.isNotBlank(nonpayment.getZjAgentCode())) {
                codes.add(nonpayment.getZjAgentCode());
            }
        });

        List<String> codeList = new ArrayList<>(codes);
        List<List<String>> agentIds = com.google.common.collect.Lists.partition(codeList, 100);
        List<AccountInfoDTO> userInfos = new ArrayList<>();

        agentIds.forEach(info -> {
            List<AccountInfoDTO> agentsBatch = umApiWrapper.queryAccountInfosByEmployeeCodeList(info);
            if (CollectionUtils.isNotEmpty(agentsBatch)) {
                userInfos.addAll(agentsBatch);
            }
        });

        Map<String, AccountInfoDTO> userInfoMap = new HashMap<>();
        userInfos.forEach(userInfo -> userInfoMap.put(userInfo.getAgentInfo().getEmployeeCode(), userInfo));

        //构造消息提醒数据
        List<MessageAddRequest> messageList = new ArrayList<>();
        List<WxMessageRequest> wxMessageRequest = new ArrayList<>();
        nonPaymentDTO.getDataContent().getValue().forEach(nonpayment -> {
            //应交费日期 yyyy-MM-dd
            String payToDate = nonpayment.getPayToDate();
            if (StringUtil.isBlank(payToDate)) {
                log.info("续期催缴提醒getPayToDate为空,核心推过的来的数据为：{}", JsonUtil.toJSON(nonpayment));
                return;
            }

            long daysSub = 0;
            try {
                daysSub = DateUtils.getDaysSub(payToDate);
            } catch (ParseException e) {
                log.error("续期催缴提醒,日期转换异常,消息忽略");
                return;
            }

            if (-30 == daysSub) {//后30天
                daysSub = 30;
            } else if (-50 == daysSub) {//后50天
                daysSub = 50;
            } else {//非后30天和后50天消息不推送
                return;
            }
            log.info("续期催缴提醒,未缴费保单号为={},计算未交费天数={}", nonpayment.getContNo(), daysSub);

            Set<String> agentCodes = new HashSet<>(2);
            if (StringUtil.isNotBlank(nonpayment.getAgentCode())) {
                agentCodes.add(nonpayment.getAgentCode());
            }

            if (StringUtil.isNotBlank(nonpayment.getZjAgentCode())) {
                agentCodes.add(nonpayment.getZjAgentCode());
            }

            if (CollectionUtils.isEmpty(agentCodes)) {
                log.error("续期催缴提醒,推送信息没有代理人工号，流程结束");
                return;
            }

            long finalDaysSub = daysSub;
            agentCodes.forEach(agentCode -> {
                if (null == userInfoMap.get(agentCode)) {
                    log.error("续期催缴提醒,人员信息未查到,核心推过的来的数据工号为：{}", agentCode);
                    return;
                }
                Long agentId = userInfoMap.get(agentCode).getAgentInfo().getAgentId();
                String employeeCode = userInfoMap.get(agentCode).getAgentInfo().getEmployeeCode();
                if (null == agentId || StringUtil.isEmpty(employeeCode)) {
                    log.error("续期催缴提醒,UM人员信息不完整,核心推过的来的数据工号为：{}", employeeCode);
                    return;
                }
                if (StringUtil.isEmpty(nonpayment.getContNo())) {
                    log.info("续期催缴提醒getContNo为空,消息忽略,employeeCode:{}", employeeCode);
                    return;
                }

                String payIntv = "";
                if (Objects.nonNull(policyEsVoMap) && Objects.nonNull(policyEsVoMap.get(nonpayment.getContNo()))) {
                    try {
                        payIntv = URLEncoder.encode(PaymentFrequency.fromValue(policyEsVoMap.get(nonpayment.getContNo()).getPayIntv()).getName(), "UTF-8");
                    } catch (UnsupportedEncodingException e) {
                        log.error("urlEnder 失败", e);
                        return;
                    }
                }
                //站内消息地址
                String url = "/renewal-policy-detail?type=TO_BE_CONTINUED&payState=0&contNo=" + nonpayment.getContNo() + "&payCount=" + nonpayment.getPayCount()
                        + "&payToDate=" + nonpayment.getPayToDate() + "&payIntv=" + payIntv;
                AggrSendMessageRequest aggrSendMessageRequest = new AggrSendMessageRequest();
                aggrSendMessageRequest.setEmployeeCode(employeeCode);
                aggrSendMessageRequest.setMessageContentType(MessageContentType.RENEW_COLLECTION_ALERT.name());
                List<MessageSendChannel> messageSendChannelList = new ArrayList<>();
                messageSendChannelList.add(MessageSendChannel.WX);
                messageSendChannelList.add(MessageSendChannel.LOCAL);
                messageSendChannelList.add(MessageSendChannel.PUSH);
                aggrSendMessageRequest.setMessageSendChannelList(messageSendChannelList);
                aggrSendMessageRequest.setKey1(nonpayment.getAppntName());
                aggrSendMessageRequest.setKey2(nonpayment.getContNo());
                aggrSendMessageRequest.setKey3(String.valueOf(finalDaysSub));
                aggrSendMessageRequest.setUrl(url);
                messageService.aggrSendMsg(aggrSendMessageRequest);
            });
        });


        if (CollectionUtils.isNotEmpty(messageList) && CollectionUtils.isNotEmpty(wxMessageRequest)) {
            log.info("续期催缴提醒,实际发送消息人数={}", wxMessageRequest.size());
            messageService.batchAdd(BatchMessageAddRequest.builder().messageList(messageList).build());
            messageService.wxMessage(wxMessageRequest);
        }
    }

    @Override
    public void contactRecordSave(RecordSaveRequest request) {
        RenewalContactRecord renewalContactRecord = BeanCopier.copyObject(request, RenewalContactRecord.class);
        renewalContactRecord.setStatus(Boolean.FALSE);
        if (null ==  request.getContactByAppointment()){
            renewalContactRecord.setStatus(Boolean.TRUE);
        }
        //添加任务to marketing
        if (request.getContactByAppointment() != null){
            scheduleApi.contactByAppointment(ContactByAppointmentRequest.builder()
                    .contNo(request.getPolicyNo())
                    .riskType(request.getContStatus())
                    .customerName(request.getAppntName())
                    .customerNo(request.getCustomerNo())
                    .contactByAppointmentTime(request.getContactByAppointment())
                    .agentName(request.getAppntName())
                    .agentCode(request.getEmployeeCode())
                    .customerMobile(request.getAppntMobile())
                    .riskName(request.getMainRiskName())
                    .policyNo(request.getPolicyNo())
                    .urlParams(request.getUrlParams())
                    .build());
        }


        renewalContactRecordMapper.insertSelective(renewalContactRecord);
    }

    @Override
    public void remindSuccess(RenewalSucceedRequest request) {

        if ("2".equals(request.getEmpFlag())) {
            final EmployeeVO employeeInfo = employeeApi.getEmployeeInfo(AgentOrgType.valueOf(RequestContextHolder.getOrgType()), request.getEmployeeCode());
            request.setEmployeeCode(employeeInfo.getJobNumber());
        }

        if (request.getFlag()){
            renewalSucceedMapper.insertSelective(RenewalSucceed.builder()
                    .succeedMark(request.getPolicyNo())
                    .payToDay(request.getEffectiveDate())
                    .contStatus(request.getContStatus())
                    .employeeCode(request.getEmployeeCode())
                    .build());
            }else {
                RenewalSucceed renewalSucceed = renewalSucceedMapper.selectOne(Wrappers.lambdaQuery(RenewalSucceed.class)
                        .eq(RenewalSucceed::getSucceedMark, request.getPolicyNo())
                        .eq(RenewalSucceed::getPayToDay, request.getEffectiveDate())
                        .eq(RenewalSucceed::getEmployeeCode, request.getEmployeeCode())
                        .last("limit 1")
            );
            renewalSucceedMapper.deleteById(renewalSucceed);
        }
        if (!"2".equals(request.getEmpFlag()) && !request.getType().equals(MessageContentType.RENEW_TRANS_RENEWS.name())){
            posterApi.readRenew(request.getPolicyNo(),request.getEmployeeCode(),request.getEffectiveDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        }
    }

    @Override
    public PolicyParticularResponse policyParticular( PolicyRenewalContRequest request) {
        if (request == null || StringUtils.isBlank(request.getContNo())){
            throw  new BadRequestException("入参异常，请稍后再试！");
        }
        try {
            PolicyParticularResponse result = new PolicyParticularResponse();

            String payIntv = request.getPayIntv();
            if (StringUtils.isNotEmpty(request.getPayState()) && request.getPayState().equals("2")){
                request.setPayState("2");
            }else {
                request.setPayState("0");
            }
            RenewalRequest renewalRequest = BeanCopier.copyObject(request, RenewalRequest.class);
            renewalRequest.setPaytoDate(request.getPayToDate());
            DetailsOfRenewalVO detailsOfRenewalVO ;
            try {
                 detailsOfRenewalVO = renewApi.queryDetailsOfRenewal(renewalRequest);
            }catch (Exception e){
                renewalRequest.setPayState("2");
                detailsOfRenewalVO = renewApi.queryDetailsOfRenewal(renewalRequest);
            }
            DetailsOfContRenewalVO detailsOfContRenewalVO = renewApi.queryDetailsRenewal(RenewalContRequest.builder().contNo(request.getContNo()).build());
            //查询ES
            PolicyEsQueryRequestVo build = PolicyEsQueryRequestVo.builder()
                    .from(0)
                    .size(10)
                    .contNoList(Collections.singletonList(request.getContNo()))
                    .build();
            EsQueryResponseVo esData = policyServiceImpl.queryPolicy(build);
            DeductionVO deductionVO = null;
            try {
                deductionVO = renewApi.queryLastPayInfo(request.getContNo());
            }catch (Exception e){
                log.error("查询到最近扣款记录:::::error:{}", e.getMessage());

            }


            if(null != deductionVO && !"成功".equals(deductionVO.getPayResult())){
                String[] split = deductionVO.getPayResult().split("-");
                result.setFailReason(split[1]);
                result.setFailDate(deductionVO.getPayDate().toString());
            }

            Map<String, RiskEsVo> esVoMap = new HashMap<>();
            if (null != esData &&  esData.getTotal()>0){
                PolicyEsVo policyEsVo = esData.getDataList().get(0);
                result.setPartnerOrgName(policyEsVo.getOrgName());
                result.setPolicyStatus(policyServiceImpl.payModeByStr(policyEsVo.getStatusForMyPolicys()));
                result.setChannelOrgName(policyEsVo.getChannelOrgName());
                result.setChannelAgentName(policyEsVo.getChannelAgentName());
                result.setPolicyChannel(policyEsVo.getPolicyChannel());
                if ("03".equals(policyEsVo.getSaleChnl())){
                    result.setChannelAgentName(policyEsVo.getBankAgentName());
                }
                List<RiskEsVo> riskVoList = policyEsVo.getRiskVoList();
                if (CollectionUtils.isNotEmpty(riskVoList)) {
                    List<RiskEsVo> collect = riskVoList.stream().filter(risk -> "M".equals(risk.getPrimaryOrSecondary())).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(collect)) {
                        RiskEsVo max = Collections.max(collect, Comparator.comparing(RiskEsVo::getPrem));
                        result.setPayEndYear(policyServiceImpl.makePaymentPeriod(max));
                        result.setPayIntv(policyServiceImpl.payModeByStr(max.getPayIntv()));
                    }
                }
                payIntv = policyServiceImpl.payModeByStr(policyEsVo.getPayIntv());

                esVoMap = esData.getDataList().get(0).getRiskVoList().stream().collect(Collectors.toMap(info -> info.getRiskCode() + info.getInsuredName(),  Function.identity(), (k1, k2) -> k1));
            }
            Map<String, RiskEsVo> finalEsVoMap = esVoMap;


            DetailsOfRenewaVO contList = detailsOfRenewalVO.getContList();

            if (null != contList){
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                DetailsOfRenewaContlVO detailsOfRenewaContlVO = contList.getCont();

                String payToDate = detailsOfRenewaContlVO.getPaytoDate();
                LocalDate payDate = LocalDate.parse(payToDate, formatter);
                LocalDate endPayDate = payDate.plusDays(60);
                result.setAppntName(detailsOfRenewaContlVO.getAppnt().getName());
                Set<String> insuredNameList = detailsOfRenewaContlVO.getInsuredList().stream().map(DetailsOfRenewalInsuredVO::getName).collect(Collectors.toSet());
                result.setInsuredName(String.join("、", insuredNameList));
                result.setIsLock(detailsOfRenewaContlVO.getRnContLockFlag());
                result.setContNo(detailsOfRenewaContlVO.getContNo());
                result.setPayToDate(payToDate);
                result.setPayState(detailsOfRenewaContlVO.getPayState());
                result.setSumDuePayMoney(detailsOfRenewaContlVO.getSumDuePayMoney());
                result.setEndPayDate(endPayDate.toString());
                result.setAccountName(detailsOfRenewaContlVO.getAppnt().getName());
                result.setPayMode(detailsOfRenewaContlVO.getPayModeName());
                result.setBankAccNo(desensitizeBankCard(detailsOfRenewaContlVO.getBankAccNo()));
                if (StringUtil.isNotEmpty(contList.getDeductionTime())){

                    LocalDate date = LocalDate.parse(detailsOfRenewaContlVO.getPaytoDate(), formatter);
                    long l = Long.parseLong(contList.getDeductionTime());
                    LocalDate newDate = date.plusDays(l);
                    result.setDeductionTime(newDate.toString());
                }
                result.setInsuAccBala(contList.getWNLeaveMoney());
            }


            if ( null !=  detailsOfContRenewalVO){
                DetailsOfContNoRenewalVO detailsOfContNoRenewalVO = detailsOfContRenewalVO.getCont();
                DetailsOfContRenewalAppntVO detailsOfContRenewalAppntVO = detailsOfContNoRenewalVO.getAppnt();
                DetailsOfContRenewalPayVO pay = detailsOfContNoRenewalVO.getPay();


                result.setContState(detailsOfContNoRenewalVO.getState());
                result.setBankName(pay.getBankName());
                result.setCValiDate(detailsOfContNoRenewalVO.getCValiDate());
                result.setMainRiskName(detailsOfContNoRenewalVO.getRiskName());

                result.setAppntMobile(detailsOfContNoRenewalVO.getAppnt().getMobile());


                result.setAirPayParameterVO(AirPayParameterVO.builder()
                        .contNo(result.getContNo())
                        .amount(result.getSumDuePayMoney())
                        .certType(detailsOfContRenewalAppntVO.getIdType())
                        .certNo(detailsOfContRenewalAppntVO.getIdNo())
                        .payCount(request.getPayCount())
                        .name(result.getAppntName())
                        .nationalityCode(detailsOfContRenewalAppntVO.getNationality())
                        .payState(result.getPayState())
                        .payToDate(result.getPayToDate())
                        //.isAvailable(result.getIsAvailable())
                        .contState(result.getContState())
                        //.hangType(result.getHangType())
                        .payMode(result.getPayMode())
                        .build());
                //缴费明细字段补充
                ArrayList<RenewPolicyRisk> risks = new ArrayList<>();
                detailsOfContNoRenewalVO.getInsuredList().forEach(insured->{
                    insured.getRiskList().forEach(risk->{
                        risks.add(RenewPolicyRisk.builder()
                                .insuredName(insured.getInsuredName())
                                .insuredSex(insured.getInsuredSex())
                                .idType(insured.getIdType())
                                .idNo(insured.getIdNo())
                                .state(finalEsVoMap.get(risk.getRiskCode()+insured.getInsuredName()) == null ? risk.getState() : policyServiceImpl.esRiskCodeByStr(finalEsVoMap.get(risk.getRiskCode()+insured.getInsuredName()).getAppFlag()))
                                .prem(risk.getPrem())
                                .riskName(risk.getRiskName())
                                .build());
                    });
                });
                result.setRiskList(risks);

            }
            //意向度

            if ( !PaymentFrequency.MONTHLY.getName().equals(PaymentFrequency.fromName(payIntv).getName()) && null != result.getCValiDate() && StringUtils.isNotEmpty(result.getPayToDate())){
                InsurancePolicyIntention.IntentionType intentionTypeValue = getIntentionTypeValue(LocalDate.parse(result.getCValiDate()), result.getPayToDate());
                if (null != intentionTypeValue){
                    InsurancePolicyIntention insurancePolicyIntention = insurancePolicyIntentionMapper.selectOne(Wrappers.lambdaQuery(InsurancePolicyIntention.class)
                            .eq(InsurancePolicyIntention::getPolicyNo, request.getContNo())
                            .eq(InsurancePolicyIntention::getIntentionType,intentionTypeValue)
                    );
                    if ( null !=  insurancePolicyIntention){
                        result.setIntention(insurancePolicyIntention.getRenewalIntention().toString());
                    }
                }
            }else {
                result.setIntention(null);
            }
            //联系记录
            result.setRenewalContactRecordList(renewalContactRecordMapper.selectList(Wrappers.lambdaQuery(RenewalContactRecord.class)
                    .eq(RenewalContactRecord::getPolicyNo, request.getContNo())
                    .eq(RenewalContactRecord::getFrequency,payIntv)
                    .eq(RenewalContactRecord::getEmployeeCode,RequestContextHolder.getEmployeeCode())
                    .eq(RenewalContactRecord::getContStatus,MessageContentType.RENEW_TO_BE_CONTINUED.name())
            ));

            return result;
        }catch (Exception e){
            log.error("policyParticular countNo:{}:::error",request.getContNo(),e);
            throw  new ApiException(400,"哎呀，网络开了小差，再试一次吧！");
        }

    }

    @Override
    public PolicyReinstateResponse policyReinstate( PolicyRenewalRequest request) {
        if (request == null || StringUtils.isBlank(request.getContNo()) ||StringUtils.isBlank(request.getPayIntv())){
            throw  new BadRequestException("入参异常，请稍后再试！");
        }
        try {
            PolicyReinstateResponse response = new PolicyReinstateResponse();
            RenewalContRequest renewalContRequest = BeanCopier.copyObject(request, RenewalContRequest.class);
            DetailsOfContRenewalVO detailsOfContRenewalVO = renewApi.queryDetailsRenewal(renewalContRequest);

            //查询ES
            PolicyEsQueryRequestVo build = PolicyEsQueryRequestVo.builder()
                    .from(0)
                    .size(10)
                    .contNoList(Collections.singletonList(request.getContNo()))
                    .build();
            EsQueryResponseVo esData = policyServiceImpl.queryPolicy(build);

            Map<String, RiskEsVo> esVoMap = new HashMap<>();
            if ( null != esData && esData.getTotal()>0){
                PolicyEsVo policyEsVo = esData.getDataList().get(0);
                response.setPartnerOrgName(policyEsVo.getOrgName());
                response.setPolicyStatus(policyServiceImpl.payModeByStr(policyEsVo.getStatusForMyPolicys()));
                response.setChannelOrgName(policyEsVo.getChannelOrgName());
                response.setChannelAgentName(policyEsVo.getChannelAgentName());
                response.setPolicyChannel(policyEsVo.getPolicyChannel());
                if ("03".equals(policyEsVo.getSaleChnl())){
                    response.setChannelAgentName(policyEsVo.getBankAgentName());
                }
                List<RiskEsVo> riskVoList = policyEsVo.getRiskVoList();
                if (CollectionUtils.isNotEmpty(riskVoList)) {
                    List<RiskEsVo> collect = riskVoList.stream().filter(risk -> "M".equals(risk.getPrimaryOrSecondary())).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(collect)) {
                        RiskEsVo max = Collections.max(collect, Comparator.comparing(RiskEsVo::getPrem));
                        response.setPayEndYear(policyServiceImpl.makePaymentPeriod(max));
                        response.setPayIntv(policyServiceImpl.payModeByStr(max.getPayIntv()));
                    }
                }
                esVoMap = esData.getDataList().get(0).getRiskVoList().stream().collect(Collectors.toMap(info -> info.getRiskCode() + info.getInsuredName(),  Function.identity(), (k1, k2) -> k1));
            }
            Map<String, RiskEsVo> finalEsVoMap = esVoMap;

            if ( null !=  detailsOfContRenewalVO){
                DetailsOfContNoRenewalVO detailsOfContNoRenewalVO = detailsOfContRenewalVO.getCont();
                DetailsOfContRenewalInsuredVO detailsOfContRenewalInsuredVO = detailsOfContNoRenewalVO.getInsuredList().get(0);
                DetailsOfContRenewalPayVO pay = detailsOfContNoRenewalVO.getPay();
                DetailsOfContRenewalAppntVO detailsOfContRenewalAppntVO = detailsOfContNoRenewalVO.getAppnt();
                response.setAppntMobile(detailsOfContRenewalAppntVO.getMobile());
                response.setAppntName(detailsOfContRenewalAppntVO.getAppntName());
                response.setInsuredName(detailsOfContNoRenewalVO.getInsuredList().get(0).getInsuredName());
                response.setSumDuePayMoney(pay.getPrem());
                response.setContNo(detailsOfContNoRenewalVO.getContNo());
                response.setPayToDate(detailsOfContRenewalInsuredVO.getRiskList().get(0).getPaytoDate());
                response.setEndPayDate(detailsOfContRenewalInsuredVO.getRiskList().get(0).getPayEndDate());
                response.setPayEndYear(detailsOfContRenewalInsuredVO.getRiskList().get(0).getPayEndYear());
                response.setPayIntv(pay.getPayIntv());
                response.setContState(detailsOfContNoRenewalVO.getState());
                response.setCValiDate(detailsOfContNoRenewalVO.getCValiDate());
                response.setMainRiskName(detailsOfContNoRenewalVO.getRiskName());
                response.setRenewalContactRecordList(
                                renewalContactRecordMapper.selectList(Wrappers.lambdaQuery(RenewalContactRecord.class)
                                        .eq(RenewalContactRecord::getPolicyNo, request.getContNo())
                                        .eq(RenewalContactRecord::getFrequency,request.getPayIntv())
                                        .eq(RenewalContactRecord::getEmployeeCode,RequestContextHolder.getEmployeeCode())
                                        .eq(RenewalContactRecord::getContStatus,MessageContentType.RENEW_REINSTATE.name())
                                ));
                response.setAirPayParameterVO(AirPayParameterVO.builder()
                                .name(detailsOfContRenewalAppntVO.getAppntName())
                                .certNo(detailsOfContRenewalAppntVO.getIdNo())
                                .certType(detailsOfContRenewalAppntVO.getIdType())
                                .contNo(detailsOfContNoRenewalVO.getContNo())
                                .build());



                finalEsVoMap.values().stream()
                        .filter(v -> "失效".equals(detailsOfContNoRenewalVO.getState()))
                        .forEach(v -> v.setAppFlag("SX"));
                //缴费明细字段补充
                ArrayList<RenewPolicyRisk> risks = new ArrayList<>();
                detailsOfContNoRenewalVO.getInsuredList().forEach(insured->{
                    insured.getRiskList().forEach(risk->{
                        risks.add(RenewPolicyRisk.builder()
                                .insuredName(insured.getInsuredName())
                                .insuredSex(insured.getInsuredSex())
                                .idType(insured.getIdType())
                                .idNo(insured.getIdNo())
                                .state(finalEsVoMap.get(risk.getRiskCode()+insured.getInsuredName()) == null ? "失效" : policyServiceImpl.esRiskCodeByStr(finalEsVoMap.get(risk.getRiskCode()+insured.getInsuredName()).getAppFlag()))
                                .riskName(risk.getRiskName())
                                .prem(risk.getPrem())
                                .build());
                    });
                });
                response.setRiskList(risks);
            }


            return response;
        }catch (Exception e){
            log.error("policyReinstate:::error",e);
            throw  new ApiException(400,"哎呀，网络开了小差，再试一次吧！");
        }
    }

    @Override
    public void remindSuccessJob() {
        List<RenewalContactRecord> renewalContactRecordList = renewalContactRecordMapper.selectList(Wrappers.lambdaQuery(RenewalContactRecord.class)
                .isNotNull(RenewalContactRecord::getContactByAppointment)
                .eq(RenewalContactRecord::getStatus,Boolean.FALSE));
        if (CollectionUtils.isNotEmpty(renewalContactRecordList)){
            renewalContactRecordList.forEach(info->{
                LocalDateTime contactByAppointment = info.getContactByAppointment().withSecond(0).withNano(0);
                // 获取当前时间的LocalDateTime对象并加上一小时
                LocalDateTime nowPlusOneHour = LocalDateTime.now().plusHours(1).withSecond(0).withNano(0);
                if ( info.getStatus().equals(Boolean.FALSE) && contactByAppointment.isBefore(nowPlusOneHour)) {
                    MessageTemplateConfig messageTemplateConfig = templateConfig.getMessage().get(info.getContStatus());
                    AggrSendMessageRequest aggrSendMessageRequest = new AggrSendMessageRequest();
                    if (!info.getContStatus().equals(MessageContentType.RENEW_TRANS_RENEWS.name())){
                        String url = messageTemplateConfig.getUrl()
                                .replace("%K1%", info.getContStatus().equals(MessageContentType.RENEW_TO_BE_CONTINUED.name())?"TO_BE_CONTINUED":"PENDING_RECOVERY")
                                .replace("%K2%", info.getPolicyNo())
                                .replace("%K3%", StringUtils.isNotEmpty(info.getPayCount()) ? info.getPayCount() : "")
                                .replace("%K4%", StringUtils.isNotEmpty(info.getPayState()) ? info.getPayState() :"")
                                .replace("%K5%", info.getPayToDate() != null ? info.getPayToDate().format(DateTimeFormatter.ofPattern(DatePatterns.YYYY_MM_DD)) : "")
                                .replace("%K6%", StringUtils.isNotEmpty(info.getFrequency()) ? info.getFrequency() : "")
                                .replace("%K7%", info.getExpiryDate() != null  ? info.getExpiryDate().format(DateTimeFormatter.ofPattern(DatePatterns.YYYY_MM_DD)) : "")
                                .replace("%K8%", info.getExpirationDate() != null ? info.getExpirationDate().format(DateTimeFormatter.ofPattern(DatePatterns.YYYY_MM_DD)) : "")
                                .replace("%K9%", StringUtils.isNotEmpty(info.getFailureCause()) ? info.getFailureCause() : "")
                                .replace("%K10%",StringUtils.isNotEmpty(info.getPrtNo()) ? info.getPrtNo() : "");

                        if (info.getContStatus().equals(MessageContentType.RENEW_TO_BE_CONTINUED.name())){
                            //续期续保-待续交提醒
                            aggrSendMessageRequest.setMessageContentType(MessageContentType.RENEW_TO_BE_CONTINUED.name());
                        }else{
                            //续期续保-带复效提醒
                            aggrSendMessageRequest.setMessageContentType(MessageContentType.RENEW_REINSTATE.name());
                        }
                        aggrSendMessageRequest.setKey1(info.getAppntName());
                        aggrSendMessageRequest.setKey2(info.getPolicyNo());
                        aggrSendMessageRequest.setUrl(url);
                    }else {
                        //短险重投预约提醒
                        aggrSendMessageRequest.setMessageContentType(MessageContentType.RENEW_TRANS_RENEWS.name());
                        aggrSendMessageRequest.setKey1(info.getAppntName());
                        aggrSendMessageRequest.setKey2(info.getPolicyNo());
                    }

                    aggrSendMessageRequest.setEmployeeCode(info.getEmployeeCode());
                    List<MessageSendChannel> messageSendChannelList = new ArrayList<>();
                    messageSendChannelList.add(MessageSendChannel.WX);
                    messageSendChannelList.add(MessageSendChannel.LOCAL);
                    messageSendChannelList.add(MessageSendChannel.PUSH);
                    aggrSendMessageRequest.setMessageSendChannelList(messageSendChannelList);
                    messageService.aggrSendMsg(aggrSendMessageRequest);
                    renewalContactRecordMapper.updateByPrimaryKeySelective(RenewalContactRecord.builder().id(info.getId()).status(Boolean.TRUE).build());
                }
            });
        }
    }

    @Override
    public AirPayVO airPayReinstate(AirPayReinstateRequest request) {
        /* 官微的五证查询只支持 0-身份证、1-护照、B-回乡证、4-户口本、E-台胞证*/
        if (!(StringUtils.equals(ThreePointSixIdTypeEnum.ID_CARD_CERT_TYPE.getCode(), request.getCertType()) ||
                StringUtils.equals(ThreePointSixIdTypeEnum.PASSPORT_CERT_TYPE.getCode(), request.getCertType()) ||
                StringUtils.equals(ThreePointSixIdTypeEnum.HOUSE_CARD_CERT_TYPE.getCode(), request.getCertType()) ||
                StringUtils.equals(ThreePointSixIdTypeEnum.REENTRY_PERMIT_CERT_TYPE.getCode(), request.getCertType()) ||
                StringUtils.equals(ThreePointSixIdTypeEnum.TAIBAO_CARD_CERT_TYPE.getCode(), request.getCertType()))) {
            throw new BadRequestException("客户投保使用的证件类型不支持线上复效，请知悉！");
        }
        if (!(StringUtils.equals(ThreePointSixIdTypeEnum.ID_CARD_CERT_TYPE.getCode(), request.getCertType()))) {
            try {
                IdType idType1 = IdTypeMapping.getIdType(request.getCertType());
                EcRealNameInfoVO ecRealNameInfoVO = ecServiceApi.ecRealNameInfoByCNoAndCType(QueryRealNameRequest.builder()
                        .customerCertNo(request.getCertNo())
                        .customerCertType(idType1.name())
                        .build());
                if (ecRealNameInfoVO != null && 0 == ecRealNameInfoVO.getMiddleRealNameStatus()) {

                } else {
                    log.error("请求电商接口返回数据为失败:{}", "result");
                    throw new BadRequestException("客户未完成官微实名认证无法续交，请先指引客户完成官微实名认证后在试");
                }
            }catch (Exception e){
                throw new BadRequestException("客户未完成官微实名认证无法续交，请先指引客户完成官微实名认证后在试");
            }
        }
        EmployeeVO employeeInfo = employeeApi.getEmployeeByEmployeeCode(AgentOrgType.valueOf(RequestContextHolder.getOrgType()), RequestContextHolder.getEmployeeCode());
        String name = employeeInfo.getName();
       return AirPayVO.builder()
                .title("横琴人寿保单空中复效")
                .url(logReinstateUrl.replace("%", Base64.getEncoder().encodeToString((request.getPhone()+":"+request.getContNo()).getBytes(StandardCharsets.UTF_8)))+request.getPrtNo())
                .remark(maskData(request.getContNo()) + "，保单已失效，" + name+"邀您办理复效，延续保险保障！")
                .logoUrl(logUrl)
                .build();

    }

    @Override
    public void invalidPolicyMsgRemind(List<InvalidPolicyRemindMsgDTO> dtoList) {
        if (CollectionUtils.isEmpty(dtoList)) {
            return;
        }
        log.info("续期保单失效提醒,核心推送提醒保单数={}", dtoList.size());

        Map<String, InvalidPolicyRemindMsgDTO> remindMsgDTOMap = dtoList.stream().collect(Collectors.toMap(InvalidPolicyRemindMsgDTO::getContNo, Function.identity(), (k1, k2) -> k1));

        Set<String> contList = remindMsgDTOMap.keySet();
        EsQueryResponseVo esQueryResponseVo = policyWrapper.queryPolicyList(PolicyEsQueryRequestVo.builder()
                .contNoList(new ArrayList<>(contList))
                .size(contList.size())
                .build());

        if (CollectionUtils.isEmpty(esQueryResponseVo.getDataList())) {
            log.info("续期保单失效提醒,，失效保单MQ查询ES数据为空，流程结束");
            return;
        }

        //批量查询人员信息
        Set<String> codes = new HashSet<>();
        esQueryResponseVo.getDataList().forEach(dataList -> {
            if (StringUtil.isNotBlank(dataList.getAgentCode())) {
                codes.add(dataList.getAgentCode());
            }

            if (StringUtil.isNotBlank(dataList.getChannelAgentCode())) {
                codes.add(dataList.getChannelAgentCode());
            }
        });

        List<String> codeList = new ArrayList<>(codes);
        List<List<String>> agentIds = com.google.common.collect.Lists.partition(codeList, 100);
        List<AccountInfoDTO> userInfos = new ArrayList<>();

        agentIds.forEach(info -> {
            List<AccountInfoDTO> agentsBatch = umApiWrapper.queryAccountInfosByEmployeeCodeList(info);
            if (CollectionUtils.isNotEmpty(agentsBatch)) {
                userInfos.addAll(agentsBatch);
            }
        });

        Map<String, AccountInfoDTO> userInfoMap = new HashMap<>();
        userInfos.forEach(userInfo -> userInfoMap.put(userInfo.getAgentInfo().getEmployeeCode(), userInfo));

        //构造消息提醒数据
        List<MessageAddRequest> messageList = new ArrayList<>();
        List<WxMessageRequest> wxMessageRequest = new ArrayList<>();
        esQueryResponseVo.getDataList().forEach(data -> {

            Set<String> agentCodes = new HashSet<>(2);
            if (StringUtil.isNotBlank(data.getAgentCode())) {
                agentCodes.add(data.getAgentCode());
            }

            if (StringUtil.isNotBlank(data.getChannelAgentCode())) {
                agentCodes.add(data.getChannelAgentCode());
            }

            if (CollectionUtils.isEmpty(agentCodes)) {
                log.error("续期保单失效提醒,推送信息没有代理人工号，流程结束");
                return;
            }

            agentCodes.forEach(agentCode -> {
                if (null == userInfoMap.get(agentCode)) {
                    log.error("续期保单失效提醒,人员信息未查到,工号为：{}", agentCode);
                    return;
                }
                Long agentId = userInfoMap.get(agentCode).getAgentInfo().getAgentId();
                String employeeCode = userInfoMap.get(agentCode).getAgentInfo().getEmployeeCode();
                if (null == agentId || StringUtil.isEmpty(employeeCode)) {
                    log.error("续期保单失效提醒,UM人员信息不完整,工号为：{}", agentCode);
                    return;
                }
                if (StringUtil.isEmpty(data.getContNo())) {
                    log.info("续期保单失效提醒,getContNo为空,消息忽略,employeeCode:{}", employeeCode);
                    return;
                }

                String payIntv = "";
                try {
                    payIntv = URLEncoder.encode(PaymentFrequency.fromValue(data.getPayIntv()).getName(), "UTF-8");
                } catch (UnsupportedEncodingException e) {
                    log.error("urlEnder 失败", e);
                    return;
                }

                String expiryDate = "";
                String expirationDate = "";
                String failureCause = "";
                String prtNo = "";
                if (Objects.nonNull(remindMsgDTOMap.get(data.getContNo()))) {
                    //失效日期
                    expiryDate = remindMsgDTOMap.get(data.getContNo()).getInvalidDate().replace("年", "-").replace("月", "-").replace("日", "");

                    if (StringUtil.isNotBlank(expiryDate)) {
                        //复效截止日期
                        expirationDate = TimeUtil.format(TimeUtil.parseLocalDate(expiryDate, DatePatterns.YYYY_MM_DD).plusYears(2), DatePatterns.YYYY_MM_DD);
                    }
                    //失效原因
                    failureCause = remindMsgDTOMap.get(data.getContNo()).getInvalidReason();
                    //投保单号
                    prtNo = remindMsgDTOMap.get(data.getContNo()).getPrtNo();
                }
                //站内消息地址
                String url = "/renewal-policy-detail?type=PENDING_RECOVERY&contNo=" + data.getContNo() + "&payIntv=" + payIntv + "&expirationDate="
                        + expirationDate + "&expiryDate=" + expiryDate + "&failureCause=" + failureCause + "&prtNo=" + prtNo;
                AggrSendMessageRequest aggrSendMessageRequest = new AggrSendMessageRequest();
                aggrSendMessageRequest.setEmployeeCode(employeeCode);
                aggrSendMessageRequest.setMessageContentType(MessageContentType.ENDORSE_POLICY_INVALID.name());
                List<MessageSendChannel> messageSendChannelList = new ArrayList<>();
                messageSendChannelList.add(MessageSendChannel.WX);
                messageSendChannelList.add(MessageSendChannel.LOCAL);
                messageSendChannelList.add(MessageSendChannel.PUSH);
                aggrSendMessageRequest.setMessageSendChannelList(messageSendChannelList);
                aggrSendMessageRequest.setKey1(data.getAppntEsVo().getName());
                aggrSendMessageRequest.setKey2(data.getContNo());
                aggrSendMessageRequest.setUrl(url);
                messageService.aggrSendMsg(aggrSendMessageRequest);
            });
        });
    }

    @Override
    public void readRenew(String contNo, LocalDate payToDate, String employeeCode,String empFlag) {
        log.info("续期保单失效提醒,阅读消息,contNo={},payToDate={},employeeCode={}", contNo, payToDate, employeeCode);
        if (StringUtil.isEmpty(contNo) || null == payToDate || StringUtil.isEmpty(employeeCode)) {
            log.error("续期保单失效提醒,阅读消息,参数为空,contNo={},payToDate={},employeeCode={}", contNo, payToDate, employeeCode);
            return;
        }
        if ("2".equals(empFlag)) {
            final EmployeeVO employeeInfo = employeeApi.getEmployeeInfo(AgentOrgType.valueOf(RequestContextHolder.getOrgType()), employeeCode);
            employeeCode = employeeInfo.getJobNumber();
        }

        RenewalSucceed renewalSucceed = renewalSucceedMapper.selectOne(new LambdaQueryWrapper<RenewalSucceed>()
                .eq(RenewalSucceed::getSucceedMark, contNo)
                .eq(RenewalSucceed::getEmployeeCode, employeeCode)
                .last("limit 1"));

        if (null == renewalSucceed) {
            renewalSucceedMapper.insertSelective(RenewalSucceed.builder()
                    .succeedMark(contNo)
                    .payToDay(payToDate)
                    .employeeCode(employeeCode)
                    .contStatus(RenewPolicySearchType.TO_BE_CONTINUED.name())
                    .deleted(Boolean.FALSE)
                    .createTime(LocalDateTime.now())
                    .operatorId(employeeCode)
                    .build());
        }else{
            renewalSucceedMapper.deleteById(renewalSucceed.getId());
        }
    }

    @Override
    public List<TransRenewsSearchVO> transRenewsSearch() {
        List<TransRenewsSearchVO> result = new ArrayList<>();
        String employeeCode = RequestContextHolder.getEmployeeCode();
        if (StringUtils.isNotBlank(employeeCode)){
            TransRenewPolicyVO.TransRenewPolicy transRenewPolicy = renewApi.transRenewList(TransRenewListDTO.builder().agentCode(employeeCode).build());
            if (transRenewPolicy != null ){
                List<TransRenewPolicyVO.TransRenewPolicyContList> contList = transRenewPolicy.getContList();
                if (CollectionUtils.isNotEmpty(contList)){
                    List<RenewalSucceed> renewalSucceedList = renewalSucceedMapper.selectList(Wrappers.lambdaQuery(RenewalSucceed.class)
                            .eq(RenewalSucceed::getEmployeeCode, employeeCode)
                            .eq(RenewalSucceed::getContStatus, RenewPolicySearchType.TRANS_RENEWS.name())
                    );
                    Map<String, RenewalSucceed> succeedMap = new HashMap<>();
                    if (CollectionUtils.isNotEmpty(renewalSucceedList)){
                        succeedMap = StreamEx.of(renewalSucceedList)
                                .toMap(RenewalSucceed::getSucceedMark, Function.identity(), (k1, k2) -> k1);
                    }
                    for (TransRenewPolicyVO.TransRenewPolicyContList renewPolicyCont : contList) {
                        String policyChannel = BeanConverter.getPolicyChannel(renewPolicyCont.saleChnl, renewPolicyCont.saleType);
                        List<TransRenewPolicyVO.TransRenewPolicyInsured> insuredList = renewPolicyCont.getInsuredList();
                        List<TransRenewsSearchVO> dataList = new ArrayList<>();
                        Set<String> insuredLNameSet = new HashSet<>();
                        for (TransRenewPolicyVO.TransRenewPolicyInsured policyInsured : insuredList) {
                            if (CollectionUtils.isEmpty(policyInsured.getRiskList())){
                                //没有返回险种信息
                                log.info("短险重投列表保单号:{},没有返回险种信息被保人:{}",renewPolicyCont.contNo, policyInsured.getInsuredNo()+policyInsured.getInsuredName());
                                continue;
                            }
                            TransRenewPolicyVO.TransRenewPolicyRisk policyRisk = policyInsured.getRiskList().stream()
                                    .filter(x -> x.getIsMainRisk().equals("Y"))
                                    .max(Comparator.comparing(x -> Optional.ofNullable(x.getRiskPrem()).orElse(String.valueOf(Double.MIN_VALUE))))
                                    .get();
                            //保单号(外部)
                            String contNo = renewPolicyCont.getContNo();
                            //投保人姓名(外部)
                            String appntName = renewPolicyCont.getAppntName();
                            //投保人客户号(外部)
                            String appntNo = renewPolicyCont.getAppntNo();
                            //投保人手机号(外部)
                            String appntMobile = renewPolicyCont.getAppntMobile();
                            //被保人姓名(被保人层)
                            insuredLNameSet.add(policyInsured.getInsuredName());
                            //险种名称(被保人险种层)
                            String riskName = renewPolicyCont.getProductComName();
                            if (StringUtils.isBlank(renewPolicyCont.getProductComName())){
                                riskName = policyRisk.getRiskName();
                            }
                            //险种保费(被保人险种层)
                            String prem = policyRisk.getRiskPrem();
                            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.S");
                            LocalDate endDateLocal = LocalDate.parse(policyRisk.endDate, formatter);
                            LocalDate minReapplyDate = endDateLocal.minusDays(20);
                            LocalDate maxReapplyDate = endDateLocal.plusDays(60);
                            //可重投日期(被保人险种层)
                            String reapplyDate = minReapplyDate + " ~ " + maxReapplyDate;
                            dataList.add(TransRenewsSearchVO.builder()
                                    .policyNo(contNo)
                                    .url(renewPolicyCont.getUrl())
                                    .reapplyDate(reapplyDate)
                                    .endDate(endDateLocal.toString())
                                    .reapplyPremium(prem)
                                    .policyChannel(policyChannel)
                                    .appntName(appntName)
                                    .appntNo(appntNo)
                                    .appntMobile(appntMobile)
                                    .mainRiskName(riskName)
                                    .appFlag(renewPolicyCont.getAppFlag())
                                    .isRemind(Boolean.TRUE)
                                    .build());
                        }
                        if (CollectionUtils.isNotEmpty(dataList)){
                            //多被保人取最大保费的
                            TransRenewsSearchVO transRenewsSearchVO = dataList.stream()
                                    .max(Comparator.comparing(x -> Optional.ofNullable(x.getReapplyPremium()).orElse(String.valueOf(Double.MIN_VALUE))))
                                    .get();
                            //保费(外部)
                            transRenewsSearchVO.setReapplyPremium(renewPolicyCont.getPrem());
                            transRenewsSearchVO.setInsuredName(String.join("、", insuredLNameSet));
                            RenewalSucceed renewalSucceed = succeedMap.get(transRenewsSearchVO.policyNo);
                            if (null != renewalSucceed){
                                transRenewsSearchVO.setIsRemind(Boolean.TRUE);
                            }else {
                                transRenewsSearchVO.setIsRemind(Boolean.FALSE);
                            }
                            result.add(transRenewsSearchVO);
                        }
                    }
                }
            }
        }
        return result;
    }

    @Override
    public TransRenewsDetailsVO transRenewsDetails(String contNo) {
        SimplePolicyDetailVO simplePolicyDetailVO = policyServiceImpl.myDetails(contNo);
        TransRenewsDetailsVO response= new TransRenewsDetailsVO();
        BeanFiller.fill(response,simplePolicyDetailVO);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        SimplePolicyDetailVO.RiskInner riskInner = response.getRiskList().stream()
                .filter(x -> x.getMainRisk().equals("1"))
                .max(Comparator.comparing(x -> Optional.ofNullable(x.getTotalPremium()).orElse(String.valueOf(Double.MIN_VALUE))))
                .get();
        LocalDate endDateLocal = LocalDate.parse(riskInner.getEndDate(), formatter);
        LocalDate maxReapplyDate = endDateLocal.plusDays(60);
        response.setEndDate(endDateLocal);
        response.setReapplyDate(maxReapplyDate);
        List<RenewalContactRecord> renewalSucceedList = renewalContactRecordMapper.selectList(new LambdaQueryWrapper<RenewalContactRecord>()
                .eq(RenewalContactRecord::getPolicyNo, contNo)
                .eq(RenewalContactRecord::getEmployeeCode, RequestContextHolder.getEmployeeCode()));
        response.setRenewalContactRecordList(renewalSucceedList);
        return response;
    }

    @Override
    public PageInfo<RenewSuccessSearchVO> queryRenewSuccessPage(RenewSuccessRequest request) {

        if (request.getTyp().equals("RENEW")){
            return queryRenewSuccessLongList(request);
        }else {
            return queryRenewSuccessShortList(request);
        }
    }

    @Override
    public PageInfo<RenewSuccessSearchVO> queryRenewSuccessShortList(RenewSuccessRequest request) {
        List<TransRenewSuccessShortListVO.RenewSuccessShortListVO> shortListVOList = renewApi.transRenewSuccessShortList(TransRenewListDTO.builder()
                .agentCode(RequestContextHolder.getEmployeeCode())
                .appntName(request.getApplicant())
                .appFlag(request.getAppFlag())
                .startCvaliDate(request.getStartDate() != null ? request.getStartDate().format(DateTimeFormatter.ofPattern(DatePatterns.YYYY_MM_DD)) : "")
                .endCvaliDate(request.getEndDate() != null ? request.getEndDate().format(DateTimeFormatter.ofPattern(DatePatterns.YYYY_MM_DD)) : "")
                .contNo(request.getContNo())
                .build());
        log.info("queryRenewSuccessShortList返回:{}", JsonUtil.toJSON(shortListVOList));
        List<RenewSuccessSearchVO> result = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(shortListVOList)){
            for (TransRenewSuccessShortListVO.RenewSuccessShortListVO renewPolicyCont : shortListVOList) {
                String policyChannel = BeanConverter.getPolicyChannel(renewPolicyCont.getSaleChnl(), renewPolicyCont.getSellType());
                /*if (StringUtils.isNotBlank(request.getCustomerSource()) && !policyChannel.equals(request.getCustomerSource())){
                    continue;
                }*/
                List<TransRenewSuccessShortListVO.SuccessShortListInsured> insuredList = renewPolicyCont.getInsuredList();
                if (CollectionUtils.isEmpty(insuredList)){
                    continue;
                }
                if (renewPolicyCont.getAppFlag() == null || !renewPolicyCont.getAppFlag().equals("1")){
                    //过滤无效的
                    continue;
                }
                List<RenewSuccessSearchVO> dataList = new ArrayList<>();
                Set<String> insuredLNameSet = new HashSet<>();
                for (TransRenewSuccessShortListVO.SuccessShortListInsured policyInsured : insuredList) {
                    if (CollectionUtils.isEmpty(policyInsured.getRiskList())){
                        //没有返回险种信息
                        log.info("短险已重投列表保单号:{},没有返回险种信息被保人:{}",renewPolicyCont.getContNo(), policyInsured.getInsuredNo()+policyInsured.getInsuredName());
                        continue;
                    }
                    TransRenewSuccessShortListVO.SuccessShortListRisk shortListRisk = policyInsured.getRiskList().stream()
                            .filter(x -> x.getIsMainRisk().equals("Y"))
                            .max(Comparator.comparing(x -> Optional.ofNullable(x.getRiskPrem()).orElse(Double.valueOf(String.valueOf(Double.MIN_VALUE)))))
                            .get();
                    //保单号(外部)
                    String contNo = renewPolicyCont.getRcontNo();
                    //投保人姓名(外部)
                    String appntName = renewPolicyCont.getAppntName();
                    //投保人客户号(外部)
                    String appntNo = renewPolicyCont.getAppntNo();
                    //被保人姓名(被保人层)
                    insuredLNameSet.add(policyInsured.getInsuredName());
                    //险种名称(被保人险种层)
                    String riskName = renewPolicyCont.getProductComName();
                    if (StringUtils.isBlank(renewPolicyCont.getProductComName())){
                        riskName = shortListRisk.getRiskName();
                    }
                    //险种保费(被保人险种层)
                    Double prem = shortListRisk.getRiskPrem();
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.S");
                    LocalDate endDateLocal = LocalDate.parse(shortListRisk.getEndDate(), formatter);
                    LocalDate successDate = LocalDate.parse(renewPolicyCont.getRenewalSuccessDate(), formatter);
                    LocalDate cvaliDate = LocalDate.parse(renewPolicyCont.getCvaliDate(), formatter);
                    dataList.add(RenewSuccessSearchVO.builder()
                            .mainRiskName(riskName)
                            .appntName(appntName)
                            .appntNo(appntNo)
                            .contNo(contNo)
                            .oldContNo(renewPolicyCont.getTcontNo())
                            .transRenewsSuccessDate(successDate.toString())
                            .premium(String.valueOf(prem))
                            .payModel(renewPolicyCont.getNewPayMode())
                            .endDate(endDateLocal)
                            .cvaliDate(cvaliDate)
                            .build());
                }
                if (CollectionUtils.isNotEmpty(dataList)){
                    //多被保人取最大保费的
                    RenewSuccessSearchVO renewSuccessSearchVO = dataList.stream()
                            .max(Comparator.comparing(x -> Optional.ofNullable(x.getPaymentPeriod()).orElse(String.valueOf(Double.MIN_VALUE))))
                            .get();
                    //保费(外部)
                    renewSuccessSearchVO.setPremium(renewPolicyCont.getPrem());
                    renewSuccessSearchVO.setInsuredName(String.join("、", insuredLNameSet));
                    renewSuccessSearchVO.setPolicyChannel(policyChannel);
                    result.add(renewSuccessSearchVO);
                }
            }
        }
        PageInfo<RenewSuccessSearchVO> pageInfo = new PageInfo<>();
        pageInfo.setRecords(result);
        return pageInfo;
    }


    @Override
    public PageInfo<RenewSuccessSearchVO> queryRenewSuccessLongList(RenewSuccessRequest request) {
        String employeeCode = RequestContextHolder.getEmployeeCode();
        if ("2".equals(request.getEmpFlag())){
            final EmployeeVO employeeInfo = employeeApi.getEmployeeInfo(AgentOrgType.valueOf(RequestContextHolder.getOrgType()), employeeCode);
            employeeCode = employeeInfo.getJobNumber();
        }
        RenewSuccessLongListVO successLongVO = renewApi.renewSuccessLongList(TransRenewListDTO.builder()
                .agentCode(employeeCode)
                .appntName(request.getApplicant())
                .startDate(request.getStartDate() != null ? request.getStartDate().format(DateTimeFormatter.ofPattern(DatePatterns.YYYY_MM_DD)) : "")
                .endDate(request.getEndDate() != null ? request.getEndDate().format(DateTimeFormatter.ofPattern(DatePatterns.YYYY_MM_DD)) : "")
                .contNo(request.getContNo())
                .channel(request.getCustomerSource())
                .pageNum(request.getCurrent())
                .pageSize(request.getSize())
                .build());
        List<RenewSuccessSearchVO> result = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(successLongVO.getData())){
            for (RenewSuccessLongListVO.RenewSuccessLongVO renewSuccessLongVO : successLongVO.getData()) {
                String policyChannel = BeanConverter.getPolicyChannel(renewSuccessLongVO.getSalechnl(), renewSuccessLongVO.getSellType());
                result.add(RenewSuccessSearchVO.builder()
                                .policyChannel(policyChannel)
                                .mainRiskName(renewSuccessLongVO.getRiskName())
                                .appntName(renewSuccessLongVO.getAppntName())
                                .contNo(renewSuccessLongVO.getContNo())
                                .payToDate(renewSuccessLongVO.getPayDate())
                                .transferDate(renewSuccessLongVO.getConfDate())
                                .paymentPeriod(renewSuccessLongVO.getPayIntv())
                                .insuredName(renewSuccessLongVO.getInsuredName())
                                .premium(renewSuccessLongVO.getPrem())
                        .build());
            }
        }
        PageInfo<RenewSuccessSearchVO> pageInfo = new PageInfo<>();
        pageInfo.setRecords(result);
        pageInfo.setTotal(CollectionUtils.isNotEmpty(successLongVO.getData()) ? successLongVO.getTotalSize() : 0L);
        pageInfo.setCurrent(request.getCurrent());
        pageInfo.setSize(request.getSize());
        return pageInfo;
    }

    @Override
    public RenewsSuccessShortDetailVO transRenewsSuccessShortDetails(String contNo,String oldContNo) {
        List<TransRenewSuccessShortInfo.SuccessShortInfoDetail> successShortInfoDetails = renewApi.transRenewSuccessShortInfo(TransRenewSuccessShortInfoRequest.builder()
                .tcontNo(oldContNo)
                .rcontNo(contNo)
                .build());
        RenewsSuccessShortDetailVO response = new RenewsSuccessShortDetailVO();
        if (CollectionUtils.isNotEmpty(successShortInfoDetails)){
            TransRenewSuccessShortInfo.SuccessShortInfoDetail successShortOneDetail = successShortInfoDetails.get(0);
            TransRenewSuccessShortInfo.SuccessShortInfoDetail successShortTwoDetail = successShortInfoDetails.get(1);
            //新单
            makeDetailNewOrOld(contNo, response,successShortOneDetail);
            makeDetailNewOrOld(contNo, response,successShortTwoDetail);

        }

        return response;
    }

    @Override
    public RenewsSuccessShortDetailVO transRenewsSuccessLongDetails(String contNo) {
        SuccessLongInfoDetail successLongInfoDetail = renewApi.renewSuccessLongInfo(TransRenewSuccessShortInfoRequest.builder()
                .contNo(contNo)
                .build());
        RenewsSuccessShortDetailVO response = new RenewsSuccessShortDetailVO();
        if (successLongInfoDetail != null){
            BeanFiller.fill(response,successLongInfoDetail);
            response.setMainRiskName(successLongInfoDetail.getRiskName());
            response.setApplicantName(successLongInfoDetail.getAppntName());
            response.setPayDate(successLongInfoDetail.getConfDate());
            response.setSumDuePayMoney(successLongInfoDetail.getPayMoney());
            response.setPayToDate(successLongInfoDetail.getPayDate());
            String accNo = "";
            if (StringUtils.isNotBlank(successLongInfoDetail.getBankAccNo())){
                response.setBankAccNo(desensitizeBankCard(successLongInfoDetail.getBankAccNo()));
                accNo="("+successLongInfoDetail.getBankAccNo().substring(successLongInfoDetail.getBankAccNo().length()-4)+")";
            }
            response.setPayAccNo(StringUtils.isNotEmpty(successLongInfoDetail.getBankName()) ? successLongInfoDetail.getBankName()+accNo : "");
            response.setBankAccName(successLongInfoDetail.getAccName());
            response.setPayMode(successLongInfoDetail.getPayMode());
            response.setPayEndYear(successLongInfoDetail.getPayEndYear() + getPayEndYearFlagDescription(successLongInfoDetail.getPayEndYearFlag()));
            response.setPayIntv(getPayIntervalDescription(successLongInfoDetail.getPayIntv()));
            response.setSalesOrg(successLongInfoDetail.getManageCom());
            response.setSalesPerson(successLongInfoDetail.getAgentName());
            //因为核心列表固定查 appFlag是1 详细请返回的都是承保
            response.setPolicyStatus("保障中");
            List<RenewsSuccessShortDetailVO.RenewsSuccessRiskInner> riskInnerList =new ArrayList<>();
            Set<String> insuredNameList =new HashSet<>();
            for (SuccessLongInfoDetail.XqRiskDetailPojo xqRiskDetailPojo : successLongInfoDetail.getXqRiskDetailPojos()) {
                insuredNameList.add(xqRiskDetailPojo.getInsuredName());
                riskInnerList.add(RenewsSuccessShortDetailVO.RenewsSuccessRiskInner.builder()
                                .riskName(xqRiskDetailPojo.getRiskName())
                                .insuredName(xqRiskDetailPojo.getInsuredName())
                                .policyStatus(xqRiskDetailPojo.getPolState())
                                .totalPremium(xqRiskDetailPojo.getPrem())
                        .build());
            }
            response.setInsuredName(String.join("、", insuredNameList));
            List<RenewsSuccessShortDetailVO.XqInfoPojo> xqInfoPojoList = BeanCopier.copyList(successLongInfoDetail.getXqInfoPojos(), RenewsSuccessShortDetailVO.XqInfoPojo.class);

            response.setRiskList(riskInnerList);
            response.setXqInfoPojos(xqInfoPojoList);
        }
        return response;

    }
    public  String getPayIntervalDescription(String payintv) {
        switch (payintv) {
            case "0":
                return "趸交";
            case "1":
                return "月交";
            case "12":
                return "年交";
            default:
                return "未知";
        }
    }
    public  String getPayEndYearFlagDescription(String payendyearflag) {
        switch (payendyearflag) {
            case "Y":
                return "年";
            case "M":
                return "月";
            case "D":
                return "天";
            default:
                return "未知";
        }
    }

    private void makeDetailNewOrOld(String contNo, RenewsSuccessShortDetailVO response,TransRenewSuccessShortInfo.SuccessShortInfoDetail successShortDetail) {
        log.info("已重投保单详情入参:contNo{},successShortDetail:{}",contNo,JsonUtil.toJSON(successShortDetail));
        String detailContNo = successShortDetail.getContNo();
        if (contNo.equals(detailContNo)){

            response.setSalesOrg(successShortDetail.getManageCom());
            response.setSalesPerson(successShortDetail.getAgentName());
            response.setAccountName(successShortDetail.getNewAccName());
            String accNo = "";
            if (StringUtils.isNotBlank(successShortDetail.getNewBankAccNo())){
                response.setBankAccNo(desensitizeBankCard(successShortDetail.getNewBankAccNo()));
                accNo="("+successShortDetail.getNewBankAccNo().substring(successShortDetail.getNewBankAccNo().length()-4)+")";
            }
            response.setPayAccNo(successShortDetail.getNewBankCodeName()+accNo);
            response.setPayMode(getPaymentMethodDescription(successShortDetail.getNewPayMode()));
            response.setBankAccName(successShortDetail.getNewBankCode());
            response.setPolicyStatus(successShortDetail.getAppFlag().equals("1") ? "保障中" : "已终止" );


            String riskName = successShortDetail.getProductComName();
            List<TransRenewSuccessShortInfo.SuccessShortInfoInsured> insuredList = successShortDetail.getInsuredList();
            Set<String> insuredLNameSet = new HashSet<>();
            List<RenewsSuccessShortDetailVO.RenewsSuccessRiskInner> riskList = new ArrayList<>();
            RenewsSuccessShortDetailVO.RenewOldDetail  renewNewDetail= new RenewsSuccessShortDetailVO.RenewOldDetail();
            for (TransRenewSuccessShortInfo.SuccessShortInfoInsured successShortInfoInsured : insuredList) {
                TransRenewSuccessShortInfo.SuccessShortInfoRisk infoMaxRisk = successShortInfoInsured.getRiskList().stream()
                        .filter(x -> x.getIsMainRisk().equals("Y"))
                        .max(Comparator.comparing(x -> Optional.ofNullable(x.getRiskPrem()).orElse(Double.valueOf(String.valueOf(Double.MIN_VALUE)))))
                        .get();
                if (StringUtils.isBlank(successShortDetail.getProductComName())){
                    riskName = infoMaxRisk.getRiskName();
                }
                for (TransRenewSuccessShortInfo.SuccessShortInfoRisk successShortInfoRisk : successShortInfoInsured.getRiskList()) {
                    RenewsSuccessShortDetailVO.RenewsSuccessRiskInner risk = new RenewsSuccessShortDetailVO.RenewsSuccessRiskInner();
                    risk.setInsuredName(successShortInfoInsured.getInsuredName());
                    risk.setRiskName(successShortInfoRisk.getRiskName());
                    risk.setTotalPremium(String.valueOf(successShortInfoRisk.getRiskPrem()));
                    risk.setPolicyStatus(successShortInfoRisk.getAppFlag().equals("1") ? "承保" : "终止" );
                    riskList.add(risk);
                }
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.S");
                LocalDate effective = LocalDate.parse(infoMaxRisk.getRiskCvaliDate(), formatter);
                LocalDate expiration = LocalDate.parse(infoMaxRisk.getEndDate(), formatter);
                renewNewDetail.setPolicyNumber(detailContNo);
                renewNewDetail.setEffective(effective.toString());
                renewNewDetail.setExpiration(expiration.toString());
                renewNewDetail.setBasicPremium(successShortDetail.getPrem());
                renewNewDetail.setAdditionalFee(infoMaxRisk.getAddPrem());
                renewNewDetail.setSpecialAgreement(infoMaxRisk.getSpecContent());
                renewNewDetail.setInsuranceName(riskName);
                insuredLNameSet.add(successShortInfoInsured.getInsuredName());
                response.setMainRiskName(riskName);
                response.setContNo(detailContNo);
                response.setApplicantName(successShortDetail.getAppntName());
                response.setPayIntv(PaymentFrequency.getNameByValue(infoMaxRisk.getPayIntv()));
            }
            renewNewDetail.setInsuredPerson(String.join("、", insuredLNameSet));
            response.setNewRenewDetail(renewNewDetail);
            response.setRiskList(riskList);
        }else {

            //旧单
            List<TransRenewSuccessShortInfo.SuccessShortInfoInsured> insuredList = successShortDetail.getInsuredList();
            Set<String> insuredLNameSet = new HashSet<>();
            RenewsSuccessShortDetailVO.RenewOldDetail  renewOldDetail= new RenewsSuccessShortDetailVO.RenewOldDetail();
            for (TransRenewSuccessShortInfo.SuccessShortInfoInsured successShortInfoInsured : insuredList) {
                List<String> riskNameList = new ArrayList<>();
                TransRenewSuccessShortInfo.SuccessShortInfoRisk infoMaxRisk = null;
                double maxPrem = Double.MIN_VALUE; // 用于记录最大保费

                /*if (StringUtils.isNotBlank(successShortDetail.getProductComName())){
                    riskNameList.add(successShortDetail.getProductComName());
                }*/
                Double addPrem = 0.0;
                for (TransRenewSuccessShortInfo.SuccessShortInfoRisk risk : successShortInfoInsured.getRiskList()) {
                    riskNameList.add(risk.getRiskName());
                    // 过滤主险
                    if ("Y".equals(risk.getIsMainRisk())) {
                        // 获取保费，处理空值
                        double currentPrem = Optional.ofNullable(risk.getRiskPrem()).orElse(Double.MIN_VALUE);
                        // 更新最大保费及对应险种
                        if (currentPrem > maxPrem) {
                            maxPrem = currentPrem;
                            infoMaxRisk = risk;
                        }
                    }
                    if (risk.getAddPrem() != null){
                        addPrem += risk.getAddPrem();
                    }
                }


                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.S");
                LocalDate effective = LocalDate.parse(infoMaxRisk.getRiskCvaliDate(), formatter);
                LocalDate expiration = LocalDate.parse(infoMaxRisk.getEndDate(), formatter);
                renewOldDetail.setPolicyNumber(detailContNo);
                renewOldDetail.setEffective(effective.toString());
                renewOldDetail.setExpiration(expiration.toString());
                renewOldDetail.setBasicPremium(successShortDetail.getPrem());
                renewOldDetail.setAdditionalFee(addPrem);
                renewOldDetail.setSpecialAgreement(infoMaxRisk.getSpecContent());
                renewOldDetail.setInsuranceName(String.join(",",riskNameList));
                insuredLNameSet.add(successShortInfoInsured.getInsuredName());
            }
            renewOldDetail.setInsuredPerson(String.join("、", insuredLNameSet));
            response.setOldRenewDetail(renewOldDetail);
        }
    }

    /**
     * 核心保单状态转换
     */
    public String policyStatusByStr(String code){
        String policyStatus = "";
        if (StringUtils.isNotBlank(code)){
            switch (code){
                case "1":
                    policyStatus = "保障中";
                    break;
                case "4":
                    policyStatus = "已失效";
                    break;
                default:
                    policyStatus = "未知状态";
                    break;
            }
        }
        return policyStatus;
    }
    /**
     * 数据脱敏
     */
    private String maskData(String data) {
        if (StringUtils.isNotBlank(data) || data.length() > 8) {
            int length = data.length();
            return data.substring(0, 6) + "*****" + data.substring(length - 2, length);
        }
        return "";
    }

    /**
     * 核心缴费方式转换
     */
    private String getPaymentMethodDescription(String paymentMethod) {
        if(StringUtils.isNotBlank(paymentMethod)){
            switch (paymentMethod) {
                case "R":
                    return "实时代收";
                case "2":
                    return "人工收取";
                case "3":
                    return "其它";
                case "4":
                    return "客户自交";
                case "0":
                    return "银行转账";
                case "1":
                    return "投保人自缴";
                case "F":
                    return "支付宝";
                case "W":
                    return "微信支付";
                case "M":
                    return "蚂蚁支付宝";
                case "Z":
                    return "京东支付";
                case "P":
                    return "平安付";
                case "T":
                    return "定期结算";
                default:
                    return null;
            }
        }
        return null;
    }



    /**
     * 计算意向度
     *    计算（应交日-生效日）/365，结果保留一位小数
     */
    public static InsurancePolicyIntention.IntentionType getIntentionTypeValue(LocalDate dueDate, String effectiveDateStr) {
        LocalDate effectiveDate = LocalDate.parse(effectiveDateStr);
        long days = ChronoUnit.DAYS.between(dueDate, effectiveDate);
        BigDecimal result = new BigDecimal(days).divide(new BigDecimal("365"), 1, RoundingMode.HALF_UP);
        log.info("计算意向度:{}",result);
        if (result.compareTo(new BigDecimal("1.0")) >= 0 && result.compareTo(new BigDecimal("2.0")) < 0) {
            return InsurancePolicyIntention.IntentionType.MONTH_13;
        } else if (result.compareTo(new BigDecimal("2.0")) >= 0 && result.compareTo(new BigDecimal("3.0")) < 0) {
            return InsurancePolicyIntention.IntentionType.MONTH_25;
        } else if (result.compareTo(new BigDecimal("3.0")) >= 0 && result.compareTo(new BigDecimal("4.0")) < 0) {
            return InsurancePolicyIntention.IntentionType.MONTH_37;
        }
        return null;
    }

    /**
     * 银行卡脱敏
     */
    public static String desensitizeBankCard(String bankCard) {

        if (bankCard == null || bankCard.length() < 10) {
            // 判断银行卡号是否有效（有效的卡号长度必须大于等于 10）
            return "";
        }
        int len = bankCard.length();
        StringBuilder stringBuilder = new StringBuilder(bankCard.substring(0, 4));
        for (int i = 4; i < len - 4; i++) {
            stringBuilder.append("*");
        }
        stringBuilder.append(bankCard.substring(len - 4));
        return stringBuilder.toString();
    }


}
