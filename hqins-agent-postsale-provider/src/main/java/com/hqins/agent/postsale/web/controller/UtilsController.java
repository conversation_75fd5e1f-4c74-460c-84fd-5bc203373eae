package com.hqins.agent.postsale.web.controller;

import com.hqins.agent.postsale.service.UtilsService;
import com.hqins.common.base.ApiResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Slf4j
@RestController
@Api(tags = "工具类控制器")
@RequestMapping("/utils")
public class UtilsController {
    @Resource
    private UtilsService utilsService;

    @GetMapping("/workDays")
    @ResponseStatus(HttpStatus.OK)
    @ApiOperation("查询工作日，除国家法定节假日")
    public ApiResult<String> workDays(@ApiParam("年份:2025") @RequestParam(value = "year") String year,
                                        @ApiParam("月份:1") @RequestParam(value = "month") String month){
        return  ApiResult.ok(utilsService.workDays(year,month));
    }

    @GetMapping("/yearWorkDays")
    @ResponseStatus(HttpStatus.OK)
    @ApiOperation("查询工作日，除国家法定节假日")
    public ApiResult<String> yearWorkDays(@ApiParam("年份:2025") @RequestParam(value = "year") String year){
        return  ApiResult.ok(utilsService.yearWorkDays(year));
    }

    @GetMapping("/syncHolidayCalendar")
    @ResponseStatus(HttpStatus.OK)
    @ApiOperation("同步国家法定节假日")
    public ApiResult<String> syncHolidayCalendar(@ApiParam("年份:2025") @RequestParam(value = "year") String year){
        utilsService.syncHolidayCalendar(year);
        return  ApiResult.ok("SUCCESS");
    }


}
