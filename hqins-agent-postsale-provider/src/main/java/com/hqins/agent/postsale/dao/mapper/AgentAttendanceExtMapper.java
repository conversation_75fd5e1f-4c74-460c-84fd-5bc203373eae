package com.hqins.agent.postsale.dao.mapper;

import com.hqins.agent.postsale.dao.entity.AgentAttendance;
import com.hqins.agent.postsale.dao.entity.AttendanceActivity;
import com.hqins.agent.postsale.dao.entity.AttendanceCustomer;
import com.hqins.agent.postsale.dto.request.DataStatisticsRequest;
import com.hqins.agent.postsale.dto.response.MonthPunchIn;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 人员出勤表数据访问接口
 * <AUTHOR>
 * @since 2023-03-02
 */
@Mapper
public interface AgentAttendanceExtMapper extends BatchBaseMapper<AgentAttendance> {

    /**
     * 查询当月打卡日历数据总计
     */
    Integer selectCalendarByMonth(@Param("monchStartTime") LocalDateTime monchStartTime, @Param("monchEndTime") LocalDateTime monchEndTime,
                                  @Param("employeeCode") String employeeCode);

    Integer selectCalendarByDay(@Param("month") int month, @Param("years") int years, @Param("employeeCode") String employeeCode);

    List<MonthPunchIn> selectCalendarDataByMonth(@Param("request")DataStatisticsRequest request, @Param("employeeCode") String employeeCode,
                                                 @Param("monchStartTime") LocalDateTime monchStartTime,@Param("monchEndTime") LocalDateTime monchEndTime);

    List<MonthPunchIn> selectCalendarDataByMonthBatch(@Param("request")DataStatisticsRequest request, @Param("employeeCode") List<String> employeeCode,
                                                 @Param("monthStartTime") LocalDateTime monthStartTime,@Param("monthEndTime") LocalDateTime monthEndTime);

    List<String> selectCountByReissueACard(@Param("employeeCode") String employeeCode, @Param("years") int years,@Param("month") int month);

    List<AgentAttendance> selectDaySingData(@Param("date") LocalDateTime date, @Param("employeeCode") String employeeCode);
    /**
     * 查询银保个人当天打卡数据
     */
    List<AgentAttendance> selectYBSingData(@Param("employeeCode") String employeeCode, @Param("mStartTime") String mStartTime,
                                           @Param("mEndTime") String mEndTime, @Param("aStartTime") String aStartTime, @Param("aEndTime") String aEndTime);

    /**
     * 查询打卡日志活动数据
     */
    List<AttendanceCustomer> selectAttCust(@Param("id") Long id);

    /**
     * 查询打卡日志客户数据
     */
    List<AttendanceActivity> selectAttAct(@Param("id") Long id);


}

