package com.hqins.agent.postsale.dao.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 国家法定节假日数据表
 */
@TableName("holiday_data")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class HolidayData implements Serializable {

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 年份:2025
     */
    private String holidayYear;

    /**
     * 月份:1
     */
    private String holidayMonth;

    /**
     * 日:1
     */
    private String holidayDay;

    /**
     * 假期名字
     */
    private String holidayName;

    /**
     * 假期类型,transfer_workday:补班,public_holiday:假期
     */
    private String holidayType;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
}
