package com.hqins.agent.postsale.service.impl;

import cn.hutool.core.codec.Base64;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.alicp.jetcache.Cache;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.CreateCache;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hqins.agent.org.model.api.DataAccessApi;
import com.hqins.agent.org.model.api.EmployeeApi;
import com.hqins.agent.org.model.api.EmployeeOrgApi;
import com.hqins.agent.org.model.api.OrgApi;
import com.hqins.agent.org.model.enums.EmployeeStatus;
import com.hqins.agent.org.model.vo.EmployeeOrgVO;
import com.hqins.agent.org.model.vo.EmployeeVO;
import com.hqins.agent.org.model.vo.MyDataAccessVO;
import com.hqins.agent.postsale.api.UmApiWrapper;
import com.hqins.agent.postsale.config.AttendanceConfig;
import com.hqins.agent.postsale.config.MessageTemplateConfig;
import com.hqins.agent.postsale.config.TemplateConfig;
import com.hqins.agent.postsale.dao.entity.AttendanceActivity;
import com.hqins.agent.postsale.dao.entity.AttendanceCustomer;
import com.hqins.agent.postsale.dao.entity.*;
import com.hqins.agent.postsale.dao.mapper.*;
import com.hqins.agent.postsale.dto.enums.*;
import com.hqins.agent.postsale.dto.request.*;
import com.hqins.agent.postsale.dto.response.*;
import com.hqins.agent.postsale.model.enums.*;
import com.hqins.agent.postsale.model.request.*;
import com.hqins.agent.postsale.model.vo.*;
import com.hqins.agent.postsale.service.*;
import com.hqins.agent.postsale.utils.DateUtils;
import com.hqins.agent.postsale.utils.PrivacyUtil;
import com.hqins.common.base.constants.DatePatterns;
import com.hqins.common.base.enums.AgentOrgType;
import com.hqins.common.base.errors.ApiException;
import com.hqins.common.base.page.PageInfo;
import com.hqins.common.base.utils.AssertUtil;
import com.hqins.common.helper.BeanCopier;
import com.hqins.common.utils.HttpUtils;
import com.hqins.common.utils.JsonUtil;
import com.hqins.common.utils.PageUtil;
import com.hqins.common.utils.StringUtil;
import com.hqins.common.web.RequestContextHolder;
import com.hqins.file.service.api.FileApi;
import com.hqins.file.service.model.request.FileBase64Request;
import com.hqins.file.service.model.vo.FileGetUrlsVO;
import com.hqins.um.model.dto.AgentDTO;
import lombok.extern.slf4j.Slf4j;
import one.util.streamex.StreamEx;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.BufferedInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.net.MalformedURLException;
import java.net.URL;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR> Li
 * rDate 2023/3/1 16:35
 */
@Service
@Slf4j
@RefreshScope
public class AttendanceSettingServiceImpl extends ServiceImpl<AttendanceAddressMapper, AttendanceAddress> implements AttendanceSettingService {

    private final AttendanceAddressMapper attendanceAddressMapper;
    private final AgentAttendanceMapper agentAttendanceMapper;
    private final LeaveApplicationsMapper leaveApplicationsMapper;
    private final OrgApi orgApi;
    private final UmApiWrapper umApiWrapper;
    private final FileApi fileApi;
    private final TemplateConfig templateConfig;
    private final DataAccessApi dataAccessApi;
    private final MessageService messageService;
    private final AttendanceCustomerMapper attendanceCustomerMapper;
    private final AttendanceActivityMapper attendanceActivityMapper;
    private final AgentImageMapper agentImageMapper;
    private final EmployeeApi employeeApi;
    private final AttendanceService attendanceService;
    private final EmployeeOrgApi employeeOrgApi;
    private final AgentAttendanceTimeMapper agentAttendanceTimeMapper;
    private final AgentAttendanceExtMapper agentAttendanceExtMapper;
    private final RepairLeaveApprovalMapper repairLeaveApprovalMapper;
    private final AgentApplyConfigMapper agentApplyConfigMapper;
    private final LeaveRevokeMapper leaveRevokeMapper;
    private final AttendanceLeaveCheckMapper attendanceLeaveCheckMapper;
    private final AgentLeaveRemainingMapper agentLeaveRemainingMapper;
    private final LeaveCheckConfigMapper leaveCheckConfigMapper;
    private final LeaveApplicationsServiceImpl leaveApplicationsService;
    private final UtilsService utilsService;
    private final AttendanceConfig attendanceSignInImageConfig;
    private final AttendanceConfig attendanceConfig;
    private final AttendanceYinBaoService attendanceYinBaoService;

    @Value("${environment-url}")
    private String environmentUrl;
    @Value("${environment}")
    private String environment;
    @Value("${attendanceAddressScope}")
    private Integer attendanceAddressScope ;
    @CreateCache(name = "SIGN_IN:", expire = 2, timeUnit = TimeUnit.DAYS, cacheType = CacheType.REMOTE)
    Cache<String, String> signInCache;

    @CreateCache(name = "numberPost:", expire = 23, timeUnit = TimeUnit.HOURS, cacheType = CacheType.REMOTE)
    Cache<String, Long> numberPost;

    @CreateCache(name = "checkAddress:", expire = 1, timeUnit = TimeUnit.HOURS, cacheType = CacheType.REMOTE)
    Cache<String, String> checkAddressCache;
    @Value("${quantumUrl}")
    private  String quantumUrl;
    @Value("${quantumUrlNew}")
    private  String quantumUrlNew;

    @Value("${fdTemplateId}")
    private  String fdTemplateId;

    @Value("${loginName}")
    private  String loginName;
    @Value("${appointNewTemplateId}")
    private  String appointNewTemplateId;
    @Value("${leaveNewTemplateId}")
    private  String leaveNewTemplateId;
    @Value("${revokeNewTemplateId}")
    private  String revokeNewTemplateId;
    @Value("${appointNewId}")
    private  String appointNewId;
    @Value("${leaveNewId}")
    private  String leaveNewId;
    @Value("${revokeNewId}")
    private  String revokeNewId;

    /**
     * FLAG 通过不通过 1 通过
     * COUNT 自定义打卡数量不能大于八
     * KEY redis 下载暂存key
     */
    private static final String FLAG = "1";
    private static final int COUNT = 8;

    public AttendanceSettingServiceImpl(AttendanceAddressMapper attendanceAddressMapper, AgentAttendanceMapper agentAttendanceMapper, LeaveApplicationsMapper leaveApplicationsMapper, OrgApi orgApi, UmApiWrapper umApiWrapper, FileApi fileApi, TemplateConfig templateConfig, DataAccessApi dataAccessApi, MessageService messageService, AttendanceCustomerMapper attendanceCustomerMapper, AttendanceActivityMapper attendanceActivityMapper, AgentImageMapper agentImageMapper, EmployeeApi employeeApi, AttendanceService attendanceService, EmployeeOrgApi employeeOrgApi, AgentAttendanceTimeMapper agentAttendanceTimeMapper, AgentAttendanceExtMapper agentAttendanceExtMapper, RepairLeaveApprovalMapper repairLeaveApprovalMapper, AgentApplyConfigMapper agentApplyConfigMapper, LeaveRevokeMapper leaveRevokeMapper, AttendanceLeaveCheckMapper attendanceLeaveCheckMapper, AgentLeaveRemainingMapper agentLeaveRemainingMapper, LeaveCheckConfigMapper leaveCheckConfigMapper, LeaveApplicationsServiceImpl leaveApplicationsService, UtilsService utilsService, AttendanceConfig attendanceSignInImageConfig, AttendanceConfig attendanceConfig, AttendanceYinBaoService attendanceYinBaoService) {
        this.attendanceAddressMapper = attendanceAddressMapper;
        this.agentAttendanceMapper = agentAttendanceMapper;
        this.leaveApplicationsMapper = leaveApplicationsMapper;
        this.orgApi = orgApi;
        this.umApiWrapper = umApiWrapper;
        this.fileApi = fileApi;
        this.templateConfig = templateConfig;
        this.dataAccessApi = dataAccessApi;
        this.messageService = messageService;
        this.attendanceCustomerMapper = attendanceCustomerMapper;
        this.attendanceActivityMapper = attendanceActivityMapper;
        this.agentImageMapper = agentImageMapper;
        this.employeeApi = employeeApi;
        this.attendanceService = attendanceService;
        this.employeeOrgApi = employeeOrgApi;
        this.agentAttendanceTimeMapper = agentAttendanceTimeMapper;
        this.agentAttendanceExtMapper = agentAttendanceExtMapper;
        this.repairLeaveApprovalMapper = repairLeaveApprovalMapper;
        this.agentApplyConfigMapper = agentApplyConfigMapper;
        this.leaveRevokeMapper = leaveRevokeMapper;
        this.attendanceLeaveCheckMapper = attendanceLeaveCheckMapper;
        this.agentLeaveRemainingMapper = agentLeaveRemainingMapper;
        this.leaveCheckConfigMapper = leaveCheckConfigMapper;
        this.leaveApplicationsService = leaveApplicationsService;
        this.utilsService = utilsService;
        this.attendanceSignInImageConfig = attendanceSignInImageConfig;
        this.attendanceConfig = attendanceConfig;
        this.attendanceYinBaoService = attendanceYinBaoService;
    }

    @Override
    public PageInfo<AttendanceSettingVO> queryList(String companyCode,String companyInstCode, AddressType addressType, String merchantCode, String merchantOrgCode, Boolean status,Boolean authenticationOrNot, long current, long size) {
        MyDataAccessVO myDataAccessVO = dataAccessApi.getCurrentStaffDataAccess(RequestContextHolder.getAdminAppId(), RequestContextHolder.getStaffId());
        List<String> partnerOrgCodes = null;
        if (!myDataAccessVO.getContainsSuperAdmin()) {
            partnerOrgCodes = CollectionUtils.isEmpty(myDataAccessVO.getPartnerOrgCodes()) ? null : Lists.newArrayList(myDataAccessVO.getPartnerOrgCodes());
        }
        log.info("[examineQuery] partnerCodes:{}", JsonUtil.toJSON(partnerOrgCodes));
        Page<AttendanceAddress> page = attendanceAddressMapper.selectPage(new Page<>(current, size),
                Wrappers.lambdaQuery(AttendanceAddress.class)
                        .eq(StringUtils.isNotEmpty(companyCode), AttendanceAddress::getCompanyCode, companyCode)
                        .eq(StringUtils.isNotEmpty(companyInstCode), AttendanceAddress::getCompanyInstCode, companyInstCode)
                        .eq(addressType != null, AttendanceAddress::getAddressType, addressType)
                        .eq(StringUtils.isNotEmpty(merchantCode), AttendanceAddress::getMerchantCode, merchantCode)
                        .eq(StringUtils.isNotEmpty(merchantOrgCode), AttendanceAddress::getMerchantOrgCode, merchantOrgCode)
                        .eq(status != null, AttendanceAddress::getStatus, status)
                        .eq(authenticationOrNot != null, AttendanceAddress::getAuthenticationOrNot, authenticationOrNot)
                        .eq(AttendanceAddress::getDeleted, false)
                        .in( CollectionUtils.isNotEmpty(partnerOrgCodes), AttendanceAddress::getCompanyInstCode,partnerOrgCodes)
                        .eq(AttendanceAddress::getDeleted,false)
                        .orderByDesc(AttendanceAddress::getId)
        );

        return  PageUtil.convert(page,attendanceAddress -> BeanCopier.copyObject(attendanceAddress, AttendanceSettingVO.class));

    }

    /**
     * 打卡地点一级保存
     */
    @Override
    public Boolean saveSetting(AttendanceSettingRequest request) {
        log.info("[saveSetting] request:{}", JsonUtil.toJSON(request));
        AssertUtil.notNull(request.getMerchantOrgName(), new ApiException(400,"打卡地点不能为空"));
        //判断是否重名
        AttendanceAddress attendanceAddress = attendanceAddressMapper.selectOne(Wrappers.lambdaQuery(AttendanceAddress.class)
                .eq(AttendanceAddress::getCompanyInstCode, request.getCompanyInstCode())
                .eq(AttendanceAddress::getDeleted, false)
                .eq(AttendanceAddress::getMerchantOrgName, request.getMerchantOrgName()));
        AssertUtil.isTrue(ObjectUtils.isEmpty(attendanceAddress) ,new ApiException(400,"打卡地点名称不能重复，请重新输入"));
        Integer integer = attendanceAddressMapper.selectCount(Wrappers.lambdaQuery(AttendanceAddress.class)
                .eq(AttendanceAddress::getCompanyInstCode, request.getCompanyInstCode())
                .eq(AttendanceAddress::getAddressType,AddressType.COMPANY)
                .eq(AttendanceAddress::getDeleted, false)
                .eq(AttendanceAddress::getStatus,Boolean.TRUE ));
        AssertUtil.isTrue(integer<COUNT ,new ApiException(400,"当前机构最多自定义"+COUNT+"个打卡地点"));
        attendanceAddressMapper.insert(
                AttendanceAddress.builder()
                        .companyName(request.getCompanyName())
                        .companyCode(request.getCompanyCode())
                        .companyInstCode(request.getCompanyInstCode())
                        .companyInstName(request.getCompanyInstName())
                        .merchantOrgName(request.getMerchantOrgName())
                        .addressType(request.getAddressType().name())
                        .status(Boolean.FALSE)
                        .authenticationOrNot(Boolean.TRUE)
                        .authenticationTime(LocalDateTime.now())
                        .merchantOrgCode(UUID.randomUUID().toString().replace("-", ""))
                        .scope(attendanceAddressScope)
                        .operatorId(RequestContextHolder.getStaffId().toString())
                        .operatorName(RequestContextHolder.getStaffUsername())
                        .deleted(false)
                        .build()
                );
        return true;
    }

    @Override
    public Boolean updateSetting(AttendanceSettingRequest request) {
        if (StringUtils.isNotEmpty(request.getCompanyInstCode()) && FLAG.equals(request.getFlag())){
            Integer integer = attendanceAddressMapper.selectCount(Wrappers.lambdaQuery(AttendanceAddress.class)
                    .eq(AttendanceAddress::getCompanyInstCode, request.getCompanyInstCode())
                    .eq(AttendanceAddress::getAddressType,AddressType.COMPANY)
                    .eq(AttendanceAddress::getDeleted, false)
                    .eq(AttendanceAddress::getStatus,Boolean.TRUE ));
            AssertUtil.isTrue(integer<COUNT ,new ApiException(400,"当前机构最多自定义"+COUNT+"个打卡地点"));
        }
        log.info("[updateSetting] request:{}", JsonUtil.toJSON(request));
            this.update(
                    Wrappers.lambdaUpdate(AttendanceAddress.class)
                            .eq(AttendanceAddress::getId,request.getId())
                            .set(StringUtils.isNotEmpty(request.getCompanyName()),AttendanceAddress::getCompanyName,request.getCompanyName())
                            .set(StringUtils.isNotEmpty(request.getCompanyCode()),AttendanceAddress::getCompanyCode,request.getCompanyCode())
                            //.set(StringUtils.isNotEmpty(request.getCompanyInstCode()),AttendanceAddress::getCompanyInstCode,request.getCompanyInstCode())
                            .set(StringUtils.isNotEmpty(request.getCompanyInstName()),AttendanceAddress::getCompanyInstName,request.getCompanyInstName())
                            .set(StringUtils.isNotEmpty(request.getMerchantOrgName()),AttendanceAddress::getMerchantOrgName,request.getMerchantOrgName())
                            .set(StringUtils.isNotEmpty(request.getLongitude()),AttendanceAddress::getLongitude,request.getLongitude())
                            .set(StringUtils.isNotEmpty(request.getLatitude()), AttendanceAddress::getLatitude,request.getLatitude())
                            .set(request.getStatus() != null , AttendanceAddress::getStatus,request.getStatus())
                            .set( AttendanceAddress::getOperatorId,RequestContextHolder.getStaffId().toString())
                            .set( AttendanceAddress::getOperatorName,RequestContextHolder.getStaffUsername())
                            .set(AttendanceAddress::getUpdateTime, LocalDateTime.now())
            );
        return true;
    }

    @Override
    public PageInfo<AttendanceAuditQueryVO> examineQuery(AttendanceQueryRequest request, long current, long size, Long adminAppId, Long staffId) {
        log.info("[examineQuery] request:{}", JsonUtil.toJSON(request));
        MyDataAccessVO myDataAccessVO = dataAccessApi.getCurrentStaffDataAccess(adminAppId, staffId);
        List<String> partnerCodes = null;
        if (!myDataAccessVO.getContainsSuperAdmin()) {
            partnerCodes = CollectionUtils.isEmpty(myDataAccessVO.getPartnerOrgCodes()) ? null : Lists.newArrayList(myDataAccessVO.getPartnerOrgCodes());
        }
        log.info("[examineQuery] request:{},partnerCodes:{}", JsonUtil.toJSON(request), JsonUtil.toJSON(partnerCodes));
        LambdaQueryWrapper<AgentAttendance> wrapper = Wrappers.lambdaQuery(AgentAttendance.class)
                .eq(StringUtils.isNotEmpty(request.getCompanyCode()), AgentAttendance::getCompanyCode, request.getCompanyCode())
                .eq(request.getAddressType() != null, AgentAttendance::getAddressType, request.getAddressType())
                .eq(StringUtils.isNotEmpty(request.getOrgCode()), AgentAttendance::getOrgCode, request.getOrgCode())
                .eq(StringUtils.isNotEmpty(request.getAudit()), AgentAttendance::getAudit, request.getAudit())
                .like(StringUtils.isNotEmpty(request.getAuditType()), AgentAttendance::getAuditType, request.getAuditType())
                .eq(StringUtils.isNotEmpty(request.getTopCode()), AgentAttendance::getTopCode, request.getTopCode())
                .eq(StringUtils.isNotEmpty(request.getEmployeeCode()), AgentAttendance::getEmployeeCode, request.getEmployeeCode())
                .eq(StringUtils.isNotEmpty(request.getEmployeeName()), AgentAttendance::getEmployeeName, request.getEmployeeName())
                .eq(StringUtils.isNotEmpty(request.getLocationCode()), AgentAttendance::getLocationCode, request.getLocationCode())
                .eq(StringUtils.isNotEmpty(request.getSignInType()), AgentAttendance::getSignInType, request.getSignInType())
                .eq(StringUtils.isNotEmpty(request.getSpotCheckType()), AgentAttendance::getSpotCheckType, request.getSpotCheckType())
                .ge(request.getCheckInTime() != null, AgentAttendance::getCheckInTime, request.getCheckInTime()+" 00:00:00")
                .le(request.getCheckOutTime() != null, AgentAttendance::getCheckInTime, request.getCheckOutTime()+" 23:59:59")
                .in(CollectionUtils.isNotEmpty(partnerCodes), AgentAttendance::getOrgCode, partnerCodes);

        String today = LocalDate.now().toString();
        LocalDate yinBaoStartDate = attendanceSignInImageConfig.getYinBaoStartTime().toInstant().atZone(ZoneId.of("UTC")).toLocalDate();
        wrapper.and(w -> {
            //银保
            w.nested(nested1 -> nested1
                    .eq(AgentAttendance::getTopCode, PermissionType.P00001.name())
                    .and(inner1 -> {
                                //默认查询正常的
                                inner1.isNotNull(AgentAttendance::getCheckOutTime)
                                        //六月一日前的数据没有签退
                                        .or(LocalDate.now().isAfter(yinBaoStartDate),or ->
                                                or.lt(AgentAttendance::getCheckInTime, yinBaoStartDate))
                                        .or( or->
                                                or.like(AgentAttendance::getAuditType,SignInType.MISSED_SIGNING.name()));

                            }
                    )
            );
            //个团 查询前一天的数据
            w.or().nested(nested2 -> nested2
                    .or(or -> or
                            .eq(AgentAttendance::getTopCode, PermissionType.P00003.name())
                            .nested( nest -> nest
                                    .lt(AgentAttendance::getCheckInTime, today + " 00:00:00")
                                    .or( orin-> orin
                                            .apply("date(check_in_time) ='"+today+"'")
                                            .isNotNull(AgentAttendance::getCheckOutTime)
                                    )
                            )
                      )
                    .or( or->or
                            .eq(AgentAttendance::getTopCode, PermissionType.P00004.name())
                            .nested( nest -> nest
                                    .lt(AgentAttendance::getCheckInTime, today + " 00:00:00")
                                    .or( orin-> orin
                                            .apply("date(check_in_time) ='"+today+"'")
                                            .isNotNull(AgentAttendance::getCheckOutTime)
                                    )
                            )
                    ));
        });
        Page<AgentAttendance> page  = agentAttendanceMapper.selectPage(new Page<>(current, size),
                //整体思路就是不查申请的记录
                wrapper.notLike(AgentAttendance::getAuditType,"APPOINT_")
                        .orderByDesc(AgentAttendance::getCheckInTime)
        );
        PageInfo<AttendanceAuditQueryVO> pageInfo = PageUtil.convert(page,agentAttendance -> BeanCopier.copyObject(agentAttendance, AttendanceAuditQueryVO.class));
        //计算停留时长
        if (CollectionUtils.isNotEmpty(pageInfo.getRecords())){
            calculatingTime(pageInfo,yinBaoStartDate);
        }
        return pageInfo;
    }


    /**
     * 判断对象属性全空
     * @param object 对象
     * @return 结果
     */
    public static boolean checkObjAllFieldsIsNull(Object object) {
        if (null == object) {
            return true;
        }
        try {
            for (Field f : object.getClass().getDeclaredFields()) {
                f.setAccessible(true);
                if (f.get(object) != null && StringUtils.isNotEmpty(f.get(object).toString())) {
                    return false;
                }
            }
        } catch (Exception e) {
            log.error("checkObjAllFieldsIsNull_ERROR",e);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AuditResponse auditAttendance(AuditRequest request) {
        DateTimeFormatter dfDateTime = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        AgentAttendance agentAttendance = agentAttendanceMapper.selectById(request.getId());
        LocalDate localDate = agentAttendance.getCheckInTime().toLocalDate();
        AttendanceCheckType type ;
        //通过
        if (FLAG.equals(request.getFlag())){

            String auditType = agentAttendance.getAuditType();
            if (StringUtils.isNotEmpty(auditType)){
                auditType= StreamEx.of(auditType.split(","))
                        .filter(s -> !s.equals(SignInType.MISSED_SIGNING.name()))
                        .collect(Collectors.joining(","));
            }
            agentAttendanceMapper.updateByPrimaryKeySelective(
                    AgentAttendance.builder()
                            .id(request.getId())
                            .audit(AttendanceCheckType.PASS_THE_AUDIT.name())
                            .auditType(auditType)
                            .auditTime(LocalDateTime.now())
                            .updateTime(LocalDateTime.now())
                            .auditor( null == RequestContextHolder.getStaffUsername() ? request.getAuditor() : RequestContextHolder.getStaffUsername())
                            .auditorId(null == RequestContextHolder.getStaffId()? request.getAuditorId() : RequestContextHolder.getStaffId().toString())
                            .build());

            type = AttendanceCheckType.PASS_THE_AUDIT;
            //判断地点认证
            AttendanceAddress attendanceAddress = attendanceAddressMapper.selectOne(Wrappers.lambdaQuery(AttendanceAddress.class)
                    .eq(AttendanceAddress::getMerchantOrgCode, agentAttendance.getLocationCode())
                    .eq(AttendanceAddress::getDeleted, false)
            );
            //未认证
            if (null != attendanceAddress && Boolean.FALSE.equals(attendanceAddress.getAuthenticationOrNot())){
                Integer count = agentAttendanceMapper.selectCount(Wrappers.lambdaQuery(AgentAttendance.class)
                        .eq(AgentAttendance::getLocationCode, agentAttendance.getLocationCode())
                        .eq(AgentAttendance::getAudit, AttendanceCheckType.PASS_THE_AUDIT.name())
                );
                if (agentAttendance.getAuditType().contains(SignInType.NO_ADDRESS.name())){
                    //指定地点不在范围内
                    //未认证第一次申请审核 只更新坐标
                    if (count == 1){
                        attendanceAddressMapper.updateByPrimaryKeySelective(AttendanceAddress.builder()
                                .id(attendanceAddress.getId())
                                .latitude(agentAttendance.getLatitude())
                                .longitude(agentAttendance.getLongitude())
                                .build());
                    }
                    //未认证第二次申请审核 更新坐标更新认证状态
                    if (count == 2){
                        attendanceAddressMapper.updateByPrimaryKeySelective(AttendanceAddress.builder()
                                .id(attendanceAddress.getId())
                                .latitude(agentAttendance.getLatitude())
                                .longitude(agentAttendance.getLongitude())
                                .authenticationOrNot(Boolean.TRUE)
                                .authenticationTime(LocalDateTime.now())
                                .authenticationEmployeeCode(agentAttendance.getEmployeeCode())
                                .authenticationEmployeeName(agentAttendance.getEmployeeName())
                                .build());
                    }
                }
                if (agentAttendance.getAuditType().contains(SignInType.NON_ASSIGNED.name()) && agentAttendance.getAuditType().contains(SignInType.SIGN_IN.name()) ){
                    //未认证第一次申请审核 指定网点的非分派网点
                    if (count >= 1){
                        attendanceAddressMapper.updateByPrimaryKeySelective(AttendanceAddress.builder()
                                .id(attendanceAddress.getId())
                                .authenticationOrNot(Boolean.TRUE)
                                .authenticationTime(LocalDateTime.now())
                                .authenticationEmployeeCode(agentAttendance.getEmployeeCode())
                                .authenticationEmployeeName(agentAttendance.getEmployeeName())
                                .build());
                    }
                }
            }

            //补卡通过消息
            if (agentAttendance.getAuditType().contains(SignInType.APPOINT_HALF.name()) ||
                agentAttendance.getAuditType().contains(SignInType.APPOINT_ALL.name())){
                messageSave(request.getEmployeeCode(),agentAttendance.getEmployeeName(),"",MessageContentType.REISSUE_PASS_THE_AUDIT,localDate);
            }else if(agentAttendance.getAuditType().contains(SignInType.NO_ADDRESS.name()) ||
                    agentAttendance.getAuditType().contains(SignInType.NON_ASSIGNED.name())  ||
                    agentAttendance.getAuditType().contains(SignInType.NON_SPECIFIED.name())) {
                //打卡异常通过消息
                messageSave(request.getEmployeeCode(),agentAttendance.getEmployeeName(),"",MessageContentType.EXCEPTION_RECORD_PASS_THE_AUDIT,localDate);
            }

        }else{
            agentAttendanceMapper.updateByPrimaryKeySelective(AgentAttendance.builder()
                    .id(request.getId())
                    .audit(AttendanceCheckType.AUDIT_FAILURE.name())
                    .auditor( null == RequestContextHolder.getStaffUsername() ? request.getAuditor() : RequestContextHolder.getStaffUsername())
                    .auditorId(null == RequestContextHolder.getStaffId()? request.getAuditorId() : RequestContextHolder.getStaffId().toString())
                    .auditOutcome(request.getReason())
                    .updateTime(LocalDateTime.now())
                    .auditTime(LocalDateTime.now())
                    .build());
            type = AttendanceCheckType.AUDIT_FAILURE;
            //补卡不通过消息
            List<String> strings = Arrays.asList(agentAttendance.getAuditType().split(","));
            if (agentAttendance.getAuditType().contains(SignInType.APPOINT_HALF.name()) ||
                agentAttendance.getAuditType().contains(SignInType.APPOINT_ALL.name())){

                messageSave(request.getEmployeeCode(),agentAttendance.getEmployeeName(),request.getReason(),MessageContentType.REISSUE_AUDIT_FAILURE,localDate);

            }else if((agentAttendance.getAuditType().contains(SignInType.NO_ADDRESS.name()) ||
                    agentAttendance.getAuditType().contains(SignInType.NON_ASSIGNED.name())  ||
                    agentAttendance.getAuditType().contains(SignInType.NON_SPECIFIED.name())) &&
                    agentAttendance.getAudit().equals(AttendanceCheckType.TO_BE_REVIEWED.name()) ) {
                //打卡异常不通过消息
                messageSave(request.getEmployeeCode(),agentAttendance.getEmployeeName(),request.getReason(),MessageContentType.EXCEPTION_RECORD_AUDIT_FAILURE,localDate);
            }else if ((strings.contains(SignInType.SIGN_IN.name()) && strings.size() == 1) ||
                    SpotCheckType.SPOT_CHECK.name().equals(agentAttendance.getSpotCheckType()) ){
                messageSave(request.getEmployeeCode(),agentAttendance.getEmployeeName(),request.getReason(),MessageContentType.SPOT_CHECK_AUDIT_FAILURE,localDate);
            }
        }
        return AuditResponse.builder()
                .audit(type.name())
                .auditor(RequestContextHolder.getStaffUsername())
                .auditOutcome(request.getReason())
                .auditTime(dfDateTime.format(LocalDateTime.now()))
                .build();
    }

    @Override
    @Async("executor")
    public void export(String exportId, AttendanceDetailQueryRequest request, Long adminAppId, Long staffId) {
        MyDataAccessVO myDataAccessVO = dataAccessApi.getCurrentStaffDataAccess(adminAppId, staffId);
        if (!myDataAccessVO.getContainsSuperAdmin()) {
            if (CollectionUtils.isEmpty(myDataAccessVO.getPartnerOrgCodes())){
                throw new ApiException(400,"没有权限导出数据");
            }
        }
        reportExcelData(exportId,request, adminAppId,staffId);
    }

    public void reportExcelData(String exportId,AttendanceDetailQueryRequest request, Long adminAppId, Long staffId) {
        List<AuditReportExcel> auditReportExcels = new ArrayList<>();
        if (StringUtils.isEmpty(request.getType())){
            //导出全部类型
            request.setType(LeaveApplyType.LEAVE.name());
            PageInfo<AttendanceDetailQueryVO> pageLeave = detailQuery(request, 1, 999999,adminAppId,staffId);
            List<AuditReportExcel> pageLeaveList = BeanCopier.copyList(pageLeave.getRecords(), AuditReportExcel.class);
            request.setType(AppointType.APPOINT.name());
            PageInfo<AttendanceDetailQueryVO> pageAppoint = detailQuery(request, 1, 999999,adminAppId,staffId);
            List<AuditReportExcel> pageAppointList = BeanCopier.copyList(pageAppoint.getRecords(), AuditReportExcel.class);
            request.setType(AppointType.NON_SPECIFIED.name());
            PageInfo<AttendanceDetailQueryVO> pageNon = detailQuery(request, 1, 999999,adminAppId,staffId);
            List<AuditReportExcel> pageNonList = BeanCopier.copyList(pageNon.getRecords(), AuditReportExcel.class);
            auditReportExcels.addAll(pageLeaveList);
            auditReportExcels.addAll(pageAppointList);
            auditReportExcels.addAll(pageNonList);
        }else {
            PageInfo<AttendanceDetailQueryVO> pageInfo = detailQuery(request, 1, 999999,adminAppId,staffId);
            auditReportExcels = BeanCopier.copyList(pageInfo.getRecords(), AuditReportExcel.class);
        }
        if (CollectionUtils.isNotEmpty(auditReportExcels)){
            long i=1;
            auditReportExcels.sort(Comparator.comparing(AuditReportExcel::getOrgCode).thenComparing(AuditReportExcel::getDate, Comparator.reverseOrder()));
            for (AuditReportExcel reportExcel : auditReportExcels) {
                reportExcel.setId(i++);
            }
        }

        byte[] bytes = null;
        //查询数据 生成报表
        try (ByteArrayOutputStream bos = new ByteArrayOutputStream()) {
            EasyExcel.write(bos, AuditReportExcel.class)
                    //.registerWriteHandler(new VoteTitleHandler(start[0]+"--"+end[0]))
                    .sheet("后台报表")
                    .doWrite(auditReportExcels);
            bytes = bos.toByteArray();
        } catch (IOException e) {
            log.error("export.error:{}", e.getMessage(), e);
        }
        // 上传阿里云
        FileGetUrlsVO fileGetUrlsVO = fileApi.upload4Base64(FileBase64Request
                .builder()
                .fileName(exportId + ".xlsx")
                .source("admin")
                .fileBase64String(Base64.encode(bytes))
                .build());
        String url ;
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(fileGetUrlsVO.getFileUrlList())) {
            url = fileGetUrlsVO.getFileUrlList().get(0).getOuterUrl();
            //添加redisKey
            signInCache.put(exportId,url);
        }
    }


    @Override
    public Map<String, Object> download(String exportId ,boolean status) {
        Map<String, Object> result = new HashMap<>();
        String url;
        boolean flag ;
        if (status){
            AssertUtil.isTrue(StringUtil.isNotEmpty(exportId) ,new ApiException(400,"报表正在生成中，请耐心等待，稍后再试"));
            url = signInCache.get(exportId);
            AssertUtil.isTrue(StringUtil.isNotEmpty(url) ,new ApiException(400,"报表正在生成中，请耐心等待，稍后再试"));
            flag=Boolean.TRUE;
        }else {
            url = signInCache.get(exportId);
            flag =  StringUtil.isEmpty(url) ? Boolean.FALSE : Boolean.TRUE;
        }
        result.put("url",url);
        result.put("hasFile",flag);
        return result;
    }

    @Override
    public void spotCheck() {
        //是否开启抽查规则：是or否（*若选择是，系统将于每月1号随机抽取30条上个月自动审核通过的打卡记录供内勤审核）
        LocalDate now = LocalDate.now();
        String years = String.valueOf(now.getYear());
        now = now.minusMonths(1);

        // 获取当前月的第一天
        LocalDate firstDay = now.with(TemporalAdjusters.firstDayOfMonth());
        LocalDateTime startTime = firstDay.atTime(0,0,0);
        // 获取当前月的最后一天
        LocalDate lastDay = now.with(TemporalAdjusters.lastDayOfMonth());
        LocalDateTime endTime = lastDay.atTime(23,59,59);
        List<String> orgCodeList = agentAttendanceTimeMapper.queryNeedAuditOrgCodeList(years);
        log.info("打卡抽查spotCheck,需要抽查的orgCodeList:{}",JsonUtil.toJSON(orgCodeList));
        for (String orgCode : orgCodeList){
            //随机查询30个自动审核通过的记录
            List<AgentAttendance> agentAttendanceList = agentAttendanceMapper.randQueryByOrgCode(orgCode,30,startTime,endTime);
            log.info("打卡抽查spotCheck,randQueryByOrgCode,orgCode:{},startTime:{},endTime:{},agentAttendanceList:{}",orgCode,startTime,endTime,agentAttendanceList);
            if (CollectionUtils.isEmpty(agentAttendanceList)){
                continue;
            }
            for (AgentAttendance agentAttendance : agentAttendanceList){
                log.info("打卡抽查spotCheck,randQueryByOrgCode更新ID:{}",agentAttendance.getId());
                AgentAttendance workVo = new AgentAttendance();
                workVo.setId(agentAttendance.getId());
                workVo.setSpotCheckType(SpotCheckType.SPOT_CHECK.name());
                workVo.setAudit(AttendanceCheckType.TO_BE_REVIEWED.name());
                agentAttendanceMapper.updateDeleteByPrimaryKey(workVo);
            }
        }
    }

    @Override
    public void checkAddress() {
        log.info("checkAddress任务,---------------开始");
        String key = "checkAddress";
        String redisValue = checkAddressCache.get(key);
        if (StringUtils.isNotEmpty(redisValue)){
            log.info("checkAddress任务,重复调用,结束");
            return;
        }
        checkAddressCache.put(key,"1");
        orgApi.queryAll().forEach(item->{
            //机构状态(0:未生效 1:生效 2:失效)
            String status = item.getStatus();
            AttendanceAddress attendanceAddress = attendanceAddressMapper.selectOne(Wrappers.lambdaQuery(AttendanceAddress.class)
                    .eq(AttendanceAddress::getMerchantOrgCode, item.getMerchantOrgCode())
            );
            if (ObjectUtils.isEmpty(attendanceAddress) && "1".equals(status)){
                AttendanceAddress address = AttendanceAddress.builder()
                        .companyCode(item.getCompanyCode())
                        .companyName(item.getCompanyName())
                        .addressType(AddressType.MERCHANT.name())
                        .companyInstName(item.getCompanyInstName())
                        .companyInstCode(item.getCompanyInstCode())
                        .merchantName(item.getMerchantName())
                        .merchantCode(item.getMerchantCode())
                        .merchantOrgCode(item.getMerchantOrgCode())
                        .merchantOrgName(item.getMerchantOrgName())
                        .merchantOrgAddress(item.getAddress())
                        .status(Boolean.TRUE)
                        .authenticationOrNot(Boolean.FALSE)
                        .scope(attendanceAddressScope)
                        .longitude("0")
                        .latitude("0")
                        .operatorId("1")
                        .operatorName("admin")
                        .createTime(LocalDateTime.now())
                        .updateTime(LocalDateTime.now())
                        .deleted(false)
                        .build();
                log.info("checkAddress任务,插入新记录,销管:{},本地:{}",JsonUtil.toJSON(item),JsonUtil.toJSON(address));
                attendanceAddressMapper.insert(address);
            } else if(attendanceAddress != null && "1".equals(status)){
                AttendanceAddress address = AttendanceAddress.builder()
                        .id(attendanceAddress.getId())
                        .companyName(item.getCompanyName())
                        .companyCode(item.getCompanyCode())
                        .companyInstCode(item.getCompanyInstCode())
                        .companyInstName(item.getCompanyInstName())
                        .merchantCode(item.getMerchantCode())
                        .merchantName(item.getMerchantName())
                        .merchantOrgCode(item.getMerchantOrgCode())
                        .merchantOrgName(item.getMerchantOrgName())
                        .merchantOrgAddress(item.getAddress())
                        .addressType(AddressType.MERCHANT.name())
                        .updateTime(LocalDateTime.now())
                        .deleted(false)
                        .build();
                log.info("checkAddress任务,更新记录,销管:{},本地:{}",JsonUtil.toJSON(item),JsonUtil.toJSON(address));
                attendanceAddressMapper.updateByPrimaryKeySelective(address);
            } else if(attendanceAddress != null && !"1".equals(status)){
                if (attendanceAddress.getDeleted() != true){
                    attendanceAddress.setDeleted(true);
                    log.info("checkAddress任务,更新记录为失效,销管:{},本地:{}",JsonUtil.toJSON(item),JsonUtil.toJSON(attendanceAddress));
                    attendanceAddressMapper.updateById(attendanceAddress);
                }
            }
        });
        checkAddressCache.remove(key);
        log.info("checkAddress任务,---------------结束");
    }

    @Override
    public AgentAttendanceTime queryAgentAttendanceTime(String year, String orgIns,Boolean ybFlag) {
        LocalDate yinBaoStartDate = attendanceSignInImageConfig.getYinBaoStartTime().toInstant().atZone(ZoneId.of("UTC")).toLocalDate();
        return agentAttendanceTimeMapper.selectOne(new LambdaQueryWrapper<AgentAttendanceTime>()
                .eq(AgentAttendanceTime::getOrgIns, orgIns)
                .le(AgentAttendanceTime::getYears, year)
                .le(orgIns.contains(PermissionType.P00001.name()) && !ybFlag,AgentAttendanceTime::getCreatedTime,yinBaoStartDate + " 00:00:00")
                .ge(orgIns.contains(PermissionType.P00001.name()) && ybFlag,AgentAttendanceTime::getCreatedTime,yinBaoStartDate + " 00:00:00")
                .orderByDesc(AgentAttendanceTime::getYears)
                .orderByDesc(AgentAttendanceTime::getCreatedTime)
                .last("limit 1"));
    }


    @Override
    public void addAttendanceTime(AgentAttendanceTime request) {
        if (null == request) {
            return;
        }
        request.setTimeMinInterval(null == request.getTimeMinInterval() ? 0 : request.getTimeMinInterval());
        LocalDate now = LocalDate.now();
        AgentAttendanceTime configuration;
        LocalDateTime utc = attendanceConfig.getYinBaoStartTime().toInstant().atZone(ZoneId.of("UTC")).toLocalDateTime();

        if (request.getYbFlag()) {
            configuration = agentAttendanceTimeMapper.selectOne(new LambdaQueryWrapper<AgentAttendanceTime>()
                    .eq(AgentAttendanceTime::getOrgIns, request.getOrgIns())
                    .eq(AgentAttendanceTime::getYears, request.getYears())
                    .ge(AgentAttendanceTime::getCreatedTime, utc.withHour(0).withMinute(0).withSecond(0))
                    .le(AgentAttendanceTime::getCreatedTime, now.atTime(23, 59, 59))
                    .ge(AgentAttendanceTime::getCreatedTime, now.atTime(0, 0, 0))
                    .orderByDesc(AgentAttendanceTime::getCreatedTime)
                    .last("limit 1"));
        } else {
            boolean dbParFlag = false;
            boolean gxParFlag = true;
            if (request.getOrgCode().equals("P00001")){
                dbParFlag = true;
                gxParFlag = false;
            }

            configuration = agentAttendanceTimeMapper.selectOne(new LambdaQueryWrapper<AgentAttendanceTime>()
                    .eq(AgentAttendanceTime::getOrgIns, request.getOrgIns())
                    .eq(AgentAttendanceTime::getYears, request.getYears())
                    .le(gxParFlag,AgentAttendanceTime::getCreatedTime, now.atTime(23, 59, 59))//个团走这个where
                    .ge(gxParFlag,AgentAttendanceTime::getCreatedTime, now.atTime(0, 0, 0))//个团走这个where
                    .le(dbParFlag,AgentAttendanceTime::getCreatedTime, utc.minusDays(1).withHour(23).withMinute(59).withSecond(59))//老页面老银保走这个where
                    .ge(dbParFlag,AgentAttendanceTime::getCreatedTime, utc.minusDays(1).withHour(0).withMinute(0).withSecond(0))//老页面老银保走这个where
                    .lt(dbParFlag,AgentAttendanceTime::getCreatedTime, utc.withHour(0).withMinute(0).withSecond(0)) //老页面老银保走这个where
                    .orderByDesc(AgentAttendanceTime::getCreatedTime)
                    .last("limit 1"));
        }

        if (null == configuration) {
            request.setCreatedTime(LocalDateTime.now());
            request.setCreatedUser(RequestContextHolder.getStaffId().toString());
            if (!request.getYbFlag() && request.getOrgCode().equals("P00001") ){
                request.setCreatedTime(utc.minusDays(1).withHour(23).withMinute(59).withSecond(59));
                request.setUpdatedTime(utc.minusDays(1).withHour(23).withMinute(59).withSecond(59));
            }
            agentAttendanceTimeMapper.insertSelective(request);
        } else {
            if (!request.getYbFlag() && request.getOrgCode().equals("P00001")){
                request.setCreatedTime(utc.minusDays(1).withHour(23).withMinute(59).withSecond(59));
                request.setUpdatedTime(utc.minusDays(1).withHour(23).withMinute(59).withSecond(59));
            }
            request.setUpdatedTime(LocalDateTime.now());
            request.setUpdatedUser(RequestContextHolder.getStaffId().toString());
            request.setId(configuration.getId());
            agentAttendanceTimeMapper.updateByPrimaryKeySelective(request);
        }
    }

    @Override
    public AttendanceTeamVO team(AttendanceTeamRequest request) {
        if (null == request || CollectionUtils.isEmpty(request.getEmployeeCode())){
            return AttendanceTeamVO.builder()
                    .noneCount((double) 0)
                    .perDayCount((double) 0)
                    .empDateList(new ArrayList<>())
                    .build();
        }
        List<String> employeeCode = request.getEmployeeCode();
        final EmployeeVO employeeInfo = employeeApi.getEmployeeInfo(AgentOrgType.PARTNER, employeeCode.get(0));
        LocalDate startTime = request.getStartTime();
        LocalDate endTime = request.getEndTime();
        LocalDateTime monthStartTime = startTime.atTime(0,0,0);
        LocalDateTime monthEndTime = endTime.atTime(23,59,59);

        //批量查询签到记录
        List<MonthPunchIn> monthPunchIns = agentAttendanceExtMapper.selectCalendarDataByMonthBatch(DataStatisticsRequest.builder()
                .years(request.getStartTime().getYear())
                .month(request.getStartTime().getMonthValue())
                .build(), employeeCode,monthStartTime,monthEndTime);
        if (CollectionUtils.isEmpty(monthPunchIns)){
            monthPunchIns = new ArrayList<>(0);
        }
        Map<String,List<MonthPunchIn>> punchInsEmployeeMap = monthPunchIns.stream().collect(Collectors.groupingBy(MonthPunchIn::getEmployeeCode));

        List<LeaveApplications> leaveApplications = leaveApplicationsMapper.selectList(new LambdaQueryWrapper<LeaveApplications>()
                .in(LeaveApplications::getEmployeeCode, employeeCode)
                .between(LeaveApplications::getBeginTime,monthStartTime,monthEndTime)
                .eq(LeaveApplications::getRevoked,Boolean.FALSE));
        if (CollectionUtils.isEmpty(leaveApplications)){
            leaveApplications = new ArrayList<>(0);
        }
        List<LeaveApplicationsVO> leaveList = StreamEx.of(leaveApplications).map(leaveApplications1 -> BeanCopier.copyObject(leaveApplications1, LeaveApplicationsVO.class)).toList();
        Map<String,List<LeaveApplicationsVO>> leaveEmployeeMap = leaveList.stream().collect(Collectors.groupingBy(LeaveApplicationsVO::getEmployeeCode));
        double norCount = 0;
        double unuCount = 0;
        final List<AttendanceTeamEmployeeVO> empDateList1 = new ArrayList<>();
        for (String code : employeeCode) {
            List<MonthPunchIn> monthPunchInList = CollectionUtils.isEmpty(punchInsEmployeeMap.get(code)) ? new ArrayList<>(0) : punchInsEmployeeMap.get(code) ;
            List<LeaveApplicationsVO> leaveApplicationsVOList = CollectionUtils.isEmpty(leaveEmployeeMap.get(code)) ? new ArrayList<>(0) : leaveEmployeeMap.get(code);
            final DataStatisticsResponse dataStatisticsResponse = attendanceService.daKaDataStatistics(DataStatisticsRequest
                    .builder()
                    .years(request.getStartTime().getYear())
                    .month(request.getStartTime().getMonthValue())
                    .employeeCode(code)
                    .orgTypeStr(employeeInfo.getOrgType().name())
                    .orgCode(employeeInfo.getOrgCode())
                    .topCode(employeeInfo.getTopCode())
                    .monthPunchInList(monthPunchInList)
                    .leaveApplicationsVOList(leaveApplicationsVOList)
                    .isNeedMonthPunchInCount(Boolean.FALSE)
                    .build());
            norCount += dataStatisticsResponse.getNormalAttendance();
            if (dataStatisticsResponse.getUnusualAttendance() > 0){
                unuCount++;
            }
            empDateList1.add(AttendanceTeamEmployeeVO.builder()
                    .employeeCode(code)
                    .dayCount(dataStatisticsResponse.getNormalAttendance())
                    .noneCount(dataStatisticsResponse.getUnusualAttendance())
                    .needAttendanceDays(Double.parseDouble(dataStatisticsResponse.getNeedAttendanceDays()))
                    .build());
        }
        return AttendanceTeamVO.builder()
                .perDayCount(norCount)
                .noneCount(unuCount)
                .empDateList(empDateList1)
                .build();
    }

    /**
     *  生产指定的随机数
     * @param scope 生成个数
     * @param total 范围
     * @return 集合
     */
    private  List<Integer> randomNum(int scope, int total) {
        List<Integer> list = new ArrayList<>();
        Random rd = new Random();
        while (list.size() < scope) {
            int myNum = rd.nextInt(total);
            if (!list.contains(myNum)) {
                list.add(myNum);
            }
        }
        return list;
    }


    public void messageSave(String employeeCode ,String employeeName, String reason ,MessageContentType messageContentType,LocalDate localDate) {
        log.info("打卡审批发送消息,employeeCode:{},reason:{},MessageContentType{},localDate{}",employeeCode,reason ,messageContentType,localDate);
        AggrSendMessageRequest aggrSendMessageRequest = new AggrSendMessageRequest();
        aggrSendMessageRequest.setEmployeeCode(employeeCode);
        List<MessageSendChannel> messageSendChannelList = new ArrayList<>();
        messageSendChannelList.add(MessageSendChannel.WX);
        messageSendChannelList.add(MessageSendChannel.LOCAL);
        messageSendChannelList.add(MessageSendChannel.PUSH);
        aggrSendMessageRequest.setMessageSendChannelList(messageSendChannelList);
        aggrSendMessageRequest.setMessageContentType(messageContentType.name());
        switch (messageContentType) {
            case REISSUE_PASS_THE_AUDIT:
                //补卡申请 审核通过提醒
                aggrSendMessageRequest.setKey1(employeeName);
                aggrSendMessageRequest.setKey3(DateUtils.LocalDateToString(localDate));
                break;
            case REISSUE_AUDIT_FAILURE:
                //补卡申请 审核未通过提醒
                aggrSendMessageRequest.setKey1(employeeName);
                aggrSendMessageRequest.setKey2(reason);
                aggrSendMessageRequest.setKey3(DateUtils.LocalDateToString(localDate));
                break;
            case EXCEPTION_RECORD_PASS_THE_AUDIT:
                //打卡异常记录 审核通过
                aggrSendMessageRequest.setKey1(employeeName);
                aggrSendMessageRequest.setKey3(DateUtils.LocalDateToString(localDate));
                break;
            case EXCEPTION_RECORD_AUDIT_FAILURE:
                //打卡异常记录 审核未通过提醒
                aggrSendMessageRequest.setKey1(employeeName);
                aggrSendMessageRequest.setKey2(reason);
                aggrSendMessageRequest.setKey3(DateUtils.LocalDateToString(localDate));
                break;
            case SPOT_CHECK_AUDIT_FAILURE:
                //抽查记录 审核未通过提醒
                aggrSendMessageRequest.setKey1(employeeName);
                aggrSendMessageRequest.setKey2(reason);
                aggrSendMessageRequest.setKey3(DateUtils.LocalDateToString(localDate));
                break;
            case LEAVE_PASS_THE_AUDIT:
                //请假申请通过提醒
                aggrSendMessageRequest.setKey1(employeeName);
                aggrSendMessageRequest.setKey3(DateUtils.LocalDateToString(localDate));
                break;
            case LEAVE_AUDIT_FAILURE:
                //请假申请不通过提醒
                aggrSendMessageRequest.setKey1(employeeName);
                aggrSendMessageRequest.setKey2(reason);
                aggrSendMessageRequest.setKey3(DateUtils.LocalDateToString(localDate));
                break;
        }
        messageService.aggrSendMsg(aggrSendMessageRequest);
    }

    public void messageSave2(String employeeCode , String reason ,MessageContentType messageContentType) {
        log.info("[messageSave] employeeCode:{},reason:{},MessageContentType{}",employeeCode,reason ,messageContentType);
        MessageTemplateConfig messageTemplateConfig = templateConfig.getMessage().get(messageContentType.name());
        //站内
        AgentDTO agentDTO = umApiWrapper.getAgentsByEmployee(employeeCode);
        if (null != agentDTO && null != agentDTO.getAgentId() ){
            ArrayList<MessageAddRequest> messageAddRequests = new ArrayList<>();
            messageAddRequests.add( MessageAddRequest.builder()
                    .businessName(reason)
                    .url(messageTemplateConfig.getUrl())
                    .userId(agentDTO.getAgentId())
                    .source(messageContentType)
                    .build());
            messageService.batchAdd(BatchMessageAddRequest.builder().messageList(messageAddRequests).build());
            //站外

            String k1 = agentDTO.getAgentName();
            String k2 = messageTemplateConfig.getKey2();
            String k3 = String.format(messageTemplateConfig.getKey3(),reason);
            String k4 = LocalDateTime.now().format(DateTimeFormatter.ofPattern(DatePatterns.YYYY_MM_DD_HH_MM_SS));
            messageService.wxMessage(
                    Collections.singletonList(WxMessageRequest.builder()
                            .toUser(employeeCode)
                            .templateId("UAT".equals(environment) ? messageTemplateConfig.getUTemplateId() : messageTemplateConfig.getPTemplateId())
                            .url(environmentUrl + messageTemplateConfig.getUrl())
                            .data(sendWxMessage("", k1, k2, k3, k4, ""))
                            .build()));
        }
    }

    @Override
    public void messageSaveByLeader(String employeeCode  , MessageContentType messageContentType, String name) {
        MessageTemplateConfig messageTemplateConfig = templateConfig.getMessage().get(messageContentType.name());
        //站内
        AgentDTO agentDTO = umApiWrapper.getAgentsByEmployee(employeeCode);
        if (null != agentDTO && null != agentDTO.getAgentId() ){
            //站外
            String k1 = name;
            String k2 = messageTemplateConfig.getKey2();
            String k3 = messageTemplateConfig.getKey3();
            String k4 = LocalDateTime.now().format(DateTimeFormatter.ofPattern(DatePatterns.YYYY_MM_DD_HH_MM_SS));
            messageService.wxMessage(
                    Collections.singletonList(WxMessageRequest.builder()
                            .toUser(employeeCode)
                            .templateId("UAT".equals(environment) ? messageTemplateConfig.getUTemplateId() : messageTemplateConfig.getPTemplateId())
                            .url(environmentUrl + messageTemplateConfig.getUrl()+employeeCode)
                            .data(sendWxMessage("", k1,k2 , k3, k4, ""))
                            .build()));
        }
    }

    @Override
    public List<LeaveCheckConfig> queryLeaveConfig(String year,String orgCode) {
        final List<LeaveCheckConfig> checkConfigList =leaveCheckConfigMapper.selectList(new LambdaQueryWrapper<LeaveCheckConfig>()
                .eq(LeaveCheckConfig::getOrgCode, orgCode)
                .eq(LeaveCheckConfig::getDeleted,Boolean.FALSE)
                .groupBy(LeaveCheckConfig::getLeaveType));
        List<LeaveCheckConfig> result  = new ArrayList<>();
        for (LeaveCheckConfig configuration : checkConfigList) {
            String leaveType = configuration.getLeaveType();
            LeaveCheckConfig leaveCheckConfig = leaveCheckConfigMapper.selectOne(new LambdaQueryWrapper<LeaveCheckConfig>()
                    .eq(LeaveCheckConfig::getOrgCode, orgCode)
                    .eq(LeaveCheckConfig::getLeaveType,leaveType)
                    .eq(LeaveCheckConfig::getYears,year)
                    .eq(LeaveCheckConfig::getDeleted, Boolean.FALSE)
                    .orderByDesc(LeaveCheckConfig::getCreatedTime)
                    .last("limit 1"));
            if (leaveCheckConfig == null){
                leaveCheckConfig = leaveCheckConfigMapper.selectOne(new LambdaQueryWrapper<LeaveCheckConfig>()
                        .eq(LeaveCheckConfig::getOrgCode,orgCode)
                        .eq(LeaveCheckConfig::getLeaveType,leaveType)
                        .lt(LeaveCheckConfig::getYears,year)
                        .eq(LeaveCheckConfig::getDeleted, Boolean.FALSE)
                        .orderByDesc(LeaveCheckConfig::getYears)
                        .orderByDesc(LeaveCheckConfig::getCreatedTime)
                        .last("limit 1"));
            }
            result.add(leaveCheckConfig);
        }
        return result;
    }

    @Override
    public void addLeaveConfig(List<LeaveCheckConfigRequest> request) {
        if (CollectionUtils.isEmpty(request)) {
            return;
        }
        attendanceService.logData(JsonUtil.toJSON(request), RequestContextHolder.getStaffUsername(), AttendanceNodeType.APPLY_CONFIG, Boolean.FALSE,"请假配置更新数据");
        for (LeaveCheckConfigRequest leaveCheckConfigRequest : request) {
            LeaveCheckConfig leaveCheckConfig = BeanCopier.copyObject(leaveCheckConfigRequest, LeaveCheckConfig.class);

            if (CollectionUtils.isNotEmpty(leaveCheckConfigRequest.getEntryConfigList())){
                leaveCheckConfig.setEntryConfig(JsonUtil.toJSON(leaveCheckConfigRequest.getEntryConfigList()));
            }
            leaveCheckConfig.setOperatorId(RequestContextHolder.getStaffId().toString());
            leaveCheckConfig.setOperatorName(RequestContextHolder.getStaffUsername());

            final LeaveCheckConfig configuration =leaveCheckConfigMapper.selectOne(new LambdaQueryWrapper<LeaveCheckConfig>()
                    .eq(LeaveCheckConfig::getOrgCode, leaveCheckConfigRequest.getOrgCode())
                    .eq(LeaveCheckConfig::getYears,leaveCheckConfigRequest.getYear())
                    .eq(LeaveCheckConfig::getDeleted,Boolean.FALSE)
                    .eq(LeaveCheckConfig::getLeaveType, leaveCheckConfigRequest.getLeaveType())
                    .orderByDesc(LeaveCheckConfig::getCreatedTime)
                    .last("limit 1"));


            if (null == configuration) {
                LeaveCheckConfig config = BeanCopier.copyObject(leaveCheckConfig, LeaveCheckConfig.class);
                config.setYears(Integer.valueOf(leaveCheckConfigRequest.getYear()));
                config.setCreatedTime(LocalDateTime.now());
                config.setUpdateTime(LocalDateTime.now());
                leaveCheckConfigMapper.insertSelective(config);
            } else {
                leaveCheckConfig.setId(configuration.getId());
                leaveCheckConfig.setUpdateTime(LocalDateTime.now());
                leaveCheckConfigMapper.updateByPrimaryKeySelective(leaveCheckConfig);
            }
        }

    }



    @Override
    public void saveAllStaff() {
        List<AgentLeaveRemaining> saveList = new ArrayList<>();
        List<AgentLeaveRemaining> saveBatchList = new ArrayList<>();


        List<EmployeeVO> employeeVOS = employeeApi.allEmployeesByCompanycodeAndStatus("P00001", "SERVING");
        Map<String, List<EmployeeVO>> listMap = employeeVOS.stream().collect(Collectors.groupingBy(EmployeeVO::getOrgCode));
        listMap.forEach((k,v)->{
            List<LeaveCheckConfig> checkConfigList =leaveCheckConfigMapper.selectList(new LambdaQueryWrapper<LeaveCheckConfig>()
                    .eq(LeaveCheckConfig::getOrgCode, k)
                    .eq(LeaveCheckConfig::getDeleted,Boolean.FALSE)
                    .groupBy(LeaveCheckConfig::getLeaveType));
            for (LeaveCheckConfig configuration : checkConfigList) {
                String leaveType = configuration.getLeaveType();
                LeaveCheckConfig leaveCheckConfig = leaveCheckConfigMapper.selectOne(new LambdaQueryWrapper<LeaveCheckConfig>()
                        .eq(LeaveCheckConfig::getOrgCode, k)
                        .eq(LeaveCheckConfig::getValidity, "15")
                        .eq(LeaveCheckConfig::getLeaveType,leaveType)
                        .eq(LeaveCheckConfig::getYears,LocalDate.now().getYear())
                        .eq(LeaveCheckConfig::getDeleted, Boolean.FALSE)
                        .orderByDesc(LeaveCheckConfig::getCreatedTime)
                        .last("limit 1"));
                if (leaveCheckConfig == null){
                    leaveCheckConfig = leaveCheckConfigMapper.selectOne(new LambdaQueryWrapper<LeaveCheckConfig>()
                            .eq(LeaveCheckConfig::getOrgCode, k)
                            .eq(LeaveCheckConfig::getLeaveType,leaveType)
                            .eq(LeaveCheckConfig::getValidity, "15")
                            .lt(LeaveCheckConfig::getYears,LocalDate.now().getYear())
                            .eq(LeaveCheckConfig::getDeleted, Boolean.FALSE)
                            .orderByDesc(LeaveCheckConfig::getYears)
                            .orderByDesc(LeaveCheckConfig::getCreatedTime)
                            .last("limit 1"));
                }
                if (leaveCheckConfig != null ){
                    processLeaveConfig(leaveCheckConfig,listMap,saveList);
                }
            }
        });

        if (CollectionUtils.isNotEmpty(saveList)){
            for (AgentLeaveRemaining agentLeaveRemaining : saveList) {
                AgentLeaveRemaining selectOne = agentLeaveRemainingMapper.selectOne(Wrappers.lambdaQuery(AgentLeaveRemaining.class)
                        .eq(AgentLeaveRemaining::getYears, LocalDate.now().getYear())
                        .eq(AgentLeaveRemaining::getLeaveType, agentLeaveRemaining.getLeaveType())
                        .eq(AgentLeaveRemaining::getEmployeeCode, agentLeaveRemaining.getEmployeeCode()).last("limit 1"));

                if (null != selectOne){
                    agentLeaveRemainingMapper.updateByPrimaryKeySelective(agentLeaveRemaining);
                }else {
                    saveBatchList.add(agentLeaveRemaining);
                }

            }
            if(CollectionUtils.isNotEmpty(saveBatchList)){
                agentLeaveRemainingMapper.insertBatchSomeColumn(saveBatchList);
            }

        }
    }


    @Override
    public void updateAllStaff() {
        List<AgentLeaveRemaining> agentLeaveRemainingList = agentLeaveRemainingMapper.selectList(Wrappers.lambdaQuery(AgentLeaveRemaining.class)
                .eq(AgentLeaveRemaining::getYears,LocalDate.now().minusYears(1).getYear()));

        for (AgentLeaveRemaining leaveRemaining : agentLeaveRemainingList) {
            if (!leaveRemaining.getResidue().equals("0")){
                String employeeCode = leaveRemaining.getEmployeeCode();
                double residue = Double.parseDouble(leaveRemaining.getResidue());
                LocalDate startTime = LocalDate.now().with(TemporalAdjusters.firstDayOfYear());
                LocalDate endTime = LocalDate.now().withMonth(3).with(TemporalAdjusters.lastDayOfMonth());
                List<LeaveApplications> leaveApplicationsList = getLeaveApplicationsList(employeeCode, leaveRemaining.getLeaveType(),startTime,endTime);
                if (CollectionUtils.isNotEmpty(leaveApplicationsList)){
                    leaveApplicationsList = leaveApplicationsList.stream().filter(x->{
                        int year = x.getApplyTime().getYear();
                        if (year == LocalDate.now().getYear()){
                            return true;
                        }
                        return false;
                    }).collect(Collectors.toList());
                }
                Double duration = setYearsDuration(leaveApplicationsList);
                log.info("更新人员过期,申请总天数:{}",duration);
                //在查询下今年申请去年的
                int startYear = LocalDate.now().minusYears(1).getYear();
                int nowYear= LocalDate.now().getYear();
                List<LeaveApplications> leaveList = leaveApplicationsMapper.selectApplyNowList(employeeCode, nowYear, startYear, leaveRemaining.getLeaveType());
                Double yearsDuration = setYearsDuration(leaveList);

                double overdue =  Math.max(residue- duration-yearsDuration,0);
                leaveRemaining.setOverdue(String.valueOf(overdue));
                agentLeaveRemainingMapper.updateByPrimaryKeySelective(leaveRemaining);
            }
        }

    }


    private void processLeaveConfig(LeaveCheckConfig checkConfig, Map<String, List<EmployeeVO>> listMap, List<AgentLeaveRemaining> saveList) {
        String orgCode = checkConfig.getOrgCode();
        String leaveType = checkConfig.getLeaveType();
        String releaseMode = checkConfig.getReleaseMode();
        List<EmployeeVO> employeeVOList = listMap.get(orgCode);

        if (FixedQuotaType.FIXED_QUOTA.name().equals(checkConfig.getQuotaConfig())) {
            processFixedQuotaConfig(checkConfig, employeeVOList, leaveType, releaseMode,saveList);
        } else if (FixedQuotaType.SENIORITY.name().equals(checkConfig.getQuotaConfig())){
            processEntryConfig(checkConfig, employeeVOList, leaveType,saveList);
        }
    }

    private void processFixedQuotaConfig(LeaveCheckConfig checkConfig, List<EmployeeVO> employeeVOList, String leaveType, String releaseMode, List<AgentLeaveRemaining> saveList) {
        double configurationDays = Double.parseDouble(checkConfig.getDays());
        String daysConfig = checkConfig.getDaysConfig();
        Boolean converted = checkConfig.getConverted();

        for (EmployeeVO employeeVO : employeeVOList) {
            String code = employeeVO.getCode();
            final EmployeeVO emp = employeeApi.getEmployeeByEmployeeCode(AgentOrgType.PARTNER
                    , code);
            LocalDateTime entryTime = emp.getEntryTime();
            long between = ChronoUnit.DAYS.between(entryTime, LocalDateTime.now().with(TemporalAdjusters.lastDayOfYear()));

            double residue = getResidueLeave(code, leaveType);

            if (FixedQuotaType.ANNUALLY.name().equals(daysConfig)) {
                Map<String, Double> map = new HashMap<>();
                map.put("yearsDuration", (double) 0);
                map.put("applyNowDay",(double) 0);

                double count = switchConfigTypeCount(code,leaveType, between, residue, daysConfig, releaseMode, converted, configurationDays,map);
                double configDay = Math.max(0,(residue + configurationDays) - (map.get("yearsDuration")+map.get("applyNowDay")));
                saveList.add(AgentLeaveRemaining.builder()
                                .leaveType(leaveType)
                                .orgCode(employeeVO.getOrgCode())
                                .employeeCode(code)
                                .employeeName(employeeVO.getName())
                                .residue(String.valueOf(count))
                                .overdue("0")
                                .years(LocalDate.now().getYear())
                                .createdTime(LocalDateTime.now())
                                .updateTime(LocalDateTime.now())
                        .build());
            }
        }
    }

    private void processEntryConfig(LeaveCheckConfig checkConfig, List<EmployeeVO> employeeVOList, String leaveType, List<AgentLeaveRemaining> saveList) {
        List<EntryConfig> entryConfigList = JsonUtil.toList(checkConfig.getEntryConfig(), List.class, EntryConfig.class);

        for (int i = 0 ; i < entryConfigList.size() ; i++ ) {
            EntryConfig entryConfig = entryConfigList.get(i);
            double lessEqual = Double.parseDouble(entryConfig.getLessEqual());
            double less = Double.parseDouble(entryConfig.getLess());

            for (EmployeeVO employeeVO : employeeVOList) {
                String code = employeeVO.getCode();
                final EmployeeVO emp = employeeApi.getEmployeeByEmployeeCode(AgentOrgType.PARTNER
                        , code);

                LocalDateTime entryTime = emp.getEntryTime();
                long between = ChronoUnit.DAYS.between(entryTime, LocalDateTime.now().with(TemporalAdjusters.lastDayOfYear()));

                double betweenYears =(double)  between/365;
                double residue = getResidueLeave(code, leaveType);

                if (i != entryConfigList.size() -1){
                    //说明匹配区间
                    if (lessEqual<= betweenYears && betweenYears < less){
                        builderLeaveRemaining(checkConfig, leaveType, saveList, entryConfig, employeeVO, code, between, residue);
                    }
                }else {
                    if (lessEqual<= betweenYears){
                        builderLeaveRemaining(checkConfig, leaveType, saveList, entryConfig, employeeVO, code, between, residue);

                    }
                }
            }
        }
    }

    private void builderLeaveRemaining(LeaveCheckConfig checkConfig, String leaveType, List<AgentLeaveRemaining> saveList, EntryConfig entryConfig, EmployeeVO employeeVO, String code, long between, double residue) {
        String daysConfig = entryConfig.getDaysConfig();
        double configurationDay = Double.parseDouble(entryConfig.getDays());
        if (FixedQuotaType.ANNUALLY.name().equals(checkConfig.getDaysConfig())) {
            Map<String, Double> map = new HashMap<>();
            map.put("yearsDuration", (double) 0);
            map.put("applyNowDay",(double) 0);
            double count = switchConfigTypeCount(code,leaveType, between, residue, daysConfig, checkConfig.getReleaseMode(), Boolean.FALSE, configurationDay,map);
            double configDay = Math.max(0,(residue + configurationDay) - (map.get("yearsDuration")+map.get("applyNowDay")));
            saveList.add(AgentLeaveRemaining.builder()
                    .leaveType(leaveType)
                    .orgCode(employeeVO.getOrgCode())
                    .employeeCode(code)
                    .employeeName(employeeVO.getName())
                    .residue(String.valueOf(count))
                    .overdue("0")
                    .years(LocalDate.now().getYear())
                    .createdTime(LocalDateTime.now())
                    .updateTime(LocalDateTime.now())
                    .build());
        }
    }

    private List<LeaveApplications> getLeaveApplicationsList(String code, String leaveType,LocalDate startTime,LocalDate endTime) {

        Map<String, List<LeaveApplications>> map = leaveApplicationsMapper.selectSumDurationList(code, startTime, endTime, null, null).stream()
                .filter(x -> !x.getAudit().equals(AttendanceCheckType.AUDIT_FAILURE.name()))
                .collect(Collectors.groupingBy(LeaveApplications::getLeaveType));
        return map.get(leaveType);
    }

    private double getResidueLeave(String code, String leaveType) {
        AgentLeaveRemaining leaveRemaining = agentLeaveRemainingMapper.selectOne(Wrappers.lambdaQuery(AgentLeaveRemaining.class)
                .eq(AgentLeaveRemaining::getEmployeeCode, code)
                .eq(AgentLeaveRemaining::getYears, LocalDate.now().minusYears(1).getYear())
                .eq(AgentLeaveRemaining::getLeaveType, leaveType)
                .last("limit 1"));
        return leaveRemaining != null ? Double.parseDouble(leaveRemaining.getResidue()) - Double.parseDouble(leaveRemaining.getOverdue()) : 0;
    }

    private double switchConfigTypeCount(String code,String leaveType , long between, double residue, String daysConfig, String releaseMode, Boolean converted, double configurationDays,Map<String, Double> map) {

        switch (daysConfig) {
            case "ANNUALLY":

                //剩余只能是看剩余次数
                //申请时间是今年但是开始是去年的 记录天数 不足就不让补   查询去年的配置天数
                // 处理去年的情况
                int startYear = LocalDate.now().minusYears(1).getYear();
                int nowYear= LocalDate.now().getYear();
                Double yearsDuration = 0.0;
                List<LeaveApplications> leaveApplicationsList = leaveApplicationsMapper.selectApplyNowList(code, nowYear, startYear, leaveType);
                if (CollectionUtils.isNotEmpty(leaveApplicationsList)){
                    //今年申请去年的天数
                    yearsDuration += setYearsDuration(leaveApplicationsList);
                    if (yearsDuration != 0 ){
                        //如果比剩余次数就不允许请假
                        double max = Math.max(0,residue  - yearsDuration);
                        log.info("剩余能请多少天:{},去年结余天数:{},今年申请去年天数:{}",max,residue,yearsDuration);
                        //已使用天数
                        if(max == 0 ){
                            map.put("applyNowDay",residue);
                        }else {
                            map.put("applyNowDay",yearsDuration);
                        }
                    }

                }

                LocalDate startForYear = LocalDate.now().with(TemporalAdjusters.firstDayOfYear());
                LocalDate endForYear= LocalDate.now().plusYears(1).withMonth(3).with(TemporalAdjusters.lastDayOfMonth());

                List<LeaveApplications> leaveList = leaveApplicationsMapper.selectSumDurationList(code, startForYear, endForYear, null,leaveType);

                if (CollectionUtils.isNotEmpty(leaveList)){
                    leaveList = leaveList.stream().filter(x->{
                        int year = x.getApplyTime().getYear();
                        if (year == LocalDate.now().getYear()){
                            return true;
                        }
                        return false;
                    }).collect(Collectors.toList());
                }
                // 处理每年的情况
                if (FixedQuotaType.ONE_TIME.name().equals(releaseMode)){
                    //说明是固定每年一次性发放 就需要看时候否折算
                    if (converted && between < 365){
                        //需要折算 折算公式 已入职天数/365*配置发放的日期
                        configurationDays = Math.floor(((double) between / 365) * configurationDays * 2) / 2;
                    }
                }else {
                    //按工作时长发放 假期总额*当下月份/12
                    configurationDays = Math.floor(configurationDays * LocalDate.now().getMonthValue() / 12 * 2) / 2;
                }
                //年内请了多少天(总共清了多少天)
                yearsDuration += setYearsDuration(leaveList);
                map.put("yearsDuration",setYearsDuration(leaveList));
                //剩余能请多少天 (结余+配置天数-过期)-请了多少天
                log.info("配置天数:{},总共多少天:{}",configurationDays,yearsDuration);
                return Math.max(0,(residue + configurationDays) - yearsDuration);
            default:
                // 处理其他情况
                return 0;
        }

    }

    private Double setYearsDuration(List<LeaveApplications> leaveApplicationsList) {
        //所有通过的天数
        Double applyCount = 0.0;
        if (CollectionUtils.isNotEmpty(leaveApplicationsList)){
            //过滤不通过的
            List<LeaveApplications> yearList = leaveApplicationsList.stream().filter(x -> !x.getAudit().equals(AttendanceCheckType.AUDIT_FAILURE.name())).collect(Collectors.toList());

            for (LeaveApplications leaveApplications : yearList) {
                LocalDateTime start = leaveApplications.getBeginTime();
                LocalDateTime end = leaveApplications.getEndTime();

               /* if (start.getYear() != LocalDate.now().getYear()){
                    //说明是跨年
                    start = LocalDateTime.now().with(TemporalAdjusters.firstDayOfYear()).with(LocalTime.MIN);
                }
                if (end.getYear() != LocalDate.now().getYear()){
                    //说明是跨年
                    end = LocalDateTime.now().with(TemporalAdjusters.lastDayOfYear()).with(LocalTime.MAX);
                }
*/
                double lastDays = calculateDays(start.toLocalDate(), end.toLocalDate(), amORpm(start).trim(), amORpm(end).trim());
                applyCount += lastDays;
            }

        }
        return applyCount;
    }
    @Override
    public void addCheck(AttendanceLeaveCheck request) {

        final AttendanceLeaveCheck configuration =attendanceLeaveCheckMapper.selectOne(new LambdaQueryWrapper<AttendanceLeaveCheck>()
                .eq(AttendanceLeaveCheck::getOrgCode, request.getOrgCode())
                .eq(AttendanceLeaveCheck::getDeleted,Boolean.FALSE)
                .orderByDesc(AttendanceLeaveCheck::getCreatedTime)
                .last("limit 1"));

        if (null == configuration) {
            request.setOperatorName(RequestContextHolder.getStaffUsername());
            request.setOperatorId(String.valueOf(RequestContextHolder.getStaffId()));
            request.setCreatedTime(LocalDateTime.now());
            request.setUpdateTime(LocalDateTime.now());
            attendanceLeaveCheckMapper.insertSelective(request);
        } else {
            request.setOperatorName(RequestContextHolder.getStaffUsername());
            request.setOperatorId(String.valueOf(RequestContextHolder.getStaffId()));
            request.setCreatedTime(LocalDateTime.now());
            request.setUpdateTime(LocalDateTime.now());
            attendanceLeaveCheckMapper.updateByPrimaryKeySelective(request);
        }
    }

    @Override
    public AttendanceLeaveCheck queryCheck(String orgCode) {
        AttendanceLeaveCheck attendanceLeaveCheck = attendanceLeaveCheckMapper.selectOne(Wrappers.lambdaQuery(AttendanceLeaveCheck.class)
                .eq(AttendanceLeaveCheck::getOrgCode, orgCode)
                .eq(AttendanceLeaveCheck::getDeleted, Boolean.FALSE)
                .last("limit 1"));
        if (null == attendanceLeaveCheck){
            return null;
        }
        //过滤离职的
        String attendanceCodes = attendanceLeaveCheck.getAttendanceCodes();
        String leaveCodes = attendanceLeaveCheck.getLeaveCodes();
        if(StringUtils.isNotEmpty(attendanceCodes)){
            List<String> stringList = Arrays.stream(attendanceCodes.split(",")).collect(Collectors.toList());
            List<String> result = new ArrayList<>();
            for (String empCode : stringList) {
                EmployeeVO employeeInfo = employeeApi.getEmployeeByEmployeeCode(AgentOrgType.PARTNER, empCode);
                if(EmployeeStatus.SERVING.equals(employeeInfo.getStatus())){
                    result.add(empCode);
                }
            }
            attendanceLeaveCheck.setAttendanceCodes( String.join(",", result));
        }
        if(StringUtils.isNotEmpty(leaveCodes)){
            List<String> stringList = Arrays.stream(leaveCodes.split(",")).collect(Collectors.toList());
            List<String> result = new ArrayList<>();
            for (String empCode : stringList) {
                EmployeeVO employeeInfo = employeeApi.getEmployeeByEmployeeCode(AgentOrgType.PARTNER, empCode);
                if(EmployeeStatus.SERVING.equals(employeeInfo.getStatus())){
                    result.add(empCode);
                }
            }
            attendanceLeaveCheck.setLeaveCodes( String.join(",", result));
        }
        return attendanceLeaveCheck ;
    }

    @Override
    public AllowOrNotResponse allowOrNot(String employeeCode) {
        if (StringUtils.isEmpty(employeeCode)){
            employeeCode = RequestContextHolder.getEmployeeCode();
        }
        AttendanceLeaveCheck attendanceLeaveCheck = attendanceLeaveCheckMapper.selectOne(Wrappers.lambdaQuery(AttendanceLeaveCheck.class)
                .eq(AttendanceLeaveCheck::getDeleted, Boolean.FALSE)
                .last(" and (FIND_IN_SET('" + employeeCode + "', attendance_codes) > 0 " +
                        "or FIND_IN_SET('" + employeeCode + "', leave_codes)>0) limit 1"));

        AllowOrNotResponse.AllowOrNotResponseBuilder builder = AllowOrNotResponse.builder()
                .attendance(Boolean.TRUE)
                .leave(Boolean.TRUE);

        if (attendanceLeaveCheck == null){
            return builder.build();
        }
        if(StringUtils.isNotEmpty(attendanceLeaveCheck.getAttendanceCodes()) && attendanceLeaveCheck.getAttendanceCodes().contains(employeeCode)){
            builder.attendance(Boolean.FALSE);
        }
        if(StringUtils.isNotEmpty(attendanceLeaveCheck.getLeaveCodes()) && attendanceLeaveCheck.getLeaveCodes().contains(employeeCode)){
            builder.leave(Boolean.FALSE);
        }

        return builder.build();
    }

    @Override
    @Async("executor")
    public void exportAttendance(String exportId, AttendanceQueryRequest request, Long adminAppId, Long staffId) {
        MyDataAccessVO myDataAccessVO = dataAccessApi.getCurrentStaffDataAccess(adminAppId, staffId);
        if (!myDataAccessVO.getContainsSuperAdmin()) {
            if (CollectionUtils.isEmpty(myDataAccessVO.getPartnerOrgCodes())){
                throw new ApiException(400,"没有权限导出数据");
            }
        }
        reportAttendanceExcelData(exportId,request, adminAppId,staffId);
    }

    private void reportAttendanceExcelData(String exportId, AttendanceQueryRequest request, Long adminAppId, Long staffId) {
        List<AttendanceDetailReportExcel> auditReportExcels = new ArrayList<>();
        PageInfo<AttendanceAuditQueryVO> pageInfo = examineQuery(request, 1, 999999, adminAppId, staffId);
        if (CollectionUtils.isNotEmpty(pageInfo.getRecords())){
            for (AttendanceAuditQueryVO auditQueryVO : pageInfo.getRecords()) {
                AttendanceDetailReportExcel reportExcel = BeanCopier.copyObject(auditQueryVO, AttendanceDetailReportExcel.class);

                reportExcel.setSpotCheckType(SpotCheckType.getDescByName(reportExcel.getSpotCheckType()));
                String auditType = reportExcel.getAuditType();
                if(StringUtils.isNotEmpty(auditType)){
                    String[] split = auditType.split(",");

                    List<String> collect = Arrays.stream(split).map(SignInType::getDescByName).collect(Collectors.toList());

                    reportExcel.setAuditType(String.join(",", collect));

                }

                reportExcel.setAudit(AttendanceCheckType.getDescByName(reportExcel.getAudit()));
                reportExcel.setSignInType(AppointType.getDescByName(reportExcel.getSignInType()));

                auditReportExcels.add(reportExcel);
            }


            auditReportExcels.sort(Comparator.comparing(AttendanceDetailReportExcel::getTopName).thenComparing(AttendanceDetailReportExcel::getDate, Comparator.reverseOrder()));
        }

        byte[] bytes = null;
        //查询数据 生成报表
        try (ByteArrayOutputStream bos = new ByteArrayOutputStream()) {
            EasyExcel.write(bos, AttendanceDetailReportExcel.class)
                    //.registerWriteHandler(new VoteTitleHandler(start[0]+"--"+end[0]))
                    .sheet("后台报表")
                    .doWrite(auditReportExcels);
            bytes = bos.toByteArray();
        } catch (IOException e) {
            log.error("export.error:{}", e.getMessage(), e);
        }
        // 上传阿里云
        FileGetUrlsVO fileGetUrlsVO = fileApi.upload4Base64(FileBase64Request
                .builder()
                .fileName(exportId + ".xlsx")
                .source("admin")
                .fileBase64String(Base64.encode(bytes))
                .build());
        String url ;
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(fileGetUrlsVO.getFileUrlList())) {
            url = fileGetUrlsVO.getFileUrlList().get(0).getOuterUrl();
            //添加redisKey
            signInCache.put(exportId,url);
        }
    }


    public Map<String, Map<String, String>> sendWxMessage(String first, String k1, String k2, String k3, String k4, String remark) {
        Map<String, Map<String, String>> map = new HashMap<>();
        if (StringUtil.isNotEmpty(first)) {
            Map<String, String> childMap = new HashMap<>();
            childMap.put("value", first);
            map.put("first", childMap);
        }
        if (StringUtil.isNotEmpty(k1)) {
            Map<String, String> childMap = new HashMap<>();
            childMap.put("value", k1);
            map.put("keyword1", childMap);
        }
        if (StringUtil.isNotEmpty(k2)) {
            Map<String, String> childMap = new HashMap<>();
            childMap.put("value", k2);
            map.put("keyword2", childMap);
        }
        if (StringUtil.isNotEmpty(k3)) {
            Map<String, String> childMap = new HashMap<>();
            childMap.put("value", k3);
            map.put("keyword3", childMap);
        }
        if (StringUtil.isNotEmpty(k4)) {
            Map<String, String> childMap = new HashMap<>();
            childMap.put("value", k4);
            map.put("keyword4", childMap);
        }
        if (StringUtil.isNotEmpty(remark)) {
            Map<String, String> childMap = new HashMap<>();
            childMap.put("value", remark);
            map.put("remark", childMap);
        }
        return map;
    }

    private void calculatingTime(PageInfo<AttendanceAuditQueryVO> pageInfo,LocalDate yinBaoStartDate) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        DateTimeFormatter formatter2 = DateTimeFormatter.ofPattern("HH:mm:ss");
        DateTimeFormatter formatter3 = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        List<AttendanceAuditQueryVO> records = pageInfo.getRecords();
        //records 按人分组
        List<AttendanceAuditQueryVO> maxList = new ArrayList<>();
        List<AttendanceAuditQueryVO> resultList = new ArrayList<>();
        Map<String, List<AttendanceAuditQueryVO>> collect = records.stream().collect(Collectors.groupingBy(AttendanceAuditQueryVO::getEmployeeCode));
        for (Map.Entry<String, List<AttendanceAuditQueryVO>> entry : collect.entrySet()) {
            List<AttendanceAuditQueryVO> value = entry.getValue();
            maxList.add(value.stream().max(Comparator.comparing(AttendanceAuditQueryVO::getCheckInTime)).get());
        }
        maxList.sort(Comparator.comparing(AttendanceAuditQueryVO::getCheckInTime).reversed());
        Map<Long, Integer> idMap = new HashMap<>();
        for (AttendanceAuditQueryVO attendanceAuditQueryVO : maxList) {
            List<AttendanceAuditQueryVO> vos = collect.get(attendanceAuditQueryVO.getEmployeeCode());
            for (AttendanceAuditQueryVO vo : vos) {
                LocalDateTime inDate = LocalDateTime.parse(vo.getCheckInTime(), formatter3);
                String inTime = inDate.format(formatter2);
                String date = inDate.format(formatter);
                vo.setCheckInTime(inTime);
                vo.setDate(date);
                idMap.put(vo.getId(),1);
            }
            vos.sort(Comparator.comparing(AttendanceAuditQueryVO::getDate).reversed().thenComparing(AttendanceAuditQueryVO::getCheckInTime));
            resultList.addAll(vos);
        }
        List<AttendanceCustomer> attendanceCustomers = attendanceCustomerMapper.selectList(Wrappers.lambdaQuery(AttendanceCustomer.class)
                .in(AttendanceCustomer::getAttendanceId, idMap.keySet()));
        Map<Long,List<AttendanceCustomer>> attendanceCustomersMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(attendanceCustomers)){
            attendanceCustomersMap = attendanceCustomers.stream().collect(Collectors.groupingBy(AttendanceCustomer::getAttendanceId));
        }

        List<AttendanceActivity> attendanceActivities = attendanceActivityMapper.selectList(Wrappers.lambdaQuery(AttendanceActivity.class)
                .in(AttendanceActivity::getAttendanceId, idMap.keySet()));
        Map<Long,List<AttendanceActivity>> attendanceActivitiesMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(attendanceActivities)){
            attendanceActivitiesMap = attendanceActivities.stream().collect(Collectors.groupingBy(AttendanceActivity::getAttendanceId));
        }

        List<AgentImage> agentSignImages = agentImageMapper.selectList(Wrappers.lambdaQuery(AgentImage.class)
                .eq(AgentImage::getImageType, ImageType.SIGN_IN_IMG.name())
                .in(AgentImage::getImageKey, idMap.keySet()));
        List<AgentImage> agentSignOutImages = agentImageMapper.selectList(Wrappers.lambdaQuery(AgentImage.class)
                .eq(AgentImage::getImageType, ImageType.SIGN_OUT_IMG.name())
                .in(AgentImage::getImageKey, idMap.keySet()));
        Map<String,List<AgentImage>> agentSignImagesMap = new HashMap<>();
        Map<String,List<AgentImage>> agentSignOutMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(agentSignImages)){
            agentSignImagesMap = agentSignImages.stream().collect(Collectors.groupingBy(AgentImage::getImageKey));
            agentSignOutMap = agentSignOutImages.stream().collect(Collectors.groupingBy(AgentImage::getImageKey));
        }

       for (AttendanceAuditQueryVO attendanceAuditQueryVO : resultList) {
            LocalDateTime outDate  = null;
            if (StringUtils.isNotEmpty(attendanceAuditQueryVO.getCheckOutTime())){
                outDate = LocalDateTime.parse(attendanceAuditQueryVO.getCheckOutTime(),formatter3);
                String outTime = outDate.format(formatter2);
                attendanceAuditQueryVO.setCheckOutTime(outTime);
            }
            if (StringUtils.isNotEmpty(attendanceAuditQueryVO.getCheckInTime()) && StringUtils.isNotEmpty(attendanceAuditQueryVO.getCheckOutTime())){
                long minute = Duration.between(LocalDateTime.parse(attendanceAuditQueryVO.getDate()+" "+attendanceAuditQueryVO.getCheckInTime(),formatter3), outDate).toMinutes();
                String duration = String.format("%.1f", (double) minute / 60);
                attendanceAuditQueryVO.setDuration(duration+"小时");
            }
            List<AttendanceCustomerVO> attendanceCustomerVOS = new ArrayList<>();
            List<AttendanceActivityVO> attendanceActivityVOS = new ArrayList<>();

            //客户线索详情
           List<AttendanceCustomer> customerList = attendanceCustomersMap.get(attendanceAuditQueryVO.getId());
            if(CollectionUtils.isNotEmpty(customerList)){
                customerList.forEach(x ->
                        attendanceCustomerVOS.add(AttendanceCustomerVO.builder()
                                .customerName(x.getCustomerName())
                                .mangerName(x.getMangerName())
                                .customerGender(x.getCustomerGender())
                                .estimatedPremium(x.getEstimatedPremium())
                                .outlets(x.getOutlets())
                                .otherInfo(x.getOtherInfo())
                                .build())
                );
                attendanceAuditQueryVO.setAttendanceCustomers(attendanceCustomerVOS);
            }

            //推动及活动详情
           List<AttendanceActivity> activityList = attendanceActivitiesMap.get(attendanceAuditQueryVO.getId());
            if (CollectionUtils.isNotEmpty(activityList)){
                activityList.forEach(x ->
                        attendanceActivityVOS.add(AttendanceActivityVO.builder()
                                .label(x.getLabel())
                                .explainTheTopic(x.getExplainTheTopic())
                                .numberOfParticipants(x.getNumberOfParticipants())
                                .activityOutlets(x.getActivityOutlets())
                                .detailRecord(x.getDetailRecord())
                                .build()));
                attendanceAuditQueryVO.setAttendanceActivities(attendanceActivityVOS);
            }

            //影像
           List<AgentImage> imageSignList = agentSignImagesMap.get(String.valueOf(attendanceAuditQueryVO.getId()));
           List<AgentImage> imageSignOutList = agentSignOutMap.get(String.valueOf(attendanceAuditQueryVO.getId()));
            if (CollectionUtils.isNotEmpty(imageSignList)){
                List<String> imageList = new ArrayList<>();
                imageSignList.forEach(x->
                        imageList.add(x.getImageUrl())
                );
                attendanceAuditQueryVO.setImageSignList(imageList);
            }
           if (CollectionUtils.isNotEmpty(imageSignOutList)){
               List<String> imageList = new ArrayList<>();
               imageSignOutList.forEach(x->
                       imageList.add(x.getImageUrl())
               );
               attendanceAuditQueryVO.setImageSignOutList(imageList);
           }
           //非银保漏签退
           if (!attendanceAuditQueryVO.getTopCode().equals(PermissionType.P00001.name()) && StringUtils.isEmpty(attendanceAuditQueryVO.getCheckOutTime()) ){
               //attendanceAuditQueryVO.setAuditType(SignInType.NONE.name());
               if(attendanceAuditQueryVO.getAuditType().contains(SignInType.NO_ADDRESS.name())){
                    attendanceAuditQueryVO.setAuditType(String.join(",",attendanceAuditQueryVO.getAuditType(),SignInType.MISSED_SIGNING.name()));
                }else if (StringUtils.isEmpty(attendanceAuditQueryVO.getCheckOutTime())){
                    attendanceAuditQueryVO.setAuditType(SignInType.MISSED_SIGNING.name());
                }
           }
        }
        pageInfo.setRecords(resultList);
    }

    @Override
    public Map<String, AttendanceAgentVO> attendanceAgents(AttendanceAgentsRequest request){
        Map<String, AttendanceAgentVO> resMap = new HashMap<>();
        String yearMonth = request.getYearMonth();
        String yearStr = yearMonth.substring(0,4);
        LocalDateTime nowTime = LocalDateTime.now();
        String monthStr = String.valueOf(Integer.parseInt(yearMonth.substring(5)));
        List<String> employeeCodeList = request.getEmployeeCodeList();
        if (StringUtils.isEmpty(yearMonth) || CollectionUtils.isEmpty(employeeCodeList)){
            return resMap;
        }
        EmployeeVO employeeInfo;
        for (String employeeCode : employeeCodeList){
            try{
                employeeInfo = employeeApi.getEmployeeByEmployeeCode(AgentOrgType.PARTNER, employeeCode);
            }catch (Exception e){
                log.error("给销管提供代理人考勤查询接口attendanceAgents,代理人身份异常,employeeCode:{}",employeeCode,e);
                resMap.put(employeeCode,null);
                continue;
            }
            AgentAttendanceTime agentAttendanceTime = agentAttendanceTimeMapper.selectOne(new LambdaQueryWrapper<AgentAttendanceTime>()
                    .eq(AgentAttendanceTime::getOrgIns, employeeInfo.getOrgCode())
                    .le(AgentAttendanceTime::getYears, yearStr)
                    .le(AgentAttendanceTime::getCreatedTime,attendanceConfig.getYinBaoStartTime())
                    .orderByDesc(AgentAttendanceTime::getYears)
                    .orderByDesc(AgentAttendanceTime::getCreatedTime)
                    .last("limit 1"));
            if (agentAttendanceTime == null){
                log.info("给销管提供代理人考勤查询接口attendanceAgents,机构未配置,执行配置初始化,OrgCode:{},yearMonth:{}",employeeInfo.getOrgCode(),yearMonth);
                agentAttendanceTime = new AgentAttendanceTime();
                agentAttendanceTime.setOrgCode(employeeInfo.getTopCode());
                agentAttendanceTime.setOrgIns(employeeInfo.getOrgCode());
                agentAttendanceTime.setAudit(false);
                agentAttendanceTime.setMonthJson("{\"1\":22,\"2\":22,\"3\":22,\"4\":22,\"5\":22,\"6\":22,\"7\":22,\"8\":22,\"9\":22,\"10\":22,\"11\":22,\"12\":22}");
                agentAttendanceTime.setYears(yearStr);
                agentAttendanceTime.setPermissions(true);
                agentAttendanceTime.setCreatedTime(nowTime);
                agentAttendanceTime.setUpdatedTime(nowTime);
                agentAttendanceTime.setUpdatedUser("system");
                agentAttendanceTime.setCreatedUser("system");
                if ("P00001".equals(employeeInfo.getTopCode())) {
                    //银保
                    agentAttendanceTime.setAgentClockCount(2);
                    agentAttendanceTime.setLeaderClockCount(1);
                    agentAttendanceTime.setAgentRepairCount(4);
                    agentAttendanceTime.setLeaderRepairCount(4);
                    agentAttendanceTime.setMStartTime("08:00:00");
                    agentAttendanceTime.setMEndTime("12:00:00");
                    agentAttendanceTime.setAStartTime("13:00:00");
                    agentAttendanceTime.setAEndTime("20:00:00");
                    String workDayJson = utilsService.yearWorkDays(yearStr);
                    agentAttendanceTime.setYbMonthJson(workDayJson);
                }else{
                    //非银保
                    agentAttendanceTime.setPermissions(false);
                    agentAttendanceTime.setTimeMinInterval(0);
                }
                agentAttendanceTimeMapper.insert(agentAttendanceTime);
            }
            DataStatisticsRequest dataStatisticsRequest = new DataStatisticsRequest();
            dataStatisticsRequest.setYears(Integer.parseInt(yearStr));
            dataStatisticsRequest.setMonth(Integer.parseInt(monthStr));
            dataStatisticsRequest.setOrgTypeStr(AgentOrgType.PARTNER.name());
            dataStatisticsRequest.setEmployeeCode(employeeCode);
            dataStatisticsRequest.setIsNeedMonthPunchInCount(false);
            dataStatisticsRequest.setEmployeeInfo(employeeInfo);

            DataStatisticsResponse dataStatisticsResponse = attendanceService.daKaDataStatistics(dataStatisticsRequest);
            AttendanceAgentVO attendanceAgentVO = new AttendanceAgentVO();
            attendanceAgentVO.setEmployeeCode(employeeCode);
            attendanceAgentVO.setNeedAttendanceDays(dataStatisticsResponse.getNeedAttendanceDays());
            double normalAttendance = dataStatisticsResponse.getNormalAttendance();
            attendanceAgentVO.setAttendanceDays(new BigDecimal(normalAttendance));
            attendanceAgentVO.setSuccessDaKaDays(new BigDecimal(dataStatisticsResponse.getSuccessDaKaDays()));
            if (attendanceAgentVO.getAttendanceDays().compareTo(new BigDecimal(attendanceAgentVO.getNeedAttendanceDays())) > 0){
                attendanceAgentVO.setAttendanceDays(new BigDecimal(attendanceAgentVO.getNeedAttendanceDays()));
            }
            resMap.put(employeeCode,attendanceAgentVO);
        }
        return resMap;
    }

    /**
     * 后台出勤明细查询
     */
    @Override
    public PageInfo<AttendanceDetailQueryVO> detailQuery(AttendanceDetailQueryRequest request, long current, long size ,  Long adminAppId, Long staffId) {
        MyDataAccessVO myDataAccessVO = dataAccessApi.getCurrentStaffDataAccess(adminAppId, staffId);
        List<String> partnerOrgCodes = null;
        if (!myDataAccessVO.getContainsSuperAdmin()) {
            partnerOrgCodes = CollectionUtils.isEmpty(myDataAccessVO.getPartnerOrgCodes()) ? null : Lists.newArrayList(myDataAccessVO.getPartnerOrgCodes());
        }
        log.info("[examineQuery] partnerCodes:{}", JsonUtil.toJSON(partnerOrgCodes));
        PageInfo<AttendanceDetailQueryVO> pageInfo = new PageInfo<>();
        ArrayList<AttendanceDetailQueryVO> queryVOS = new ArrayList<>();

        if (StringUtils.isNotEmpty(request.getType()) && request.getType().equals(LeaveApplyType.LEAVE.name())){

            LambdaQueryWrapper<LeaveApplications> queryWrapper = Wrappers.lambdaQuery(LeaveApplications.class)
                    .eq(StringUtils.isNotEmpty(request.getTopCode()), LeaveApplications::getCompanyCode, request.getTopCode())
                    .eq(StringUtils.isNotEmpty(request.getOrgCode()), LeaveApplications::getCompanyInstCode, request.getOrgCode())
                    .eq(StringUtils.isNotEmpty(request.getEmployeeCode()), LeaveApplications::getEmployeeCode, request.getEmployeeCode())
                    .eq(StringUtils.isNotEmpty(request.getEmployeeName()), LeaveApplications::getEmployeeName, request.getEmployeeName())
                    .eq(StringUtils.isNotEmpty(request.getMerchantCode()), LeaveApplications::getCompanyCode, request.getMerchantCode())
                    .eq(StringUtils.isNotEmpty(request.getMerchantOrgCode()), LeaveApplications::getCompanyInstCode, request.getMerchantOrgCode())

                    .ge(null != request.getStartTime(), LeaveApplications::getBeginTime, request.getStartTime())
                    .le(null != request.getEndTime(), LeaveApplications::getBeginTime, request.getEndTime())
                    .in(CollectionUtils.isNotEmpty(partnerOrgCodes), LeaveApplications::getCompanyInstCode, partnerOrgCodes);
            if (StringUtils.isNotEmpty(request.getAudit())  && AttendanceCheckType.PASS_THE_AUDIT.name().equals(request.getAudit())){
                queryWrapper.in( LeaveApplications::getAudit,Arrays.asList(AttendanceCheckType.PASS_THE_AUDIT,AttendanceCheckType.AUTO_BYPASS));
            }else if (StringUtils.isNotEmpty(request.getAudit())  && AttendanceCheckType.TO_BE_REVIEWED.name().equals(request.getAudit())){
                queryWrapper.in( LeaveApplications::getAudit,Arrays.asList(AttendanceCheckType.TO_BE_REVIEWED,AttendanceCheckType.UNDER_REVIEW));
            }else {
                queryWrapper.eq(StringUtils.isNotEmpty(request.getAudit()), LeaveApplications::getAudit, request.getAudit());
            }
            /*List<LeaveApplications> leaveApplications = leaveApplicationsMapper.selectList(queryWrapper
                    .eq(LeaveApplications::getRevoked,Boolean.FALSE)
                    .last("order by  company_inst_code asc , apply_time desc"));*/
            Page<LeaveApplications> leaveApplicationsPage = leaveApplicationsMapper.selectPage(new Page<>(current, size), queryWrapper
                    .eq(LeaveApplications::getRevoked, Boolean.FALSE)
                    .last("order by  company_inst_code asc , apply_time desc"));

            if (CollectionUtils.isNotEmpty(leaveApplicationsPage.getRecords())){
                creatLeavData(leaveApplicationsPage.getRecords(), queryVOS);
            }
//            pageInfo = PageUtil.getPageInfo(queryVOS, current, size);
            pageInfo.setRecords(queryVOS);
            pageInfo.setTotal(leaveApplicationsPage.getTotal());
            pageInfo.setPages(leaveApplicationsPage.getPages());
            pageInfo.setCurrent(leaveApplicationsPage.getCurrent());
            pageInfo.setSize(leaveApplicationsPage.getSize());

        }else {
            LambdaQueryWrapper<AgentAttendance> queryWrapper = Wrappers.lambdaQuery(AgentAttendance.class)
                    .eq(StringUtils.isNotEmpty(request.getOrgCode()), AgentAttendance::getOrgCode, request.getOrgCode())
                    .eq(StringUtils.isNotEmpty(request.getTopCode()), AgentAttendance::getTopCode, request.getTopCode())
                    .eq(StringUtils.isNotEmpty(request.getEmployeeCode()), AgentAttendance::getEmployeeCode, request.getEmployeeCode())
                    .eq(StringUtils.isNotEmpty(request.getEmployeeName()), AgentAttendance::getEmployeeName, request.getEmployeeName())
                    .eq(StringUtils.isNotEmpty(request.getMerchantOrgCode()), AgentAttendance::getLocationCode, request.getMerchantOrgCode())
                    .eq(StringUtils.isNotEmpty(request.getMerchantCode()), AgentAttendance::getCompanyCode, request.getMerchantCode())

                    .eq(StringUtils.isNotEmpty(request.getType()), AgentAttendance::getSignInType, request.getType())
                    .ge(request.getStartTime() != null, AgentAttendance::getCheckInTime, request.getStartTime() + " 00:00:00")
                    .le(request.getEndTime() != null, AgentAttendance::getCheckInTime, request.getEndTime() + " 23:59:59")
                    .in(CollectionUtils.isNotEmpty(partnerOrgCodes), AgentAttendance::getOrgCode, partnerOrgCodes);
            if (StringUtils.isNotEmpty(request.getAudit())  && AttendanceCheckType.PASS_THE_AUDIT.name().equals(request.getAudit())){
                queryWrapper.in( AgentAttendance::getAudit,Arrays.asList(AttendanceCheckType.PASS_THE_AUDIT,AttendanceCheckType.AUTO_BYPASS));
            }else if (StringUtils.isNotEmpty(request.getAudit())  && AttendanceCheckType.TO_BE_REVIEWED.name().equals(request.getAudit())){
                queryWrapper.in( AgentAttendance::getAudit,Arrays.asList(AttendanceCheckType.TO_BE_REVIEWED,AttendanceCheckType.UNDER_REVIEW));
            }else {
                queryWrapper.eq(StringUtils.isNotEmpty(request.getAudit()), AgentAttendance::getAudit, request.getAudit());
            }
//            List<AgentAttendance> agentAttendances = agentAttendanceMapper.selectList(queryWrapper.last("order by org_code asc , created_time desc"));

            Page<AgentAttendance> agentAttendancePage = agentAttendanceMapper.selectPage(new Page<>(current, size), queryWrapper.last("order by org_code asc , created_time desc"));

            if (CollectionUtils.isNotEmpty(agentAttendancePage.getRecords())){
                creatAttendanceData(queryVOS, agentAttendancePage.getRecords());
            }
//            pageInfo = PageUtil.getPageInfo(queryVOS, current, size);
            pageInfo.setRecords(queryVOS);
            pageInfo.setTotal(agentAttendancePage.getTotal());
            pageInfo.setPages(agentAttendancePage.getPages());
            pageInfo.setCurrent(agentAttendancePage.getCurrent());
            pageInfo.setSize(agentAttendancePage.getSize());
        }
        return pageInfo;
    }


    @Override
    public List<AgentAttendanceTime> configuration(LocalDateTime requestTime, String orgIns) {
       //查询当前入参时间之前的最新一条数据
        if (null != requestTime){
            requestTime = LocalDateTime.of(requestTime.getYear(), requestTime.getMonth(), requestTime.getDayOfMonth(), 0, 0, 0);
        }
        List<AgentAttendanceTime> agentAttendanceTimes;
        agentAttendanceTimes = agentAttendanceTimeMapper.selectList(Wrappers.lambdaQuery(AgentAttendanceTime.class)
                .eq(AgentAttendanceTime::getOrgIns, orgIns)
                .le(null != requestTime, AgentAttendanceTime::getCreatedTime, requestTime)
                .le(AgentAttendanceTime::getCreatedTime,attendanceConfig.getYinBaoStartTime())
                .orderByDesc(AgentAttendanceTime::getCreatedTime)
                .last(requestTime != null, "limit 1"));
        if (CollectionUtils.isEmpty(agentAttendanceTimes) && null != requestTime){
            agentAttendanceTimes = agentAttendanceTimeMapper.selectList(Wrappers.lambdaQuery(AgentAttendanceTime.class)
                    .eq(AgentAttendanceTime::getOrgIns, orgIns)
                    .le(AgentAttendanceTime::getYears,requestTime.getYear())
                    .le(AgentAttendanceTime::getCreatedTime,attendanceConfig.getYinBaoStartTime())
                    .orderByDesc(AgentAttendanceTime::getYears)
                    .orderByDesc(AgentAttendanceTime::getCreatedTime)
                    .last("limit 1"));
        }
        return agentAttendanceTimes;
    }

    /**
     * status：1 通过 2 驳回 3量子取消
     * rejectType ：0直接驳回
     * 123对应
     */
    @Override
    public void callBack(ProcessCompletedReturnRequestBean request) {

        ProcessCompletedReturnMessageBean messageBean = null;
        if (CollectionUtils.isNotEmpty(request.getMESSAGELIST())){
            messageBean = request.getMESSAGELIST().stream()
                    .filter(bean-> !"结束节点".equals(bean.getOperationType()) && !"知会机构人员".equals(bean.getOperationType()) )
                    .max(Comparator.comparing(ProcessCompletedReturnMessageBean::getApprovalTime))
                    .get();
        }


        attendanceService.logData(JsonUtil.toJSON(request),AttendanceNodeType.APPLY_BACK.name(),AttendanceNodeType.APPLY_BACK,Boolean.TRUE,"");

        RepairLeaveApproval repairLeaveApproval = repairLeaveApprovalMapper.selectOne(Wrappers.lambdaQuery(RepairLeaveApproval.class).eq(RepairLeaveApproval::getBatchId, request.getFd_id()).last("limit 1"));
        String type = repairLeaveApproval.getApplicationType();
        Long id = repairLeaveApproval.getLeaveId();

        RepairLeaveApproval entity = new RepairLeaveApproval();
        entity.setLeaveType("1".equals(request.getStatus()) ? AttendanceCheckType.PASS_THE_AUDIT.name() : AttendanceCheckType.AUDIT_FAILURE.name());
        entity.setLeaveId(id);
        entity.setApplicationType(type);

        repairLeaveApprovalMapper.updateByLeaveId(entity);

        if (type.equals(LeaveApplyType.LEAVE.name())){
            LeaveApplications leaveApplications = leaveApplicationsMapper.selectById(id);
            audit(AuditRequest.builder()
                    .id(leaveApplications.getId())
                    .employeeCode(leaveApplications.getEmployeeCode())
                    .flag("1".equals(request.getStatus()) ?  "1" : "0")
                    .reason(null == messageBean ? "量子" :  messageBean.getOpinion() )
                    .auditor("琴e办")
                    .auditorId("")
                    .build());

        }else if (type.equals(LeaveApplyType.APPOINT.name())){
            AgentAttendance agentAttendance = agentAttendanceMapper.selectById(id);
            auditAttendance(AuditRequest.builder()
                    .employeeCode(agentAttendance.getEmployeeCode())
                    .id(agentAttendance.getId())
                    .flag("1".equals(request.getStatus()) ?  "1" : "0")
                    .reason(null == messageBean ? "量子" :  messageBean.getOpinion() )
                    .auditor("琴e办")
                    .auditorId("")
                    .build());
        }



    }


    @Override
    public KmReviewParamterFormPublicResponse postProcessCompleted(KmReviewParamterFormPublicRequest form) {
        //增加量子申请
        KmReviewParamterFormPublicResponse response = new KmReviewParamterFormPublicResponse();
        try {
            String json = JSON.toJSONString(form);
            log.info("量子申请:{}",json);
            String s = HttpUtils.postJSON(quantumUrl,
                    json
            );
            log.info("量子申请返回:{}",s);
            response = JsonUtil.fromJSON(s,KmReviewParamterFormPublicResponse.class);
            //response = JSON.parseObject(s, KmReviewParamterFormPublicResponse.class);
        }catch (Exception e){
            log.info("postProcessCompleted 量子申请出错 ",e);
        }
        return response;
    }

    @Override
    public KmReviewParamterFormPublicResponse postProcessCompletedNew(ApplyOARequestBean form) {
        //增加量子申请
        KmReviewParamterFormPublicResponse response = new KmReviewParamterFormPublicResponse();
        try {
            String json = JSON.toJSONString(form);
            log.info("新OA申请:{}",json);
            String s = HttpUtils.postJSON(quantumUrlNew,
                    json
            );
            log.info("新OA申请返回:{}",s);
            response = JsonUtil.fromJSON(s,KmReviewParamterFormPublicResponse.class);
            //response = JSON.parseObject(s, KmReviewParamterFormPublicResponse.class);
        }catch (Exception e){
            log.info("postProcessCompletedNew 新OA申请出错 ",e);
        }
        return response;
    }



    @Override
    public KmReviewParamterFormPublicResponse applicationForm(Long id, LeaveApplyType type) {
        log.info("量子申请applicationForm入参::::id:{},type:{}",id,type);
        ArrayList<AttachmentForm> attachmentForms = new ArrayList<>();
        PreBatchQuantumVo preBatchQuantumVo = new PreBatchQuantumVo();

        LocalDate configDate = attendanceSignInImageConfig.getYinBaoStartTime().toInstant()
                .atZone(ZoneId.of("UTC"))
                .toLocalDate();
        //判断当前时间是否在configDate之前
        boolean isBefore = LocalDate.now().isBefore(configDate);
        //新OA请求字段对象
        List<OAFormDataVo> oaFormDataVos = new ArrayList<>();
        List<OAFileVo> oaFileVos = new ArrayList<>();
        String bmdm = "";
        String bmmc = "";
        String sqlx = "";

        String chineseName = null;
        String number = null;
        String empCode = null;
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DatePatterns.YYYY_MM_DD_HH_MM_SS);
        try {
            if (LeaveApplyType.LEAVE.equals(type)){
                List<AgentImage> imageList = agentImageMapper.selectList(Wrappers.lambdaQuery(AgentImage.class)
                        .eq(AgentImage::getImageType, ImageType.LEAVE_IMG)
                        .eq(AgentImage::getImageKey,id));

                LeaveApplications leaveApplications = leaveApplicationsMapper.selectById(id);
                EmployeeOrgVO employeeOrgVO = employeeOrgApi.queryEmployeeOrgInfo(leaveApplications.getEmployeeCode());

                AgentApplyConfig applyConfig = agentApplyConfigMapper.selectOne(Wrappers.lambdaQuery(AgentApplyConfig.class)
                                .eq(AgentApplyConfig::getApplyType,LeaveApplyType.LEAVE.name())
                        .eq(AgentApplyConfig::getOrgCode, leaveApplications.getCompanyInstCode()).last("limit 1"));

                LocalDate startTime = LocalDate.now().withDayOfMonth(1);
                LocalDate endTime = LocalDate.now().withDayOfMonth(LocalDate.now().lengthOfMonth());

                List<LeaveApplications> leaveApplicationsList = leaveApplicationsMapper.selectSumDurationList(leaveApplications.getEmployeeCode(), startTime, endTime, null,null);
                Map<String, Double> durationMap = setDuration(leaveApplicationsList);
                //已申请请假天数
                Double applyCount =durationMap.get("applyCount");
                //审核通过请假天数
                Double duration = durationMap.get("duration");
                //查询销假审核中的天数
                //Double auditCount = leaveApplicationsMapper.selectSumAuditDuration(leaveApplications.getEmployeeCode(), startTime, endTime);
                LeaveApplicationsVO leaveApplicationsVO = leaveApplicationsService.sumCount(leaveApplications.getEmployeeCode(), LeaveApplyType.LEAVE.name());
                String processLeaveApplications = processLeaveApplications(leaveApplicationsVO,leaveApplications.getLeaveType());
                //开始结束时间
                String start = leaveApplications.getBeginTime().toLocalDate() + amORpm(leaveApplications.getBeginTime());
                String end = leaveApplications.getEndTime().toLocalDate() + amORpm(leaveApplications.getEndTime());

                if (CollectionUtils.isNotEmpty(imageList)){
                    if (isBefore){
                        createImage(attachmentForms, imageList);
                    }else {
                        //新
                        createImageNew(oaFileVos, imageList);
                    }
                }

                String businessAreaName = StringUtils.isNotEmpty(employeeOrgVO.getBusinessAreaName())?  employeeOrgVO.getBusinessAreaName() :"";
                String businessDeptName = StringUtils.isNotEmpty(employeeOrgVO.getBusinessDeptName())?  employeeOrgVO.getBusinessDeptName() :"";
                String businessTeamName = StringUtils.isNotEmpty(employeeOrgVO.getBusinessTeamName())?  employeeOrgVO.getBusinessTeamName() :"";

                String leaveDate = start+"~"+end;
                if(start.equals(end)){
                    leaveDate = start;
                }

                number =  createSerialNumber("QJSH",leaveApplications.getCompanyCode());
                chineseName =leaveApplications.getEmployeeName();
                empCode = leaveApplications.getEmployeeCode();
                bmdm =  leaveApplications.getCompanyInstCode();
                bmmc =  leaveApplications.getCompanyInstName();
                sqlx = leaveNewId;
                preBatchQuantumVo = PreBatchQuantumVo.builder()
                        .title(leaveApplications.getEmployeeName()+"请假申请")
                        .employeeName(leaveApplications.getEmployeeName())
                        .topName(leaveApplications.getCompanyInstName())
                        .type(LeaveType.getLabelByName(leaveApplications.getLeaveType()))
                        .team(businessAreaName+businessDeptName+businessTeamName)
                        .leaveDate(leaveDate)
                        .leaveDetail(PrivacyUtil.filterEmoji(leaveApplications.getExplainDetails()))
                        .sumLeaveCount("本月已申请请假"+applyCount+"天,本月请假通过"+duration+"天; "+"本年"+processLeaveApplications)
                        .applyDate(leaveApplications.getApplyTime().format(formatter))
                        .cszhr(applyConfig.getCopyPy())
                        .bmfzr(applyConfig.getDepartmentPy())
                        .jgfgld(applyConfig.getDivisionPy())
                        .jgfzr(applyConfig.getOrganizationPy())
                        .sphzhry(applyConfig.getNotifyPy())
//                        .bmdm(leaveApplications.getCompanyInstCode())
//                        .sqlx(leaveNewId)
                        .build();

            }else  if (LeaveApplyType.APPOINT.equals(type)){
                List<AgentImage> imageList = agentImageMapper.selectList(Wrappers.lambdaQuery(AgentImage.class)
                        .eq(AgentImage::getImageType, ImageType.APPOINT_IMG)
                        .eq(AgentImage::getImageKey,id));
                AgentAttendance agentAttendance = agentAttendanceMapper.selectById(id);
                EmployeeOrgVO employeeOrgVO = employeeOrgApi.queryEmployeeOrgInfo(agentAttendance.getEmployeeCode());

                AgentApplyConfig applyConfig = agentApplyConfigMapper.selectOne(Wrappers.lambdaQuery(AgentApplyConfig.class)
                        .eq(AgentApplyConfig::getApplyType,LeaveApplyType.APPOINT.name())
                        .eq(AgentApplyConfig::getOrgCode, agentAttendance.getOrgCode()).last("limit 1"));

                if (CollectionUtils.isNotEmpty(imageList)){
                    if (isBefore){
                        createImage(attachmentForms, imageList);
                    }else {
                        //新
                        createImageNew(oaFileVos, imageList);
                    }
                }
                List<AttendanceActivity> attendanceActivityList = attendanceActivityMapper.selectList(Wrappers.lambdaQuery(AttendanceActivity.class)
                        .eq(AttendanceActivity::getAttendanceId, id));
                List<AttendanceCustomer> attendanceCustomerList = attendanceCustomerMapper.selectList(Wrappers.lambdaQuery(AttendanceCustomer.class)
                        .eq(AttendanceCustomer::getAttendanceId, id));

                //生成工作日志的Excel
                try {
                    Map<String, Object> map = getExcelAsByteArray(agentAttendance, attendanceActivityList, attendanceCustomerList,isBefore);
                    String url = (String) map.get("url");
                    byte[] asByteArray = (byte[]) map.get("excelByteArray");
                    attachmentForms.add(AttachmentForm.builder()
                            .fdAttachment(asByteArray)
                            .fdFileName("工作日志.xlsx")
                            .fdKey("fj")
                            .build());
                    if (StringUtils.isNotEmpty(url)){
                        oaFileVos.add(OAFileVo.builder()
                                .name("工作日志.xlsx")
                                .path(url)
                                .size("")
                                .build());
                    }
                } catch (Exception e) {
                    attendanceService.logData(JsonUtil.toJSON(preBatchQuantumVo),id+type.name(),AttendanceNodeType.APPLY_FOR,Boolean.FALSE,e.getMessage());
                    log.info("生成工作日志的Excel::::error",e);
                }

                DataStatisticsRequest build = DataStatisticsRequest.builder()
                        .employeeCode(agentAttendance.getEmployeeCode())
                        .orgType(agentAttendance.getOrgType())
                        .month(agentAttendance.getCheckInTime().getMonthValue())
                        .years(agentAttendance.getCheckInTime().getYear())
                        .day(agentAttendance.getCheckInTime().getDayOfMonth())
                        .orgTypeStr("P00001".equals(agentAttendance.getTopCode()) ? "BG" : "!BG")
                        .build();
                log.info("量子申请supplementCount入参::::{}",JSON.toJSONString(build));
                String appointDate = agentAttendance.getCheckInTime().format(formatter);
                Integer supplementCount;
                if (isBefore){
                    supplementCount = attendanceService.querySupplementCount(build);
                }else {
                    supplementCount = attendanceYinBaoService.querySupplementCount(build);
                    if (agentAttendance.getCheckOutTime() != null){
                        String outTime = agentAttendance.getCheckOutTime().toLocalTime().format(DateTimeFormatter.ofPattern(DatePatterns.HH_MM_SS));
                        appointDate = appointDate +" ~ "+outTime;
                    }
                }
                log.info("量子申请supplementCount::::{}",supplementCount);
                String businessAreaName = StringUtils.isNotEmpty(employeeOrgVO.getBusinessAreaName())?  employeeOrgVO.getBusinessAreaName() :"";
                String businessDeptName = StringUtils.isNotEmpty(employeeOrgVO.getBusinessDeptName())?  employeeOrgVO.getBusinessDeptName() :"";
                String businessTeamName = StringUtils.isNotEmpty(employeeOrgVO.getBusinessTeamName())?  employeeOrgVO.getBusinessTeamName() :"";

                number =  createSerialNumber("BKSH",agentAttendance.getTopCode());
                chineseName =agentAttendance.getEmployeeName();
                empCode = agentAttendance.getEmployeeCode();
                String appointAddress = agentAttendance.getLocationName();
                if(agentAttendance.getSignInType().equals(SignInType.NON_SPECIFIED.name())){
                    appointAddress= SignInType.NON_SPECIFIED.getDesc();
                }
                bmdm =  agentAttendance.getOrgCode();
                bmmc =  agentAttendance.getOrgName();
                sqlx = appointNewId;
                preBatchQuantumVo = PreBatchQuantumVo.builder()
                        .title(agentAttendance.getEmployeeName()+"补卡申请")
                        .type(LeaveApplyType.APPOINT.getDesc())
                        .employeeName(agentAttendance.getEmployeeName())
                        .topName(agentAttendance.getOrgName())
                        .team(businessAreaName+businessDeptName+businessTeamName)
                        .applyDate(LocalDate.now().format(DateTimeFormatter.ofPattern(DatePatterns.YYYY_MM_DD)))
                        .appointDate(appointDate)
                        .appointAddress(appointAddress)
                        .surplusAppointCount(supplementCount.toString())
                        .appointDetail(PrivacyUtil.filterEmoji(agentAttendance.getAuditRemark()))
                        .cszhr(applyConfig.getCopyPy())
                        .bmfzr(applyConfig.getDepartmentPy())
                        .jgfgld(applyConfig.getDivisionPy())
                        .jgfzr(applyConfig.getOrganizationPy())
                        .sphzhry(applyConfig.getNotifyPy())
//                        .bmdm(agentAttendance.getOrgCode())
//                        .sqlx(appointNewId)
                        .build();

            }else  if (LeaveApplyType.REVOKE.equals(type)){

                LeaveRevoke leaveRevoke = leaveRevokeMapper.selectById(id);
                EmployeeOrgVO employeeOrgVO = employeeOrgApi.queryEmployeeOrgInfo(leaveRevoke.getEmployeeCode());

                AgentApplyConfig applyConfig = agentApplyConfigMapper.selectOne(Wrappers.lambdaQuery(AgentApplyConfig.class)
                        .eq(AgentApplyConfig::getApplyType,LeaveApplyType.REVOKE.name())
                        .eq(AgentApplyConfig::getOrgCode, leaveRevoke.getCompanyInstCode()).last("limit 1"));

                LocalDate startTime = LocalDate.now().withDayOfMonth(1);
                LocalDate endTime = LocalDate.now().withDayOfMonth(LocalDate.now().lengthOfMonth());

                List<LeaveApplications> leaveApplicationsList = leaveApplicationsMapper.selectSumDurationList(leaveRevoke.getEmployeeCode(), startTime, endTime, null,null);
                Map<String, Double> durationMap = setDuration(leaveApplicationsList);
                //已申请请假天数
                Double applyCount =durationMap.get("applyCount");
                //审核通过请假天数
                Double duration = durationMap.get("duration");
                //查询销假审核中的天数
                //Double auditCount = leaveApplicationsMapper.selectSumAuditDuration(leaveRevoke.getEmployeeCode(), startTime, endTime);

                //开始结束时间
                String start = leaveRevoke.getBeginTime().toLocalDate() + amORpm(leaveRevoke.getBeginTime());
                String end = leaveRevoke.getEndTime().toLocalDate() + amORpm(leaveRevoke.getEndTime());


                String businessAreaName = StringUtils.isNotEmpty(employeeOrgVO.getBusinessAreaName())?  employeeOrgVO.getBusinessAreaName() :"";
                String businessDeptName = StringUtils.isNotEmpty(employeeOrgVO.getBusinessDeptName())?  employeeOrgVO.getBusinessDeptName() :"";
                String businessTeamName = StringUtils.isNotEmpty(employeeOrgVO.getBusinessTeamName())?  employeeOrgVO.getBusinessTeamName() :"";

                String revokeDate = start+"~"+end;
                if(start.equals(end)){
                    revokeDate = start;
                }

                number =  createSerialNumber("QJSH",leaveRevoke.getCompanyCode());
                chineseName =leaveRevoke.getEmployeeName();
                empCode = leaveRevoke.getEmployeeCode();
                bmdm =  leaveRevoke.getCompanyInstCode();
                bmmc =  leaveRevoke.getCompanyInstName();
                sqlx = revokeNewId;
                preBatchQuantumVo = PreBatchQuantumVo.builder()
                        .title(leaveRevoke.getEmployeeName()+"销假申请")
                        .employeeName(leaveRevoke.getEmployeeName())
                        .topName(leaveRevoke.getCompanyInstName())
                        .type(LeaveType.getLabelByName(leaveRevoke.getLeaveType()))
                        .team(businessAreaName+businessDeptName+businessTeamName)
                        .revokeDate(revokeDate)
                        .revokeDetail(PrivacyUtil.filterEmoji(leaveRevoke.getExplainDetails()))
                        .sumLeaveCount("已申请请假"+applyCount+"天,请假通过"+duration+"天")
                        .applyDate(leaveRevoke.getApplyTime().format(formatter))
                        .cszhr(applyConfig.getCopyPy())
                        .bmfzr(applyConfig.getDepartmentPy())
                        .jgfgld(applyConfig.getDivisionPy())
                        .jgfzr(applyConfig.getOrganizationPy())
                        .sphzhry(applyConfig.getNotifyPy())
//                        .bmdm(leaveRevoke.getCompanyInstCode())
//                        .sqlx(revokeNewId)
                        .build();
            }

            //映射到list里
            String templateId = "";
            switch (type){
                case LEAVE:
                    templateId = leaveNewTemplateId;
                    break;
                case REVOKE:
                    templateId = revokeNewTemplateId;
                    break;
                case APPOINT:
                    templateId = appointNewTemplateId;
                    break;
            }
            mapObjectToOaFormDataVos(preBatchQuantumVo,oaFormDataVos);
            oaFormDataVos.add(
                    OAFormDataVo.builder()
                            .fieldName("bmdm")
                            .fieldvalue(bmdm)
                            .build());
            oaFormDataVos.add(
                    OAFormDataVo.builder()
                            .fieldName("sqlx")
                            .fieldvalue(sqlx)
                            .build());
            ApplyOARequestBean oaRequestBean = ApplyOARequestBean.builder()
                    .applyUser(loginName)
                    .requestName("【"+number+"】 " +chineseName+type.getDesc()+"申请")
                    //业务id
                    .ywlxid(templateId)
                    .bmdm(bmdm)
                    .bmmc(bmmc)
                    .mainData(oaFormDataVos)
                    .fileList(oaFileVos)
                    .build();
            KmReviewParamterFormPublicRequest form  = KmReviewParamterFormPublicRequest.builder()
                    .kmReviewParamterForm(KmReviewParamterForm.builder()
                            .fdTemplateId(fdTemplateId)
                            .docCreator("{\"LoginName\": \""+loginName+"\"}")
                            .fdKeyword("[\""+type.getDesc()+"\", \"申请\"]")
                            .docSubject("【"+number+"】 " +chineseName+type.getDesc()+"申请")
                            .formValues(JSON.toJSONString(preBatchQuantumVo))
                            .fdId("")
                            .attachmentForms(attachmentForms)
                            .build())
                    .type("iHomePro-01")
                    .build();
            if (isBefore){
                KmReviewParamterFormPublicResponse response = postProcessCompleted(form);
                if (StringUtils.isNotEmpty(response.getResult()) &&  !"user is not exist".equals(response.getResult())){
                    attendanceService.logData(JsonUtil.toJSON(preBatchQuantumVo),id+type.name(),AttendanceNodeType.APPLY_FOR,Boolean.TRUE,"量子提交申请返回信息 "+ response.getResult());
                    repairLeaveApprovalMapper.insert(RepairLeaveApproval.builder()
                            .leaveId(id)
                            .applicationType(type.name())
                            .batchId(response.getResult())
                            .sub(Boolean.FALSE)
                            .leaveCode(empCode)
                            .leaveType( type.equals(LeaveApplyType.REVOKE) ? AttendanceCheckType.PASS_THE_AUDIT.name()  :  AttendanceCheckType.UNDER_REVIEW.name())
                            .oaType(Boolean.FALSE)
                            .approvalCode("")
                            .createdTime(LocalDateTime.now())
                            .createdUser(2)
                            .build());
                    RepairLeaveApproval entity = new RepairLeaveApproval();
                    entity.setLeaveId(id);
                    entity.setLeaveType(type.equals(LeaveApplyType.REVOKE) ? AttendanceCheckType.PASS_THE_AUDIT.name()  :  AttendanceCheckType.UNDER_REVIEW.name());
                    entity.setApplicationType(type.name());
                    repairLeaveApprovalMapper.updateByLeaveId(entity);
                    if (type.equals(LeaveApplyType.LEAVE)){
                        LeaveApplications leaveApplications = new LeaveApplications();
                        leaveApplications.setId(id);
                        leaveApplications.setAudit(AttendanceCheckType.UNDER_REVIEW.name());
                        leaveApplications.setAuditor("机构领导");
                        leaveApplicationsMapper.updateByPrimaryKeySelective(leaveApplications);
                    }
                    else  if (type.equals(LeaveApplyType.APPOINT)){
                        AgentAttendance agentAttendance = new AgentAttendance();
                        agentAttendance.setId(id);
                        agentAttendance.setAudit(AttendanceCheckType.UNDER_REVIEW.name());
                        agentAttendance.setAuditor("机构领导");
                        agentAttendanceMapper.updateByPrimaryKeySelective(agentAttendance);
                    }
                }else {
                    attendanceService.logData(JsonUtil.toJSON(preBatchQuantumVo),id+type.name(),AttendanceNodeType.APPLY_FOR,Boolean.FALSE,"量子提交申请返回信息为空量子提交错误!!!!");
                    rollbackData(id, type);
                }
                return response;
            }else {
                KmReviewParamterFormPublicResponse responseNew = postProcessCompletedNew(oaRequestBean);
                if (responseNew.getFlag() != null && responseNew.getFlag()){
                    attendanceService.logData(JsonUtil.toJSON(preBatchQuantumVo),id+type.name(),AttendanceNodeType.APPLY_FOR,Boolean.TRUE,"量子提交申请返回信息 "+ responseNew.getMessage());
                    repairLeaveApprovalMapper.insert(RepairLeaveApproval.builder()
                            .leaveId(id)
                            .applicationType(type.name())
                            .batchId(responseNew.getRequestId())
                            .sub(Boolean.FALSE)
                            .oaType(Boolean.TRUE)
                            .leaveCode(empCode)
                            .leaveType(type.equals(LeaveApplyType.REVOKE) ? AttendanceCheckType.PASS_THE_AUDIT.name()  :  AttendanceCheckType.UNDER_REVIEW.name())
                            .approvalCode("")
                            .createdTime(LocalDateTime.now())
                            .createdUser(2)
                            .build());
                    RepairLeaveApproval entity = new RepairLeaveApproval();
                    entity.setLeaveId(id);
                    entity.setLeaveType(type.equals(LeaveApplyType.REVOKE) ? AttendanceCheckType.PASS_THE_AUDIT.name()  :  AttendanceCheckType.UNDER_REVIEW.name());
                    entity.setApplicationType(type.name());
                    repairLeaveApprovalMapper.updateByLeaveId(entity);
                    if (type.equals(LeaveApplyType.LEAVE)){
                        LeaveApplications leaveApplications = new LeaveApplications();
                        leaveApplications.setId(id);
                        leaveApplications.setAudit(AttendanceCheckType.UNDER_REVIEW.name());
                        leaveApplications.setAuditor("机构领导");
                        leaveApplications.setUpdateTime(LocalDateTime.now());
                        leaveApplicationsMapper.updateByPrimaryKeySelective(leaveApplications);
                    }
                    else  if (type.equals(LeaveApplyType.APPOINT)){
                        AgentAttendance agentAttendance = new AgentAttendance();
                        agentAttendance.setId(id);
                        agentAttendance.setAudit(AttendanceCheckType.UNDER_REVIEW.name());
                        agentAttendance.setAuditor("机构领导");
                        agentAttendance.setUpdateTime(LocalDateTime.now());
                        agentAttendanceMapper.updateByPrimaryKeySelective(agentAttendance);
                    }
                }else {
                    attendanceService.logData(JsonUtil.toJSON(preBatchQuantumVo),id+type.name(),AttendanceNodeType.APPLY_FOR,Boolean.FALSE,"量子提交错误" + responseNew.getException());
                    rollbackData(id, type);
                }
                return responseNew;
            }

        }catch (Exception e){
            log.info("量子提交申请错误::::error",e);
            attendanceService.logData(JsonUtil.toJSON(preBatchQuantumVo),id+type.name(),AttendanceNodeType.APPLY_FOR,Boolean.FALSE,"量子提交申请错误::::"+e.getMessage());
            rollbackData(id, type);
        }
        return KmReviewParamterFormPublicResponse.builder()
                .requestId("500")
                .message("量子提交申请错误")
                .build();
    }

    public String processLeaveApplications(LeaveApplicationsVO leaveApplicationsVO ,String type) {
        List<String> formatList = new ArrayList<>();
        String[] allLeaveTypes = leaveApplicationsVO.getAllLeaveTypes();
        List<String> LeaveTypeList = new ArrayList<>();
        //不配置也不限制
//        LeaveTypeList.add(LeaveType.BUSINESS_TRAVEL_LEAVE.name());
        //LeaveTypeList.add(LeaveType.OFFICIAL_BUSINESS_LEAVE.name());
        for (int i = 0; i < allLeaveTypes.length; i++) {
            String leave = allLeaveTypes[i];
            if (StringUtils.isNotEmpty(leave)){
                LeaveType leaveType = LeaveType.values()[i];
                String[] sickLeaveList = leave.split(",");
                if (leaveType.name().equals(type)){
                    if (sickLeaveList.length == 1 ){
                        String a = leaveType.getLabel()+":已休 %s 天";
                        formatList.add(String.format(a, sickLeaveList[0]));
                    }else {
                        String a = leaveType.getLabel()+":已休 %s 天，剩余 %s 天";
                        formatList.add(String.format(a, sickLeaveList[0], sickLeaveList[1]));
                    }
                    /*String a = leaveType.getLabel()+":已休 %s 天，剩余 %s 天";
                    formatList.add(String.format(a, sickLeaveList[0], sickLeaveList[1]));*/
                }
            }
        }
        return String.join("; ",formatList);
    }

    private Map<String,Double> setDuration(List<LeaveApplications> leaveApplicationsList) {
        Map<String, Double> result = new HashMap<>();
        Double applyCount = 0.0;
        Double duration = 0.0;
        if (CollectionUtils.isNotEmpty(leaveApplicationsList)){
            //审核通过
            List<LeaveApplications> passList = leaveApplicationsList.stream().filter(x -> x.getAudit().equals(AttendanceCheckType.PASS_THE_AUDIT.name())).collect(Collectors.toList());

            for (LeaveApplications applications : leaveApplicationsList) {
                LocalDateTime start = applications.getBeginTime();
                LocalDateTime end = applications.getEndTime();
                if (applications.getBeginTime().getMonthValue() != LocalDate.now().getMonthValue()){
                    //说明是跨月
                    start = LocalDateTime.now().with(TemporalAdjusters.firstDayOfMonth());
                }
                if (applications.getEndTime().getMonthValue() != LocalDate.now().getMonthValue()){
                    //说明是跨月
                    end = LocalDateTime.now().with(TemporalAdjusters.lastDayOfMonth());
                }

                double lastDays = calculateDays(start.toLocalDate(), end.toLocalDate(), amORpm(start).trim(), amORpm(end).trim());
                applyCount += lastDays;
            }
            if (CollectionUtils.isNotEmpty(passList)){
                for (LeaveApplications applications : passList) {
                    LocalDateTime start = applications.getBeginTime();
                    LocalDateTime end = applications.getEndTime();
                    if (applications.getBeginTime().getMonthValue() != LocalDate.now().getMonthValue()){
                        //说明是跨月
                        start = LocalDateTime.now().with(TemporalAdjusters.firstDayOfMonth());
                    }
                    if (applications.getEndTime().getMonthValue() != LocalDate.now().getMonthValue()){
                        //说明是跨月
                        end = LocalDateTime.now().with(TemporalAdjusters.lastDayOfMonth());
                    }

                    double lastDays = calculateDays(start.toLocalDate(), end.toLocalDate(), amORpm(start).trim(), amORpm(end).trim());
                    duration += lastDays;
                }
            }
        }
        result.put("applyCount",applyCount);
        result.put("duration",duration);
        return result;
    }

    private void rollbackData(Long id, LeaveApplyType type) {
        if (LeaveApplyType.LEAVE.equals(type)){
            LeaveApplications leaveApplications = new LeaveApplications();
            leaveApplications.setId(id);
            leaveApplications.setAudit(AttendanceCheckType.TO_BE_REVIEWED.name());
            leaveApplicationsMapper.updateByPrimaryKeySelective(leaveApplications);

        }
        else  if (LeaveApplyType.APPOINT.equals(type)){
            AgentAttendance agentAttendance = new AgentAttendance();
            agentAttendance.setId(id);
            agentAttendance.setAudit(AttendanceCheckType.TO_BE_REVIEWED.name());
            agentAttendanceMapper.updateByPrimaryKeySelective(agentAttendance);
        }
        if (!type.equals(LeaveApplyType.REVOKE)){
            Long byLeaveId = repairLeaveApprovalMapper.selectByLeaveId(id,type);
            RepairLeaveApproval entity = new RepairLeaveApproval();
            entity.setLeaveId(id);
            entity.setId(byLeaveId);
            entity.setSub(Boolean.TRUE);
            entity.setLeaveType(AttendanceCheckType.TO_BE_REVIEWED.name());
            repairLeaveApprovalMapper.updateByPrimaryKeySelective(entity);
        }

    }

    private String createSerialNumber(String var1 , String var2) {
        DateTimeFormatter
                formatter = DateTimeFormatter.ofPattern(DatePatterns.CONTINUITY_YYYY_MM_DD);
        String currentDate = LocalDate.now().format(formatter);
        String key = var1+ var2 + currentDate;
        Long serialNumber = numberPost.computeIfAbsent(key, k -> 0L);
        serialNumber++;
        numberPost.put(key, serialNumber);
        String formattedSerialNumber = String.format("%04d", serialNumber % 10000);
        return var1+ var2 + currentDate + formattedSerialNumber;
    }

    private void createImage(ArrayList<AttachmentForm> attachmentForms, List<AgentImage> imageList) throws IOException {
        int i = 1;
        for (AgentImage agentImage : imageList) {
            byte[] bytes = downloadFile(agentImage.getImageUrl());
            attachmentForms.add(AttachmentForm.builder()
                    .fdAttachment(bytes)
                    .fdFileName("图片"+ i++ +".jpg")
                    .fdKey("fj")
                    .build());
        }
    }

    private void createImageNew(List<OAFileVo> oaFileVos, List<AgentImage> imageList) throws IOException {
        int i = 1;
        for (AgentImage agentImage : imageList) {
            oaFileVos.add(OAFileVo.builder()
                    .name("图片"+ i++ +".jpg")
                    .path("/wui/cas-entrance.jsp?path=" + agentImage.getImageUrl())
                    .size("")
                    .build());
        }
    }

    public  Map<String, Object> getExcelAsByteArray(AgentAttendance agentAttendance,List<AttendanceActivity> attendanceActivityList,List<AttendanceCustomer> attendanceCustomerList , Boolean isBefore) throws IOException {
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("工作计划");
        sheet.setColumnWidth(0,20*256);
        sheet.setColumnWidth(1,20*256);
        // 设置单元格格式居中
        CellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        Row row = sheet.createRow(0);
        // 工作计划 添加表头内容
        Cell cell = row.createCell(0);
        cell.setCellValue("网点经营");
        cell.setCellStyle(cellStyle);
        cell = row.createCell(1);
        cell.setCellValue("网点会议/活动");
        cell.setCellStyle(cellStyle);
        Row row1 = sheet.createRow(1);
        Cell cell1 = row1.createCell(0);
        cell1.setCellValue(agentAttendance.getBusinessLabel());
        cell1.setCellStyle(cellStyle);
        cell1 = row1.createCell(1);
        cell1.setCellValue(agentAttendance.getActivityLabel());
        cell1.setCellStyle(cellStyle);


        Sheet sheet1 = workbook.createSheet("客户线索详情");
        sheet1.setColumnWidth(0,20*256);
        sheet1.setColumnWidth(1,20*256);
        sheet1.setColumnWidth(2,20*270);
        sheet1.setColumnWidth(3,20*256);
        sheet1.setColumnWidth(4,20*256);
        sheet1.setColumnWidth(5,20*256);
        Row rowCustomer = sheet1.createRow(0);
        Cell customerCell = rowCustomer.createCell(0);
        customerCell.setCellValue("姓名");
        customerCell.setCellStyle(cellStyle);
        customerCell = rowCustomer.createCell(1);
        customerCell.setCellValue("性别");
        customerCell.setCellStyle(cellStyle);
        customerCell = rowCustomer.createCell(2);
        customerCell.setCellValue("预计保费金额（万元）");
        customerCell.setCellStyle(cellStyle);
        customerCell = rowCustomer.createCell(3);
        customerCell.setCellValue("网点");
        customerCell.setCellStyle(cellStyle);
        customerCell = rowCustomer.createCell(4);
        customerCell.setCellValue("服务理财经理姓名");
        customerCell.setCellStyle(cellStyle);
        customerCell = rowCustomer.createCell(5);
        customerCell.setCellValue("其他详情记录");
        customerCell.setCellStyle(cellStyle);
        for (int i = 0; i < attendanceCustomerList.size(); i++) {
            rowCustomer = sheet1.createRow(i + 1);
            AttendanceCustomer customer = attendanceCustomerList.get(i);

            Cell data = rowCustomer.createCell(0);
            data.setCellValue(customer.getCustomerName());
            data.setCellStyle(cellStyle);

            data = rowCustomer.createCell(1);
            data.setCellValue(Gender.getDescByName(customer.getCustomerGender()));
            data.setCellStyle(cellStyle);

            data = rowCustomer.createCell(2);
            data.setCellValue(null == customer.getEstimatedPremium() ? "0" : customer.getEstimatedPremium().toString());
            data.setCellStyle(cellStyle);

            data = rowCustomer.createCell(3);
            data.setCellValue(customer.getOutlets());
            data.setCellStyle(cellStyle);

            data = rowCustomer.createCell(4);
            data.setCellValue(customer.getMangerName());
            data.setCellStyle(cellStyle);

            data = rowCustomer.createCell(5);
            data.setCellValue(customer.getOtherInfo());
            data.setCellStyle(cellStyle);
        }


        Sheet sheet2 = workbook.createSheet("推动及活动详情");
        sheet2.setColumnWidth(0,20*256);
        sheet2.setColumnWidth(1,20*256);
        sheet2.setColumnWidth(2,20*256);
        sheet2.setColumnWidth(3,20*256);
        Row rowActivity = sheet2.createRow(0);
        Cell activityCell = rowActivity.createCell(0);
        activityCell.setCellValue("推动形式");
        activityCell.setCellStyle(cellStyle);
        activityCell = rowActivity.createCell(1);
        activityCell.setCellValue("讲解主题");
        activityCell.setCellStyle(cellStyle);
        activityCell = rowActivity.createCell(2);
        activityCell.setCellValue("参与人数");
        activityCell.setCellStyle(cellStyle);
        activityCell = rowActivity.createCell(3);
        activityCell.setCellValue("网点");
        activityCell.setCellStyle(cellStyle);
        for (int i = 0; i < attendanceActivityList.size(); i++) {
            rowActivity = sheet2.createRow(i + 1);
            AttendanceActivity activity = attendanceActivityList.get(i);

            Cell data = rowActivity.createCell(0);
            data.setCellValue(activity.getLabel());
            data.setCellStyle(cellStyle);

            data = rowActivity.createCell(1);
            data.setCellValue(activity.getExplainTheTopic());
            data.setCellStyle(cellStyle);

            data = rowActivity.createCell(2);
            data.setCellValue( null  ==  activity.getNumberOfParticipants()?   "": activity.getNumberOfParticipants().toString()  );
            data.setCellStyle(cellStyle);

            data = rowActivity.createCell(3);
            data.setCellValue(activity.getActivityOutlets());
            data.setCellStyle(cellStyle);

        }

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        workbook.write(outputStream);
        byte[] excelByteArray = outputStream.toByteArray();

        workbook.close();
        outputStream.close();
        Map<String, Object> map = new HashMap<>();
        if (!isBefore && CollectionUtils.isNotEmpty(attendanceActivityList) && CollectionUtils.isNotEmpty(attendanceCustomerList)){
            // 上传阿里云
            FileGetUrlsVO fileGetUrlsVO = fileApi.upload4Base64(FileBase64Request
                    .builder()
                    .fileName("打卡工作日志"+LocalDateTime.now().format(DateTimeFormatter.ofPattern(DatePatterns.CONTINUITY_YYYY_MM_DD_HH_MM_SS)) + ".xlsx")
                    .source("admin")
                    .fileBase64String(Base64.encode(excelByteArray))
                    .build());
            map.put("url", fileGetUrlsVO.getFileUrlList().get(0).getOuterUrl());
        }


        map.put("excelByteArray", excelByteArray);

        return map;
    }

    public static byte[] downloadFile(String fileUrl) {
        try {
            URL url = new URL(fileUrl);
            try (InputStream in = new BufferedInputStream(url.openStream());
                 ByteArrayOutputStream out = new ByteArrayOutputStream()) {
                byte[] buffer = new byte[1024];
                int bytesRead;
                while ((bytesRead = in.read(buffer, 0, buffer.length)) != -1) {
                    out.write(buffer, 0, bytesRead);
                }
                return out.toByteArray();
            } catch (IOException e) {
                e.printStackTrace();
            }
        } catch (MalformedURLException e) {
            e.printStackTrace();
        }
        return  new byte[0];
    }

    private void creatAttendanceData(ArrayList<AttendanceDetailQueryVO> queryVOS, List<AgentAttendance> page) {
        List<Long> signId = page.stream().map(AgentAttendance::getId).collect(Collectors.toList());
        List<String> employeeCodeList = page.stream().map(AgentAttendance::getEmployeeCode).distinct().collect(Collectors.toList());

        List<AttendanceCustomer> attendanceCustomers = attendanceCustomerMapper.selectList(Wrappers.lambdaQuery(AttendanceCustomer.class)
                .in(AttendanceCustomer::getAttendanceId, signId));
        List<AttendanceActivity> attendanceActivities = attendanceActivityMapper.selectList(Wrappers.lambdaQuery(AttendanceActivity.class)
                .in(AttendanceActivity::getAttendanceId, signId));
        List<EmployeeOrgVO> employeeOrgVOList = employeeOrgApi.queryEmployeeOrgInfo(employeeCodeList);
        Map<String, EmployeeOrgVO> employeeOrgVOMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(employeeOrgVOList)){
            employeeOrgVOMap = employeeOrgVOList.stream().collect(Collectors.toMap(EmployeeOrgVO::getEmployeeCode, Function.identity(), (k1, k2) -> k1));
        }

        Map<Long, List<AttendanceCustomer>> attendanceCustomerMap = attendanceCustomers.stream().collect(Collectors.groupingBy(AttendanceCustomer::getAttendanceId));
        Map<Long, List<AttendanceActivity>> attendanceActivityMap = attendanceActivities.stream().collect(Collectors.groupingBy(AttendanceActivity::getAttendanceId));

        for (AgentAttendance agentAttendance : page) {
            //List<AttendanceCustomer> attendanceCustomerList = attendanceCustomerMapper.selectList(Wrappers.lambdaQuery(AttendanceCustomer.class).eq(AttendanceCustomer::getAttendanceId, agentAttendance.getId()));
            //List<AttendanceActivity> attendanceActivityList = attendanceActivityMapper.selectList(Wrappers.lambdaQuery(AttendanceActivity.class).eq(AttendanceActivity::getAttendanceId, agentAttendance.getId()));
            List<AttendanceCustomer> attendanceCustomerList = attendanceCustomerMap.get(agentAttendance.getId());
            List<AttendanceActivity> attendanceActivityList = attendanceActivityMap.get(agentAttendance.getId());
            AttendanceDetailQueryVO queryVO = BeanCopier.copyObject(agentAttendance, AttendanceDetailQueryVO.class);
            queryVO.setSignInType(AppointType.getDescByName(queryVO.getSignInType()));
            String childType ="";
            for (String s : agentAttendance.getAuditType().split(",")) {
                if (s.contains(LeaveApplyType.APPOINT.name())){
                    childType = String.join("/", childType, LeaveApplyType.APPOINT.getDesc());
                }else {
                    childType = String.join("/", childType, SignInType.getDescByName(s));
                }
            }
            queryVO.setAudit(AttendanceCheckType.getDescByName(queryVO.getAudit()));
            queryVO.setSignInChildType(childType.replaceFirst("/", ""));
            EmployeeOrgVO employeeOrgVO = employeeOrgVOMap.get(agentAttendance.getEmployeeCode());
            if (employeeOrgVO != null){
                queryVO.setMajordomoCode(StringUtils.isNotEmpty(employeeOrgVO.getBusinessAreaLeaderCode())?employeeOrgVO.getBusinessAreaLeaderCode():"");
                queryVO.setMajordomoName(StringUtils.isNotEmpty(employeeOrgVO.getBusinessAreaLeaderName())?employeeOrgVO.getBusinessAreaLeaderName():"");
                queryVO.setManagerCode(StringUtils.isNotEmpty(employeeOrgVO.getBusinessDeptLeaderCode())?employeeOrgVO.getBusinessDeptLeaderCode():"");
                queryVO.setManagerName(StringUtils.isNotEmpty(employeeOrgVO.getBusinessDeptLeaderName())?employeeOrgVO.getBusinessDeptLeaderName():"");
            }
            //工作计划就是两个字段合在一起
            String lab="";
            if(StringUtils.isNotEmpty( agentAttendance.getBusinessLabel())){
                lab =agentAttendance.getBusinessLabel()+"、";
            }
            if(StringUtils.isNotEmpty( agentAttendance.getActivityLabel())){
                lab += agentAttendance.getActivityLabel();
            }
            queryVO.setBusinessLabel(StringUtils.isNotEmpty( lab) ? lab.substring(0, lab.lastIndexOf("、")) :"");
            queryVO.setDate(agentAttendance.getCheckInTime().format(DateTimeFormatter.ofPattern(DatePatterns.YYYY_MM_DD)));
            queryVO.setCheckInTime(agentAttendance.getCheckInTime().toLocalTime().toString());
            queryVO.setCheckOutTime(agentAttendance.getCheckOutTime() ==  null  ?  null : agentAttendance.getCheckOutTime().toLocalTime().toString());
            if(CollectionUtils.isNotEmpty(attendanceCustomerList)){
                queryVO.setClueData(attendanceCustomerList.size());
                //预估保费
                queryVO.setPremium(attendanceCustomerList.stream().filter(x->x.getEstimatedPremium()!=null).mapToDouble(x -> x.getEstimatedPremium().doubleValue()).sum());
                StringBuilder customer  = new StringBuilder();
                for (AttendanceCustomer attendanceCustomer : attendanceCustomerList) {

                    if(StringUtils.isNotEmpty( attendanceCustomer.getCustomerName())){
                        customer = new StringBuilder(attendanceCustomer.getCustomerName() + "、");
                    }
                    if(StringUtils.isNotEmpty( attendanceCustomer.getCustomerGender())){
                        customer.append(Gender.valueOf(attendanceCustomer.getCustomerGender()).getDesc()).append("、");
                    }
                    if(attendanceCustomer.getEstimatedPremium() != null && attendanceCustomer.getEstimatedPremium().compareTo(BigDecimal.ZERO) != 0){
                        customer.append(attendanceCustomer.getEstimatedPremium()).append("万、");
                    }
                    if(StringUtils.isNotEmpty( attendanceCustomer.getMangerName())){
                        customer.append(attendanceCustomer.getMangerName()).append("、");
                    }
                    if(StringUtils.isNotEmpty( attendanceCustomer.getOtherInfo())){
                        customer.append(attendanceCustomer.getOtherInfo()).append("、");
                    }
                    if(StringUtils.isNotEmpty( attendanceCustomer.getOutlets())){
                        customer.append(attendanceCustomer.getOutlets()).append("、");
                    }
                    if(checkObjAllFieldsIsNull(attendanceCustomer)){
                        customer.append("；");
                    }
                }
                queryVO.setAttendanceCustomers(StringUtils.isNotEmpty(customer.toString()) ? customer.substring(0, customer.lastIndexOf("、")) :"");
            }
            if (CollectionUtils.isNotEmpty(attendanceActivityList)){
                //活动数量
                queryVO.setActivityNum(attendanceActivityList.size());
                //网点会议/活动参与人数
                queryVO.setPeopleNum(CollectionUtils.isNotEmpty(attendanceActivityList) ? attendanceActivityList.stream()
                        .filter(info->info.getNumberOfParticipants() != null)
                        .mapToInt(AttendanceActivity::getNumberOfParticipants)
                        .sum() : 0);

                StringBuilder activity  = new StringBuilder();
                for (AttendanceActivity attendanceActivity : attendanceActivityList) {
                    if(StringUtils.isNotEmpty( attendanceActivity.getLabel())){
                        activity.append(attendanceActivity.getLabel()).append("、");
                    }
                    if(StringUtils.isNotEmpty( attendanceActivity.getExplainTheTopic())){
                        activity.append(attendanceActivity.getExplainTheTopic()).append("、");
                    }
                    if(attendanceActivity.getNumberOfParticipants() != null){
                        activity.append(attendanceActivity.getNumberOfParticipants()).append("人、");
                    }
                    if(StringUtils.isNotEmpty(  attendanceActivity.getActivityOutlets())){
                        activity.append(attendanceActivity.getActivityOutlets()).append("、");
                    }
                    if(StringUtils.isNotEmpty(  attendanceActivity.getDetailRecord())){
                        activity.append(attendanceActivity.getDetailRecord()).append("、");
                    }
                    if(checkObjAllFieldsIsNull(attendanceActivity)){
                        activity.append("；");
                    }

                }
                queryVO.setAttendanceActivities(StringUtils.isNotEmpty(activity) ? activity.substring(0, activity.lastIndexOf("、")) : "");
            }
            queryVOS.add(queryVO);
        }
    }

    private void creatLeavData(List<LeaveApplications> page, ArrayList<AttendanceDetailQueryVO> queryVOS) {
        List<LeaveApplicationsVO> leaveApplicationsVOS = BeanCopier.copyList(page, LeaveApplicationsVO.class);
        for (LeaveApplicationsVO leaveApplication : leaveApplicationsVOS) {
            EmployeeOrgVO employeeOrgVO = employeeOrgApi.queryEmployeeOrgInfo(leaveApplication.getEmployeeCode());
            //处理跨天或半天请假，拆为多条记录
            if (!leaveApplication.getCompanyCode().equals(PermissionType.P00001.name())){
                //非银保按天分跨天按天纬度为一条记录
                List<LocalDateTime> collect = Stream.iterate(leaveApplication.getBeginTime(), date -> date.plus(1, ChronoUnit.DAYS))
                        .limit(ChronoUnit.DAYS.between(leaveApplication.getBeginTime(), leaveApplication.getEndTime()) + 1)
                        .collect(Collectors.toList());
                for (LocalDateTime dateTime : collect) {
                    queryVOS.add(AttendanceDetailQueryVO.builder()
                            .id(leaveApplication.getId())
                            .topName(leaveApplication.getCompanyName())
                            .orgCode(leaveApplication.getCompanyInstCode())
                            .orgName(leaveApplication.getCompanyInstName())
                            .majordomoCode(employeeOrgVO == null || StringUtils.isEmpty(employeeOrgVO.getBusinessAreaLeaderCode()) ? null :employeeOrgVO.getBusinessAreaLeaderCode())
                            .majordomoName(employeeOrgVO == null || StringUtils.isEmpty(employeeOrgVO.getBusinessAreaLeaderName()) ? null :employeeOrgVO.getBusinessAreaLeaderName())
                            .managerCode(employeeOrgVO == null || StringUtils.isEmpty(employeeOrgVO.getBusinessDeptLeaderCode()) ? null :employeeOrgVO.getBusinessDeptLeaderCode())
                            .managerName(employeeOrgVO == null || StringUtils.isEmpty(employeeOrgVO.getBusinessDeptLeaderName()) ? null :employeeOrgVO.getBusinessDeptLeaderName())
                            .employeeCode(leaveApplication.getEmployeeCode())
                            .employeeName(leaveApplication.getEmployeeName())
                            .signInType(LeaveApplyType.LEAVE.getDesc())
                            .signInChildType(LeaveType.getLabelByName(leaveApplication.getLeaveType()))
                            .date(dateTime.format(DateTimeFormatter.ofPattern(DatePatterns.YYYY_MM_DD)))
                            .checkInTime("全天")
                            .audit(AttendanceCheckType.getDescByName(leaveApplication.getAudit()))
                            .auditTime(leaveApplication.getAuditTime())
                            .auditor(leaveApplication.getAuditor()).build());
                }
            }else{
                //银保
                LocalDateTime beginTime = leaveApplication.getBeginTime();
                LocalDateTime endTime = leaveApplication.getEndTime();
                List<LeaveApplicationsVO> leaveApplicationsVOList = AttendanceServiceImpl.decomposeTimeInterval(beginTime, endTime, Double.parseDouble(leaveApplication.getDuration()), leaveApplication);
                for (LeaveApplicationsVO leaveApplicationsVO : leaveApplicationsVOList) {
                    queryVOS.add(AttendanceDetailQueryVO.builder()
                            .id(leaveApplicationsVO.getId())
                            .topName(leaveApplicationsVO.getCompanyName())
                            .orgCode(leaveApplicationsVO.getCompanyInstCode())
                            .orgName(leaveApplicationsVO.getCompanyInstName())
                            .majordomoCode(employeeOrgVO == null || StringUtils.isEmpty(employeeOrgVO.getBusinessAreaLeaderCode()) ? null :employeeOrgVO.getBusinessAreaLeaderCode())
                            .majordomoName(employeeOrgVO == null || StringUtils.isEmpty(employeeOrgVO.getBusinessAreaLeaderName()) ? null :employeeOrgVO.getBusinessAreaLeaderName())
                            .managerCode(employeeOrgVO == null || StringUtils.isEmpty(employeeOrgVO.getBusinessDeptLeaderCode()) ? null :employeeOrgVO.getBusinessDeptLeaderCode())
                            .managerName(employeeOrgVO == null || StringUtils.isEmpty(employeeOrgVO.getBusinessDeptLeaderName()) ? null :employeeOrgVO.getBusinessDeptLeaderName())
                            .employeeCode(leaveApplicationsVO.getEmployeeCode())
                            .employeeName(leaveApplicationsVO.getEmployeeName())
                            .signInType(LeaveApplyType.LEAVE.getDesc())
                            .signInChildType(LeaveType.getLabelByName(leaveApplication.getLeaveType()))
                            .date(leaveApplicationsVO.getBeginTime().format(DateTimeFormatter.ofPattern(DatePatterns.YYYY_MM_DD)))
                            .checkInTime(leaveApplicationsVO.getMorningOrAfternoon() == 0 ? "上午" : "下午" )
                            .audit(AttendanceCheckType.getDescByName(leaveApplication.getAudit()))
                            .auditTime(leaveApplicationsVO.getAuditTime())
                            .auditor(leaveApplicationsVO.getAuditor()).build());
                }

            }
        }
    }

    private String amORpm(LocalDateTime time) {
        if (time.toLocalTime().isBefore(LocalTime.NOON)) {
            return " 上午";
        } else {
            return " 下午";
        }
    }
    public double calculateDays(LocalDate startDate, LocalDate endDate, String hourStart, String hourEnd) {
        double days = 0;
        if (startDate.isAfter(endDate)) {
            days = 0;
        } else if (startDate.isEqual(endDate)) {
            days = 1;
            if (hourStart.equals("上午") && hourEnd.equals("上午")) {
                days -= 0.5;
            } else if (hourStart.equals("下午") && hourEnd.equals("上午")) {
                days -= 1;
            } else if (hourStart.equals("下午") && hourEnd.equals("下午")) {
                days -= 0.5;
            }
        }  else if (startDate != null && endDate != null) {
            days = ChronoUnit.DAYS.between(startDate, endDate);
            if ((hourStart.equals("上午") && hourEnd.equals("上午")) || (hourStart.equals("下午") && hourEnd.equals("下午"))) {
                days += 0.5;
            } else if (hourStart.equals("上午") && hourEnd.equals("下午")) {
                days += 1;
            }
        }
        return days;
    }

    private AuditResponse audit(AuditRequest request) {
        DateTimeFormatter dfDateTime = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LeaveApplications leaveApplications = leaveApplicationsMapper.selectById(request.getId());
        LocalDate localDate = leaveApplications.getBeginTime().toLocalDate();
        AttendanceCheckType type ;
        //通过
        if (FLAG.equals(request.getFlag())){
            //是否跨月跨月拆分不夸就更新
            if (leaveApplications.getBeginTime().getMonth() != leaveApplications.getEndTime().getMonth()){
                //拆分 然后再审核表新增一条新id的量子记录
                leaveApplications.setAudit(AttendanceCheckType.PASS_THE_AUDIT.name());
                leaveApplications.setAuditor(request.getAuditor());
                leaveApplications.setAuditorId(request.getAuditorId());
                leaveApplications.setAuditTime(LocalDateTime.now());
                splitLeaveApplication(leaveApplications,"BG");
            }else {
                leaveApplicationsMapper.updateByPrimaryKeySelective(
                        LeaveApplications.builder()
                                .id(request.getId())
                                .audit(AttendanceCheckType.PASS_THE_AUDIT.name())
                                .auditTime(LocalDateTime.now())
                                .auditor(request.getAuditor())
                                .auditorId(request.getAuditorId())
                                .build());

            }
            type = AttendanceCheckType.PASS_THE_AUDIT;
            //请假通过消息
            messageSave(request.getEmployeeCode(),leaveApplications.getEmployeeName(),"", MessageContentType.LEAVE_PASS_THE_AUDIT,localDate);
        }else{
            leaveApplicationsMapper.updateByPrimaryKeySelective(LeaveApplications.builder()
                    .id(request.getId())
                    .audit(AttendanceCheckType.AUDIT_FAILURE.name())
                    .auditor(request.getAuditor())
                    .auditorId(request.getAuditorId())
                    .auditOutcome(request.getReason())
                    .auditTime(LocalDateTime.now())
                    .build());
            type = AttendanceCheckType.AUDIT_FAILURE;
            //请假不通过消息
            messageSave(request.getEmployeeCode(),leaveApplications.getEmployeeName(),request.getReason(),MessageContentType.LEAVE_AUDIT_FAILURE,localDate);
        }
        return AuditResponse.builder()
                .audit(type.name())
                .auditor(request.getAuditor())
                .auditOutcome(request.getReason())
                .auditTime(dfDateTime.format(LocalDateTime.now()))
                .build();
    }

    public void splitLeaveApplication(LeaveApplications leaveApplications,String orgType) {

        List<LeaveApplications> insertList = new ArrayList<>();
        LeaveApplications update = BeanCopier.copyObject(leaveApplications, LeaveApplications.class);

        LocalDateTime start = leaveApplications.getBeginTime();
        LocalDateTime end = leaveApplications.getEndTime();

        // 计算月数
        Period period = Period.between(start.toLocalDate(), end.toLocalDate());

        // 给出月初到月末的时间点
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime currentMonth = start;

        if (period.getMonths() == 0){
            LocalDateTime monthStart = currentMonth;
            LocalDateTime monthEnd = currentMonth.plusMonths(1).withDayOfMonth(1).minusSeconds(1);
            LocalDateTime monthFirstEnd = currentMonth.getHour() == 12 ?
                    currentMonth.plusMonths(1).withDayOfMonth(1).minusHours(12).minusSeconds(2)   :
                    currentMonth.plusMonths(1).withDayOfMonth(1).minusSeconds(1);
            double firstDays = calculateDays(monthStart.toLocalDate(), monthFirstEnd.toLocalDate(), amORpm(monthStart).trim(), amORpm(monthEnd).trim());
            update.setBeginTime(monthStart);
            update.setEndTime(monthFirstEnd);
            update.setDuration(String.valueOf(firstDays));
            currentMonth = currentMonth.plusMonths(1).withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0);
            log.info("请假拆分id:{}:::"+monthStart.format(formatter) + " 至 " + monthFirstEnd.format(formatter)+"总共"+firstDays+"天",leaveApplications.getId());

        }else {
            for (int i = 0; i < period.getMonths(); i++) {
                LocalDateTime monthStart = currentMonth;
                LocalDateTime monthEnd = currentMonth.plusMonths(1).withDayOfMonth(1).minusSeconds(1);
                double days = calculateDays(monthStart.toLocalDate(), monthEnd.toLocalDate(), amORpm(monthStart).trim(), amORpm(monthEnd).trim());
                if (i == 0){
                    LocalDateTime monthFirstEnd = currentMonth.getHour() == 12 ? currentMonth.plusMonths(1).withDayOfMonth(1).minusHours(12).minusSeconds(2)   : currentMonth.plusMonths(1).withDayOfMonth(1).minusSeconds(1);
                    double firstDays = calculateDays(monthStart.toLocalDate(), monthFirstEnd.toLocalDate(), amORpm(monthStart).trim(), amORpm(monthEnd).trim());
                    update.setBeginTime(monthStart);
                    update.setEndTime(monthFirstEnd);
                    update.setDuration(String.valueOf(firstDays));
                    currentMonth = currentMonth.plusMonths(1).withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0);
                    log.info("请假拆分id:{}:::"+monthStart.format(formatter) + " 至 " + monthFirstEnd.format(formatter)+"总共"+firstDays+"天",leaveApplications.getId());
                    continue;
                }
                log.info("请假拆分id:{}:::"+monthStart.format(formatter) + " 至 " + monthEnd.format(formatter)+"总共"+days+"天",leaveApplications.getId());
                setList(leaveApplications, insertList, monthStart, monthEnd, days);
                currentMonth = currentMonth.plusMonths(1).withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0);
            }

        }



        // 处理最后一个月
        LocalDateTime checkLastMonthStart = currentMonth.withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0);
        if (checkLastMonthStart.getMonthValue() != end.getMonthValue()){
            LocalDateTime time = checkLastMonthStart.plusMonths(1).withDayOfMonth(1).minusSeconds(1);
            double checkDays = calculateDays(checkLastMonthStart.toLocalDate(), time.toLocalDate(), amORpm(checkLastMonthStart).trim(), amORpm(time).trim());
            setList(leaveApplications, insertList, checkLastMonthStart, time, checkDays);
            log.info("请假拆分id:{}:::最后一个记录"+checkLastMonthStart.format(formatter) + " 至 " + time.format(formatter)+"总共"+checkDays+"天",leaveApplications.getId());
            currentMonth = currentMonth.plusMonths(1).withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0);
        }

        LocalDateTime lastMonthStart = currentMonth.withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0);
        double lastDays = calculateDays(lastMonthStart.toLocalDate(), end.toLocalDate(), amORpm(lastMonthStart).trim(), amORpm(end).trim());
        setList(leaveApplications, insertList, lastMonthStart, end, lastDays);
        log.info("请假拆分id:{}:::最后一个记录"+lastMonthStart.format(formatter) + " 至 " + end.format(formatter)+"总共"+lastDays+"天",leaveApplications.getId());

        leaveApplicationsMapper.updateByPrimaryKeySelective(update);
        leaveApplicationsMapper.insertBatchSomeColumn(insertList);

        if (orgType.equals("BG")){
            List<RepairLeaveApproval> repairLeaveApprovalList = repairLeaveApprovalMapper.selectList(Wrappers.lambdaQuery(RepairLeaveApproval.class)
                    .eq(RepairLeaveApproval::getLeaveId, leaveApplications.getId())
                    .eq(RepairLeaveApproval::getApplicationType, LeaveApplyType.LEAVE));

            List<RepairLeaveApproval> repairLeaveApprovalArrayList = new ArrayList<>();
            for (LeaveApplications applications : insertList) {
                for (RepairLeaveApproval repairLeaveApproval : repairLeaveApprovalList) {
                    RepairLeaveApproval copyObject = BeanCopier.copyObject(repairLeaveApproval, RepairLeaveApproval.class);
                    copyObject.setLeaveId(applications.getId());
                    repairLeaveApprovalArrayList.add(copyObject);
                }
            }

            repairLeaveApprovalMapper.insertBatchSomeColumn(repairLeaveApprovalArrayList);
        }
    }

    private void setList(LeaveApplications leaveApplications, List<LeaveApplications> insertList, LocalDateTime monthStart, LocalDateTime monthEnd, double days) {
        LeaveApplications copyObject = BeanCopier.copyObject(leaveApplications, LeaveApplications.class);
        copyObject.setBeginTime(monthStart);
        copyObject.setEndTime(monthEnd);
        copyObject.setDuration(String.valueOf(days));
        copyObject.setId(null);

        insertList.add(copyObject);
    }

    /**
     * 使用反射机制将任意对象的字段映射到OAFormDataVo列表中
     * @param sourceObject 要映射的源对象
     * @param oaFormDataVos 用于存放OAFormDataVo对象的目标列表
     * @param excludeFields 需要排除映射的字段名（可选参数）
     */
    public static void mapObjectToOaFormDataVos(Object sourceObject,
                                                List<OAFormDataVo> oaFormDataVos,
                                                String... excludeFields) {
        // 检查源对象和目标列表是否为null
        if (sourceObject == null || oaFormDataVos == null) {
            return;  // 如果任一参数为null则直接返回
        }

        try {
            Class<?> clazz = sourceObject.getClass();
            Field[] fields = clazz.getDeclaredFields();

            // 遍历所有字段
            for (Field field : fields) {
                // 跳过被排除的字段
                if (isExcluded(field.getName(), excludeFields)) {
                    continue;
                }
                field.setAccessible(true);
                Object value = field.get(sourceObject);
                // 只处理非null的字段值
                if (value != null) {
                    String fieldName = switchFieldName(field.getName());
                    if (StringUtils.isNotEmpty(fieldName)) {
                        // 创建OAFormDataVo对象并添加到列表
                        oaFormDataVos.add(
                                OAFormDataVo.builder()
                                        .fieldName(fieldName)
                                        .fieldvalue(String.valueOf(value))
                                        .build());
                    }

                }
            }
        } catch (IllegalAccessException e) {
            log.info("使用反射映射对象字段时出错", e);
        }
    }

    private static String switchFieldName(String name) {
        switch (name) {
            case "title": return "bt";
            case "employeeName": return "xm";
            case "team": return "sztd";
//            case "sqlx": return "sqlx";
//            case "bmdm": return "bmdm";
            case "topName": return "sqrszjg";
            case "appointDate": return "bksj";
            case "appointAddress": return "bkdd";
            case "appointDetail": return "bkyy";
            case "surplusAppointCount": return "bysybkcs";
            case "leaveDate": return "qjsj";
            case "leaveDetail": return "qjsy";
//            case "jqtz": return "jqtz";
            case "sumLeaveCount": return "byqjqk";
            case "applyDate": return "lctjsj";
            case "revokeDate": return "xjsj";
            case "revokeDetail": return "xjsy";
            default: return null;
        }
    }

    private static boolean isExcluded(String fieldName, String... excludeFields) {
        if (excludeFields == null) {
            return false;
        }
        for (String excluded : excludeFields) {
            if (fieldName.equals(excluded)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 漏签退定时任务
     */
    public void missedSignatureMark(String orgCode) {
        LocalDate yinBaoStartDate = attendanceSignInImageConfig.getYinBaoStartTime().toInstant().atZone(ZoneId.of("UTC")).toLocalDate();
        LocalDate localDate = LocalDate.now().minusDays(1);
        List<AgentAttendance> agentAttendanceList = agentAttendanceMapper.selectList(Wrappers.lambdaQuery(AgentAttendance.class)
                .eq(AgentAttendance::getTopCode,PermissionType.P00001.name())
                .isNull(AgentAttendance::getCheckOutTime)
                .ge(AgentAttendance::getCheckInTime, yinBaoStartDate + " 00:00:00")
                .ge(AgentAttendance::getCheckInTime, localDate + " 00:00:00")
                .le(AgentAttendance::getCheckInTime, localDate + " 23:59:59"));
        if (CollectionUtils.isNotEmpty(agentAttendanceList)){
            for (AgentAttendance agentAttendance : agentAttendanceList) {
                String auditType = agentAttendance.getAuditType();
                if (!auditType.contains( SignInType.MISSED_SIGNING.name())){
                    //将auditType中的SIGN_IN去掉
                    if (auditType.contains(SignInType.SIGN_IN.name())){
                        auditType = auditType.replace(SignInType.SIGN_IN.name(), SignInType.MISSED_SIGNING.name());
                    }else{
                        auditType = auditType+","+SignInType.MISSED_SIGNING.name();
                    }
                    agentAttendance.setAuditType(auditType);
                    agentAttendanceMapper.updateById(agentAttendance);
                }
            }
        }

    }
}
