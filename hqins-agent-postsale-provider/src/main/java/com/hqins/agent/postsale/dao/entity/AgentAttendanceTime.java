package com.hqins.agent.postsale.dao.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.*;

import java.time.LocalDateTime;
import java.io.Serializable;

/**
 * 打卡时间配置
 *
 * <AUTHOR>
 * @since 2025-05-06
 */
@TableName("agent_attendance_time")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AgentAttendanceTime implements Serializable {


    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 归属合伙人组织
     */
    private String orgCode;

    /**
     * 归属合伙人机构
     */
    private String orgIns;

    /**
     * 是否限制打卡时间
     */
    @TableField(value = "permissions")
    private Boolean permissions;

    /**
     * 签到时间
     */
    private String signInTime;

    /**
     * EARLY / LATE
     */
    private String signInType;

    /**
     * 签退时间
     */
    private String signOutTime;

    /**
     * EARLY / LATE
     */
    private String signOutType;

    /**
     * 签到 签退，最小时间间隔
     */
    private Integer timeMinInterval;

    /**
     * 插入时间
     */
    private LocalDateTime createdTime;

    /**
     * 更新人
     */
    private String updatedUser;

    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;

    /**
     * 创建人
     */
    private String createdUser;

    /**
     * 年份
     */
    private String years;

    /**
     * 1-12个
     */
    private String monthJson;

    /**
     * 银保日期出勤天数json
     */
    private String ybMonthJson;

    /**
     * 补卡限制时长
     */
    private Integer signLimit;

    /**
     * 上午开始时间
     */
    private String mStartTime;

    /**
     * 上午结束时间
     */
    private String mEndTime;

    /**
     * 下午开始时间
     */
    private String aStartTime;

    /**
     * 下午开始时间
     */
    private String aEndTime;

    /**
     * 客户经理打卡次数
     */
    private Integer agentClockCount;

    /**
     * 主管打卡次数
     */
    private Integer leaderClockCount;

    /**
     * 客户经理补卡次数
     */
    private Integer agentRepairCount;

    /**
     * 主管补卡次数
     */
    private Integer leaderRepairCount;

    /**
     * 是否审查
     */
    @TableField(value = "is_audit")
    private Boolean audit;

    /**
     * 音标规则标识
     */
    @TableField(exist = false)
    private Boolean ybFlag;
}
