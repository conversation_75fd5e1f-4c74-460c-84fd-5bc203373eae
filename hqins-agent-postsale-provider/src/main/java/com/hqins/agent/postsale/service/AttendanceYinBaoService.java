package com.hqins.agent.postsale.service;

import com.hqins.agent.postsale.dao.entity.AgentAttendance;
import com.hqins.agent.postsale.dto.request.AttendanceSignInRequest;
import com.hqins.agent.postsale.dto.request.CheckOutRequest;
import com.hqins.agent.postsale.dto.request.DataStatisticsRequest;
import com.hqins.agent.postsale.dto.request.ReYinBaoSignRequest;
import com.hqins.agent.postsale.dto.response.AttendanceListResponse;
import com.hqins.agent.postsale.dto.response.DataStatisticsResponse;
import com.hqins.agent.postsale.dto.response.SignOutResponse;

public interface AttendanceYinBaoService {
    DataStatisticsResponse dataStatisticsYinBao(DataStatisticsRequest request);

    boolean supplement(ReYinBaoSignRequest request);

    Long signIn(AttendanceSignInRequest request);

    SignOutResponse signOut(CheckOutRequest request);

    AttendanceListResponse dataDay(String orgCode);

    void rule(String id, String companyInsCode);

    AgentAttendance noSignOut();

    Integer querySupplementCount(DataStatisticsRequest request);
}
