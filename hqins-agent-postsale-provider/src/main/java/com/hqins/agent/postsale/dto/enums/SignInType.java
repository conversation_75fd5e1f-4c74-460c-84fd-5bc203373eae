package com.hqins.agent.postsale.dto.enums;

import io.swagger.annotations.ApiModel;

@ApiModel("签到类型")
public enum SignInType {
    //签到
    SIGN_IN("正常"),
    NON_SPECIFIED("非指定地点打卡"),
    SIGN_OUT("签退"),
    APPOINT_HALF("补签-半天"),
    APPOINT_ALL("补签-全天"),
    //无地点签到,超出范围签到
    NO_ADDRESS("定位异常打卡"),    //NON_SPECIFIED,NO_ADDRESS,TIME_ERROR
    NON_ASSIGNED("非分派网点"),
    ABSENTEE("未出勤"),
    MISSED_SIGNING("漏签退"),
    TIME_ERROR("时间异常"),
    ;

    private String desc;

    SignInType(String desc) {
        this.desc = desc;
    }
    public String getDesc() {
        return desc;
    }
    public static boolean isValid(String name) {
        return get(name) != null;
    }

    private static SignInType get(String name) {
        for (SignInType value : SignInType.values()) {
            if(value.name().equals(name)) {
                return value;
            }
        }
        return null;
    }
    public static String getDescByName(String name) {
        SignInType signInType = get(name);
        if (signInType != null) {
            return signInType.getDesc();
        }
        return "";
    }
}
