package com.hqins.agent.postsale.dto.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("打卡配置对象")
public class DataStatisticsTimerVO implements Serializable {
    @ApiModelProperty("客户经理打卡次数")
    private Integer agentClockCount;

    @ApiModelProperty("主管打卡次数")
    private Integer leaderClockCount;

    @ApiModelProperty("上午开始时间")
    private String mStartTime;

    @ApiModelProperty("上午结束时间")
    private String mEndTime;

    @ApiModelProperty("下午开始时间")
    private String aStartTime;

    @ApiModelProperty("下午结束时间")
    private String aEndTime;

}
