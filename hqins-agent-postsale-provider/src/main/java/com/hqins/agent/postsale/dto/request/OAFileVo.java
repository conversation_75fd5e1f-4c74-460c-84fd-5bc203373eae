package com.hqins.agent.postsale.dto.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OAFileVo implements Serializable {

    @ApiModelProperty(value = "名称")
    public String name;

    @ApiModelProperty(value = "链接")
    public String path;

    @ApiModelProperty(value = "大小")
    public String size;

}