package com.hqins.agent.postsale.web.controller;

import cn.hutool.json.JSONUtil;
import com.hqins.agent.postsale.config.AttendanceObjConfig;
import com.hqins.agent.postsale.dao.entity.AgentAttendance;
import com.hqins.agent.postsale.dto.request.*;
import com.hqins.agent.postsale.dto.response.*;
import com.hqins.agent.postsale.service.AttendanceService;
import com.hqins.common.base.ApiResult;
import com.hqins.common.base.errors.BadRequestException;
import com.hqins.common.web.RequestContextHolder;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Map;

/**
 * 出勤控制器
 * <AUTHOR>
 */
@Slf4j
@RestController
@Api(tags = "出勤系统控制器（老银保+个团）")
@RequestMapping("/attendance")
public class AttendanceController {
    private final AttendanceService attendanceService;

    public AttendanceController(AttendanceService attendanceService) {
        this.attendanceService = attendanceService;
    }

    @ApiOperation("获取下拉数据")
    @GetMapping("/drop-down")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<DropDownResponse> dropDown(@RequestParam(value = "longitude",required = false,defaultValue ="113.27481" ) String longitude,
                                                @RequestParam(value = "latitude",required = false,defaultValue = "22.994511")String latitude) {
        return ApiResult.ok(attendanceService.dropDown(longitude,latitude));
    }


    @ApiOperation("获取下拉数据二级")
    @PostMapping("/down-child")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<DropDownResponse> downChild(@RequestBody LocationCodeRequest request) {
        return ApiResult.ok(attendanceService.downChild(request));
    }

    @ApiOperation("工作计划保存")
    @PostMapping("/job-date")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<Void> jobDate(@RequestBody CheckOutRequest request) {
        attendanceService.jobDate(request);
        return ApiResult.ok();
    }

    @ApiOperation("查询签到状态")
    @GetMapping("/type")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<AgentAttendance> getSignInType(@RequestParam String locationCode) {
        List<AgentAttendance> agentAttendances = attendanceService.attendanceType(locationCode);
        if (CollectionUtils.isEmpty(agentAttendances)){
            return null;
        }
        return ApiResult.ok(agentAttendances.get(0));
    }

    @ApiOperation("影像水印")
    @PostMapping("/image-watermark")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<List<AttendanceImageResponse>> imageWatermark(@RequestBody List<AttendanceImageRequest> request) {
        return ApiResult.ok(attendanceService.imageWatermark(request));
    }

    @ApiOperation("签到")
    @PostMapping("/sign-in")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<Long> signIn(@RequestBody AttendanceSignInRequest request) {
        log.info("attendance signIn 入参 {} ,employeeCode:{} ", JSONUtil.toJsonStr(request),RequestContextHolder.getEmployeeCode());
        return ApiResult.ok(attendanceService.signIn(request));
    }

    @ApiOperation("签退")
    @PostMapping("/sign-out")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<SignOutResponse> signOut(@RequestBody CheckOutRequest request) {
        log.info("attendance signOut 入参 {} ,employeeCode:{} ", JSONUtil.toJsonStr(request),RequestContextHolder.getEmployeeCode());
        return ApiResult.ok(attendanceService.signOut(request));
    }

    @ApiOperation("查询广告位数据")
    @GetMapping("/advertising")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<Map<String, AttendanceObjConfig>> advertising() {
        return ApiResult.ok(attendanceService.advertising());
    }

    @ApiOperation("查询打卡数据统计")
    @PostMapping("/data-statistics")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<DataStatisticsResponse> dataStatistics(@RequestBody DataStatisticsRequest request) {
        return ApiResult.ok(attendanceService.daKaDataStatistics(request));
    }

    @ApiOperation("查询当日打卡数据")
    @GetMapping("/data-day")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<AttendanceListResponse> getDaySing(@RequestParam("orgTypeStr") String orgTypeStr,@RequestParam("orgCode") String orgCode) {
        return ApiResult.ok(attendanceService.getDaySing(orgTypeStr,orgCode));
    }

    @ApiOperation("查询补卡次数")
    @PostMapping("/supplement-count")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<Integer> querySupplementCount(@RequestBody DataStatisticsRequest request) {
        return ApiResult.ok(attendanceService.querySupplementCount(request));
    }

    @ApiOperation("批量补卡申请")
    @PostMapping("/supplement-batch")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<Integer> supplementBatch(@RequestBody List<ReSignRequest> request) {
        log.info("attendance supplement 入参 {} ,employee:{} ", JSONUtil.toJsonStr(request),RequestContextHolder.getEmployeeCode());
        attendanceService.supplementBranch(request);
        return ApiResult.ok(0);
    }

    @ApiOperation("补卡申请")
    @PostMapping("/supplement")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<Integer> supplement(@RequestBody ReSignRequest request) {
        log.info("attendance supplement 入参 {} ,employee:{} ", JSONUtil.toJsonStr(request),RequestContextHolder.getEmployeeCode());
        return ApiResult.ok(attendanceService.supplement(request));
    }


    @ApiOperation("查询客户活动信息")
    @PostMapping("/customer/query")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<LogInfoResponse> customerQuery(@RequestBody CustomerQueryRequest request) {
        return ApiResult.ok(attendanceService.customerQuery(request));
    }

    @ApiOperation("更新客户活动信息接口")
    @PostMapping("/customer/update")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<Void> customerUpdate(@RequestBody LogInfoUpdateRequest request) {
        attendanceService.customerUpdate(request);
        return ApiResult.ok();
    }

    @ApiOperation("查询系统时间")
    @GetMapping("/check/time")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<Void> checkTime(@RequestParam(value = "id",required = false) String id,
                                     @RequestParam(value = "companyInsCode",required = false) String companyInsCode,
                                     @RequestParam(value = "orgTypeStr") String orgTypeStr
                                     ) {
        attendanceService.checkTime(id,companyInsCode,orgTypeStr);
        return ApiResult.ok();
    }

    @ApiOperation("查询微信临时图片")
    @PostMapping("/wx/temporary")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<List<AttendanceImageResponse> > temporary(@RequestBody  List<AttendanceImageRequest> request) {
        return ApiResult.ok(attendanceService.temporary(request));
    }

    @ApiOperation("服务器时间")
    @GetMapping("/system-time")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<Void> systemTime(@RequestParam String time) {
        //判断入参的time和服务器时间，精确到分
        time = time +":00";
        //time转localdatetime
        final LocalDateTime localDateTime = LocalDateTime.parse(time, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        validateTime(localDateTime.getYear(),localDateTime.getMonthValue(),localDateTime.getDayOfMonth(),localDateTime.getHour(),localDateTime.getMinute());
        return ApiResult.ok();
    }

    public  void validateTime(int year, int month, int day, int hour, int minute) {
        LocalDateTime clientTime = LocalDateTime.of(year, month, day, hour, minute);
        LocalDateTime systemTime = LocalDateTime.now();
        long minutesDifference = ChronoUnit.MINUTES.between(clientTime, systemTime);

        if (Math.abs(minutesDifference) > 2 || Math.abs(minutesDifference)<-2) {
            throw new BadRequestException("当前手机时间有误，请调整为正确时间");
        }
    }
}
