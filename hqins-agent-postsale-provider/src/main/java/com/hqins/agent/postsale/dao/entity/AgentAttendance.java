package com.hqins.agent.postsale.dao.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.*;

import java.time.LocalDateTime;
import java.io.Serializable;

/**
 * 人员出勤表
 *
 * <AUTHOR>
 * @since 2023-11-16
 */
@TableName("agent_attendance")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AgentAttendance implements Serializable {


    /**
     * 
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 工号
     */
    private String employeeCode;

    /**
     * 人员姓名
     */
    private String employeeName;

    /**
     * 合伙人机构 归属合伙人组织
     */
    private String companyName;

    /**
     * 公司代码 归属合伙人组织code
     */
    private String companyCode;

    /**
     * 渠道类型
     */
    private String orgType;

    /**
     * 归属合伙人机构
     */
    private String orgCode;

    /**
     * 归属合伙人机构name
     */
    private String orgName;

    /**
     * 打卡地点归属渠道商
     */
    private String topCode;

    /**
     * 归属名称
     */
    private String topName;

    /**
     * 打卡地点类型(COMPANY("公司"), MERCHANT("渠道商"))
     */
    private String addressType;

    /**
     * 打卡地点
     */
    private String locationCode;

    /**
     * 打卡地点名称
     */
    private String locationName;

    /**
     * 打卡类型 指定/非指定
     */
    private String signInType;

    /**
     * 审核情况：审核成功/审核不通过/待审核/自动审核通过（默认）
     */
    private String audit;

    /**
     * 审核人name
     */
    private String auditor;

    /**
     * 审核人id
     */
    private String auditorId;

    /**
     * 审核类型 补签/非分派网点/未出勤
     */
    private String auditType;

    /**
     * 审核时间
     */
    private LocalDateTime auditTime;

    /**
     * 审核申请原因
     */
    private String auditRemark;

    /**
     * 审核不通过原因
     */
    private String auditOutcome;

    /**
     * 签到时间
     */
    private LocalDateTime checkInTime;

    /**
     * 签退时间
     */
    private LocalDateTime checkOutTime;

    /**
     * 经度
     */
    private String longitude;

    /**
     * 纬度
     */
    private String latitude;

    /**
     * 签退经度
     */
    private String outLongitude;

    /**
     * 签退维度
     */
    private String outLatitude;

    /**
     * 网点经营，逗号分隔
     */
    private String businessLabel;

    /**
     * 网点会议活动
     */
    private String activityLabel;

    /**
     * 抽查状态，抽查和未抽查
     */
    private String spotCheckType;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 是否删除
     */
    @TableField(fill = FieldFill.INSERT, value = "is_deleted")
    private Boolean deleted;

    /**
     * 是否认证 TRUE认证
     */
    @TableField(value = "authentication")
    private Boolean authentication;

    /**
     * 审核人工号
     */
    private String reviewersCode;

    /**
     * 上下午标识
     */
    @TableField(exist = false)
    private String period;
}
