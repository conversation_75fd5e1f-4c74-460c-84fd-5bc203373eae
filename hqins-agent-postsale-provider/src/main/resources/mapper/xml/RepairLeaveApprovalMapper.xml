<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hqins.agent.postsale.dao.mapper.RepairLeaveApprovalMapper">

    <resultMap id="Base_Result_Map" type="com.hqins.agent.postsale.dao.entity.RepairLeaveApproval">
            <result property="id" column="id"/>
            <result property="applicationType" column="application_type"/>
            <result property="sub" column="is_sub"/>
            <result property="leaveId" column="leave_id"/>
            <result property="batchId" column="batch_id"/>
            <result property="leaveType" column="leave_type"/>
            <result property="leaveCode" column="leave_code"/>
            <result property="oaType" column="oa_type"/>
            <result property="approvalCode" column="approval_code"/>
            <result property="createdTime" column="created_time"/>
            <result property="createdUser" column="created_user"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,application_type,is_sub,leave_id,batch_id,leave_type,leave_code,oa_type,approval_code,created_time,created_user
    </sql>

    <insert id="insertSelective" parameterType="com.hqins.agent.postsale.dao.entity.RepairLeaveApproval"
            useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        insert into repair_leave_approval
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="applicationType != null">
                application_type,
            </if>
            <if test="sub != null">
                is_sub,
            </if>
            <if test="leaveId != null">
                leave_id,
            </if>
            <if test="batchId != null">
                batch_id,
            </if>
            <if test="leaveType != null">
                leave_type,
            </if>
            <if test="leaveCode != null">
                leave_code,
            </if>
            <if test="oaType != null">
                oa_type,
            </if>
            <if test="approvalCode != null">
                approval_code,
            </if>
            <if test="createdTime != null">
                created_time,
            </if>
            <if test="createdUser != null">
                created_user,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="applicationType != null">
                #{applicationType,jdbcType=VARCHAR},
            </if>
            <if test="sub != null">
                #{sub,jdbcType=BIT},
            </if>
            <if test="leaveId != null">
                #{leaveId,jdbcType=BIGINT},
            </if>
            <if test="batchId != null">
                #{batchId,jdbcType=VARCHAR},
            </if>
            <if test="leaveType != null">
                #{leaveType,jdbcType=VARCHAR},
            </if>
            <if test="leaveCode != null">
                #{leaveCode,jdbcType=VARCHAR},
            </if>
            <if test="oaType != null">
                #{oaType,jdbcType=BIT},
            </if>
            <if test="approvalCode != null">
                #{approvalCode,jdbcType=VARCHAR},
            </if>
            <if test="createdTime != null">
                #{createdTime,jdbcType=TIME},
            </if>
            <if test="createdUser != null">
                #{createdUser,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.hqins.agent.postsale.dao.entity.RepairLeaveApproval">
        update repair_leave_approval
        <set>
            <if test="applicationType != null">
                application_type = #{applicationType,jdbcType=VARCHAR},
            </if>
            <if test="sub != null">
                is_sub = #{sub,jdbcType=BIT},
            </if>
            <if test="leaveId != null">
                leave_id = #{leaveId,jdbcType=BIGINT},
            </if>
            <if test="batchId != null">
                batch_id = #{batchId,jdbcType=VARCHAR},
            </if>
            <if test="leaveType != null">
                leave_type = #{leaveType,jdbcType=VARCHAR},
            </if>
            <if test="leaveCode != null">
                leave_code = #{leaveCode,jdbcType=VARCHAR},
            </if>
            <if test="oaType != null">
                oa_type = #{oaType,jdbcType=BIT},
            </if>
            <if test="approvalCode != null">
                approval_code = #{approvalCode,jdbcType=VARCHAR},
            </if>
            <if test="createdTime != null">
                created_time = #{createdTime,jdbcType=TIME},
            </if>
            <if test="createdUser != null">
                created_user = #{createdUser,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByLeaveId">
        UPDATE repair_leave_approval
        SET
            LEAVE_TYPE= #{entity.leaveType},
        is_sub = false
        WHERE LEAVE_ID = #{entity.leaveId} and  application_type =#{entity.applicationType}
    </update>
    <select id="selectNoMeList" resultType="com.hqins.agent.postsale.dao.entity.RepairLeaveApproval">
        SELECT
        <include refid='Base_Column_List'/>
        FROM (
        SELECT *,
        ROW_NUMBER() OVER (PARTITION BY leave_id ORDER BY created_time DESC) as rn
        FROM repair_leave_approval
        WHERE leave_type != 'TO_BE_REVIEWED'
        AND leave_id IN (
        SELECT leave_id
        FROM repair_leave_approval
        WHERE approval_code = #{employeeCode} AND is_sub = false AND leave_type = #{type}
        )
        ) as subquery
        WHERE subquery.rn = 1
        ORDER BY created_time DESC;
    </select>
    <select id="selectByLeaveId" resultType="java.lang.Long">
        select id from repair_leave_approval where leave_id=#{id} and application_type = #{type} and  created_user ='1' order by created_time desc limit 1
    </select>


</mapper>
