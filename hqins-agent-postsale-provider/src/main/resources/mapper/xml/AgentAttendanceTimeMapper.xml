<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hqins.agent.postsale.dao.mapper.AgentAttendanceTimeMapper">

    <resultMap id="Base_Result_Map" type="com.hqins.agent.postsale.dao.entity.AgentAttendanceTime">
            <result property="id" column="id"/>
            <result property="orgCode" column="org_code"/>
            <result property="orgIns" column="org_ins"/>
            <result property="permissions" column="permissions"/>
            <result property="signInTime" column="sign_in_time"/>
            <result property="signInType" column="sign_in_type"/>
            <result property="signOutTime" column="sign_out_time"/>
            <result property="signOutType" column="sign_out_type"/>
            <result property="timeMinInterval" column="time_min_interval"/>
            <result property="createdTime" column="created_time"/>
            <result property="updatedUser" column="updated_user"/>
            <result property="updatedTime" column="updated_time"/>
            <result property="createdUser" column="created_user"/>
            <result property="years" column="years"/>
            <result property="monthJson" column="month_json"/>
            <result property="ybMonthJson" column="yb_month_json"/>
            <result property="signLimit" column="sign_limit"/>
            <result property="mStartTime" column="m_start_time"/>
            <result property="mEndTime" column="m_end_time"/>
            <result property="aStartTime" column="a_start_time"/>
            <result property="aEndTime" column="a_end_time"/>
            <result property="agentClockCount" column="agent_clock_count"/>
            <result property="leaderClockCount" column="leader_clock_count"/>
            <result property="agentRepairCount" column="agent_repair_count"/>
            <result property="leaderRepairCount" column="leader_repair_count"/>
            <result property="audit" column="is_audit"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,org_code,org_ins,permissions,sign_in_time,sign_in_type,sign_out_time,sign_out_type,time_min_interval,created_time,updated_user,updated_time,created_user,years,month_json,yb_month_json,sign_limit,m_start_time,m_end_time,a_start_time,a_end_time,agent_clock_count,leader_clock_count,agent_repair_count,leader_repair_count,is_audit
    </sql>

    <insert id="insertSelective" parameterType="com.hqins.agent.postsale.dao.entity.AgentAttendanceTime"
            useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        insert into agent_attendance_time
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orgCode != null">
                org_code,
            </if>
            <if test="orgIns != null">
                org_ins,
            </if>
            <if test="permissions != null">
                permissions,
            </if>
            <if test="signInTime != null">
                sign_in_time,
            </if>
            <if test="signInType != null">
                sign_in_type,
            </if>
            <if test="signOutTime != null">
                sign_out_time,
            </if>
            <if test="signOutType != null">
                sign_out_type,
            </if>
            <if test="timeMinInterval != null">
                time_min_interval,
            </if>
            <if test="createdTime != null">
                created_time,
            </if>
            <if test="updatedUser != null">
                updated_user,
            </if>
            <if test="updatedTime != null">
                updated_time,
            </if>
            <if test="createdUser != null">
                created_user,
            </if>
            <if test="years != null">
                years,
            </if>
            <if test="monthJson != null">
                month_json,
            </if>
            <if test="ybMonthJson != null">
                yb_month_json,
            </if>
            <if test="signLimit != null">
                sign_limit,
            </if>
            <if test="mStartTime != null">
                m_start_time,
            </if>
            <if test="mEndTime != null">
                m_end_time,
            </if>
            <if test="aStartTime != null">
                a_start_time,
            </if>
            <if test="aEndTime != null">
                a_end_time,
            </if>
            <if test="agentClockCount != null">
                agent_clock_count,
            </if>
            <if test="leaderClockCount != null">
                leader_clock_count,
            </if>
            <if test="agentRepairCount != null">
                agent_repair_count,
            </if>
            <if test="leaderRepairCount != null">
                leader_repair_count,
            </if>
            <if test="audit != null">
                is_audit,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orgCode != null">
                #{orgCode,jdbcType=VARCHAR},
            </if>
            <if test="orgIns != null">
                #{orgIns,jdbcType=VARCHAR},
            </if>
            <if test="permissions != null">
                #{permissions,jdbcType=BIT},
            </if>
            <if test="signInTime != null">
                #{signInTime,jdbcType=VARCHAR},
            </if>
            <if test="signInType != null">
                #{signInType,jdbcType=VARCHAR},
            </if>
            <if test="signOutTime != null">
                #{signOutTime,jdbcType=VARCHAR},
            </if>
            <if test="signOutType != null">
                #{signOutType,jdbcType=VARCHAR},
            </if>
            <if test="timeMinInterval != null">
                #{timeMinInterval,jdbcType=INTEGER},
            </if>
            <if test="createdTime != null">
                #{createdTime,jdbcType=TIME},
            </if>
            <if test="updatedUser != null">
                #{updatedUser,jdbcType=VARCHAR},
            </if>
            <if test="updatedTime != null">
                #{updatedTime,jdbcType=TIME},
            </if>
            <if test="createdUser != null">
                #{createdUser,jdbcType=VARCHAR},
            </if>
            <if test="years != null">
                #{years,jdbcType=VARCHAR},
            </if>
            <if test="monthJson != null">
                #{monthJson,jdbcType=VARCHAR},
            </if>
            <if test="ybMonthJson != null">
                #{ybMonthJson,jdbcType=VARCHAR},
            </if>
            <if test="signLimit != null">
                #{signLimit,jdbcType=INTEGER},
            </if>
            <if test="mStartTime != null">
                #{mStartTime,jdbcType=VARCHAR},
            </if>
            <if test="mEndTime != null">
                #{mEndTime,jdbcType=VARCHAR},
            </if>
            <if test="aStartTime != null">
                #{aStartTime,jdbcType=VARCHAR},
            </if>
            <if test="aEndTime != null">
                #{aEndTime,jdbcType=VARCHAR},
            </if>
            <if test="agentClockCount != null">
                #{agentClockCount,jdbcType=INTEGER},
            </if>
            <if test="leaderClockCount != null">
                #{leaderClockCount,jdbcType=INTEGER},
            </if>
            <if test="agentRepairCount != null">
                #{agentRepairCount,jdbcType=INTEGER},
            </if>
            <if test="leaderRepairCount != null">
                #{leaderRepairCount,jdbcType=INTEGER},
            </if>
            <if test="audit != null">
                #{audit,jdbcType=BIT},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.hqins.agent.postsale.dao.entity.AgentAttendanceTime">
        update agent_attendance_time
        <set>
            <if test="orgCode != null">
                org_code = #{orgCode,jdbcType=VARCHAR},
            </if>
            <if test="orgIns != null">
                org_ins = #{orgIns,jdbcType=VARCHAR},
            </if>
            <if test="permissions != null">
                permissions = #{permissions,jdbcType=BIT},
            </if>
            <if test="signInTime != null">
                sign_in_time = #{signInTime,jdbcType=VARCHAR},
            </if>
            <if test="signInType != null">
                sign_in_type = #{signInType,jdbcType=VARCHAR},
            </if>
            <if test="signOutTime != null">
                sign_out_time = #{signOutTime,jdbcType=VARCHAR},
            </if>
            <if test="signOutType != null">
                sign_out_type = #{signOutType,jdbcType=VARCHAR},
            </if>
            <if test="timeMinInterval != null">
                time_min_interval = #{timeMinInterval,jdbcType=INTEGER},
            </if>
            <if test="createdTime != null">
                created_time = #{createdTime,jdbcType=TIME},
            </if>
            <if test="updatedUser != null">
                updated_user = #{updatedUser,jdbcType=VARCHAR},
            </if>
            <if test="updatedTime != null">
                updated_time = #{updatedTime,jdbcType=TIME},
            </if>
            <if test="createdUser != null">
                created_user = #{createdUser,jdbcType=VARCHAR},
            </if>
            <if test="years != null">
                years = #{years,jdbcType=VARCHAR},
            </if>
            <if test="monthJson != null">
                month_json = #{monthJson,jdbcType=VARCHAR},
            </if>
            <if test="ybMonthJson != null">
                yb_month_json = #{ybMonthJson,jdbcType=VARCHAR},
            </if>
            <if test="signLimit != null">
                sign_limit = #{signLimit,jdbcType=INTEGER},
            </if>
            <if test="mStartTime != null">
                m_start_time = #{mStartTime,jdbcType=VARCHAR},
            </if>
            <if test="mEndTime != null">
                m_end_time = #{mEndTime,jdbcType=VARCHAR},
            </if>
            <if test="aStartTime != null">
                a_start_time = #{aStartTime,jdbcType=VARCHAR},
            </if>
            <if test="aEndTime != null">
                a_end_time = #{aEndTime,jdbcType=VARCHAR},
            </if>
            <if test="agentClockCount != null">
                agent_clock_count = #{agentClockCount,jdbcType=INTEGER},
            </if>
            <if test="leaderClockCount != null">
                leader_clock_count = #{leaderClockCount,jdbcType=INTEGER},
            </if>
            <if test="agentRepairCount != null">
                agent_repair_count = #{agentRepairCount,jdbcType=INTEGER},
            </if>
            <if test="leaderRepairCount != null">
                leader_repair_count = #{leaderRepairCount,jdbcType=INTEGER},
            </if>
            <if test="audit != null">
                is_audit = #{audit,jdbcType=BIT},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="queryNeedAuditOrgCodeList" resultType="java.lang.String">
        SELECT org_ins
        FROM (
                 SELECT
                     org_ins,
                     is_audit,
                     ROW_NUMBER() over (PARTITION BY org_ins ORDER BY created_time DESC) as rn
                 FROM agent_attendance_time
                 WHERE years <![CDATA[ <= ]]> #{years}
             ) as subquery
        WHERE rn = 1 and is_audit = true
    </select>
</mapper>
