<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hqins.agent.postsale.dao.mapper.AgentAttendanceExtMapper">

    <resultMap id="Base_Result_Map" type="com.hqins.agent.postsale.dao.entity.AgentAttendance">
            <result property="id" column="id"/>
            <result property="employeeCode" column="employee_code"/>
            <result property="employeeName" column="employee_name"/>
            <result property="companyName" column="company_name"/>
            <result property="companyCode" column="company_code"/>
            <result property="orgType" column="org_type"/>
            <result property="orgCode" column="org_code"/>
            <result property="orgName" column="org_name"/>
            <result property="topCode" column="top_code"/>
            <result property="topName" column="top_name"/>
            <result property="addressType" column="address_type"/>
            <result property="locationCode" column="location_code"/>
            <result property="locationName" column="location_name"/>
            <result property="signInType" column="sign_in_type"/>
            <result property="audit" column="audit"/>
            <result property="auditor" column="auditor"/>
            <result property="auditType" column="audit_type"/>
            <result property="auditTime" column="audit_time"/>
            <result property="auditRemark" column="audit_remark"/>
            <result property="auditOutcome" column="audit_outcome"/>
            <result property="checkInTime" column="check_in_time"/>
            <result property="checkOutTime" column="check_out_time"/>
            <result property="longitude" column="longitude"/>
            <result property="latitude" column="latitude"/>
            <result property="businessLabel" column="business_label"/>
            <result property="activityLabel" column="activity_label"/>
            <result property="spotCheckType" column="spot_check_type"/>
            <result property="createdTime" column="created_time"/>
            <result property="updateTime" column="update_time"/>
            <result property="deleted" column="is_deleted"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,employee_code,employee_name,company_name,company_code,org_type,org_code,org_name,top_code,top_name,address_type,location_code,location_name,sign_in_type,audit,auditor,audit_type,audit_time,audit_remark,audit_outcome,check_in_time,check_out_time,longitude,latitude,business_label,activity_label,spot_check_type,created_time,update_time,is_deleted,`authentication`
    </sql>
    <select id="selectCalendarByMonth" resultType="java.lang.Integer">
        SELECT COUNT(DISTINCT location_code) AS monthPunchInCount
        FROM agent_attendance
        WHERE employee_code = #{employeeCode}
          AND check_in_time <![CDATA[ >= ]]> #{monchStartTime}
          AND check_in_time <![CDATA[ <= ]]> #{monchEndTime}
          AND audit IN ('PASS_THE_AUDIT', 'AUTO_BYPASS')
          AND is_deleted = false
          AND address_type = 'MERCHANT'
    </select>
    <select id="selectCalendarDataByMonth" resultType="com.hqins.agent.postsale.dto.response.MonthPunchIn" parameterType="com.hqins.agent.postsale.dto.request.DataStatisticsRequest">
        SELECT
        DAY(check_in_time) as thatDay,
        check_in_time as signInTime,
        location_name as dotName,
        check_out_time as signOutTime,
        spot_check_type as spotCheckType,
        audit as checkType,
        id,
        audit_type as signInType,
        address_type as addressType,
        location_code as locationCode,
        location_name as locationName,
        audit_type as auditType,
        audit_outcome as  auditOutcome,
        sign_in_type as appointType
        FROM
        agent_attendance
        <where>
            employee_code = #{employeeCode}
            <if test="null != request.day ">
                AND DAY(check_in_time) = #{request.day}
            </if>
            AND check_in_time <![CDATA[ >= ]]> #{monchStartTime}
            AND check_in_time <![CDATA[ <= ]]> #{monchEndTime}
            AND is_deleted = false
        order by check_in_time asc
        </where>
    </select>
    <select id="selectCalendarDataByMonthBatch" resultType="com.hqins.agent.postsale.dto.response.MonthPunchIn" parameterType="com.hqins.agent.postsale.dto.request.DataStatisticsRequest">
        SELECT
        DAY(check_in_time) as thatDay,
        check_in_time as signInTime,
        location_name as dotName,
        check_out_time as signOutTime,
        spot_check_type as spotCheckType,
        audit as checkType,
        id,
        audit_type as auditType,
        address_type as addressType,
        location_code as locationCode,
        location_name as locationName,
        sign_in_type as signInType,
        employee_code as employeeCode
        FROM
        agent_attendance
        <where>
            employee_code in
            <foreach collection="employeeCode" item="emp" open="(" separator="," close=")">
                #{emp}
            </foreach>
            <if test="null != request.day ">
                AND DAY(check_in_time) = #{request.day}
            </if>
            AND check_in_time <![CDATA[ >= ]]> #{monthStartTime}
            AND check_in_time <![CDATA[ <= ]]> #{monthEndTime}
            AND is_deleted = false
            order by check_in_time asc
        </where>
    </select>

    <select id="selectDaySingData" resultType="com.hqins.agent.postsale.dao.entity.AgentAttendance">
        select
        <include refid="Base_Column_List"/>
        from agent_attendance
        where
        employee_code = #{employeeCode}
        AND is_deleted = false
        AND YEAR(check_in_time) = YEAR(#{date})
        AND MONTH(check_in_time) = MONTH(#{date})
        AND DAY(check_in_time) = DAY(#{date})
    </select>

    <select id="selectAttCust" resultType="com.hqins.agent.postsale.dao.entity.AttendanceCustomer">
        SELECT
            id AS id,
            attendance_id AS attendanceId,
            customer_name AS customerName,
            customer_gender AS customerGender,
            estimated_premium AS estimatedPremium,
            outlets AS outlets,
            outlets_code AS outletsCode,
            manger_name AS mangerName,
            other_info AS otherInfo,
            created_time AS createdTime,
            updated_time AS updatedTime
        FROM attendance_customer
        WHERE attendance_id = #{id}
    </select>

    <select id="selectAttAct" resultType="com.hqins.agent.postsale.dao.entity.AttendanceActivity">
        SELECT
            id AS id,
            attendance_id AS attendanceId,
            label AS label,
            explain_the_topic AS explainTheTopic,
            number_of_participants AS numberOfParticipants,
            activity_outlets AS activityOutlets,
            activity_outlets_code AS activityOutletsCode,
            detail_record AS detailRecord
        FROM
            attendance_activity
        WHERE attendance_id = #{id}
    </select>
    <select id="selectCalendarByDay" resultType="java.lang.Integer">
        SELECT
        count(DISTINCT DAY(check_in_time)) as monthPunchInDay
        FROM agent_attendance
        WHERE employee_code = #{employeeCode}
            AND YEAR(check_in_time) =  #{years}
          AND MONTH(check_in_time) = #{month}
          AND audit IN ('PASS_THE_AUDIT', 'AUTO_BYPASS')
          AND is_deleted = false
    </select>
    <select id="selectCountByReissueACard" resultType="java.lang.String">
        SELECT audit_type as signInType
        FROM agent_attendance
        WHERE employee_code = #{employeeCode}
            AND YEAR(check_in_time) = #{years}
          AND MONTH(check_in_time) = #{month}
          AND is_deleted = false
        AND audit != 'AUDIT_FAILURE'
    </select>

    <select id="selectYBSingData" resultType="com.hqins.agent.postsale.dao.entity.AgentAttendance">
        SELECT '上午' as period, t1.* FROM (
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        agent_attendance
        WHERE
        employee_code = #{employeeCode}
        AND check_in_time <![CDATA[ >= ]]> CONCAT(CURRENT_DATE(), #{mStartTime})
        AND check_in_time <![CDATA[ <= ]]> CONCAT(CURRENT_DATE(), #{mEndTime})
        AND is_deleted = 0
        ORDER BY
        check_in_time DESC
        LIMIT 1
        ) t1
        UNION ALL
        SELECT '下午' as period, t2.* FROM (
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        agent_attendance
        WHERE
        employee_code = #{employeeCode}
        AND check_in_time <![CDATA[ >= ]]> CONCAT(CURRENT_DATE(), #{aStartTime})
        AND check_in_time <![CDATA[ <= ]]> CONCAT(CURRENT_DATE(), #{aEndTime})
        AND is_deleted = 0
        ORDER BY
        check_in_time DESC
        LIMIT 1
        ) t2
        ORDER BY
        period;
    </select>


</mapper>
