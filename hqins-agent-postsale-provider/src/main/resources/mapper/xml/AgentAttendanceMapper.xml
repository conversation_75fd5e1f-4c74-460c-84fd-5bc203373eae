<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hqins.agent.postsale.dao.mapper.AgentAttendanceMapper">

    <resultMap id="Base_Result_Map" type="com.hqins.agent.postsale.dao.entity.AgentAttendance">
            <result property="id" column="id"/>
            <result property="employeeCode" column="employee_code"/>
            <result property="employeeName" column="employee_name"/>
            <result property="companyName" column="company_name"/>
            <result property="companyCode" column="company_code"/>
            <result property="orgType" column="org_type"/>
            <result property="orgCode" column="org_code"/>
            <result property="orgName" column="org_name"/>
            <result property="topCode" column="top_code"/>
            <result property="topName" column="top_name"/>
            <result property="addressType" column="address_type"/>
            <result property="locationCode" column="location_code"/>
            <result property="locationName" column="location_name"/>
            <result property="signInType" column="sign_in_type"/>
            <result property="audit" column="audit"/>
            <result property="auditor" column="auditor"/>
            <result property="auditorId" column="auditor_id"/>
            <result property="auditType" column="audit_type"/>
            <result property="auditTime" column="audit_time"/>
            <result property="auditRemark" column="audit_remark"/>
            <result property="auditOutcome" column="audit_outcome"/>
            <result property="checkInTime" column="check_in_time"/>
            <result property="checkOutTime" column="check_out_time"/>
            <result property="longitude" column="longitude"/>
            <result property="latitude" column="latitude"/>
            <result property="outLongitude" column="out_longitude"/>
            <result property="outLatitude" column="out_latitude"/>
            <result property="businessLabel" column="business_label"/>
            <result property="activityLabel" column="activity_label"/>
            <result property="spotCheckType" column="spot_check_type"/>
            <result property="createdTime" column="created_time"/>
            <result property="updateTime" column="update_time"/>
            <result property="deleted" column="is_deleted"/>
            <result property="authentication" column="authentication"/>
            <result property="reviewersCode" column="reviewers_code"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,employee_code,employee_name,company_name,company_code,org_type,org_code,org_name,top_code,top_name,address_type,location_code,location_name,sign_in_type,audit,auditor,auditor_id,audit_type,audit_time,audit_remark,audit_outcome,check_in_time,check_out_time,longitude,latitude,out_longitude,out_latitude,business_label,activity_label,spot_check_type,created_time,update_time,is_deleted,authentication,reviewers_code
    </sql>

    <insert id="insertSelective" parameterType="com.hqins.agent.postsale.dao.entity.AgentAttendance"
            useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        insert into agent_attendance
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="employeeCode != null">
                employee_code,
            </if>
            <if test="employeeName != null">
                employee_name,
            </if>
            <if test="companyName != null">
                company_name,
            </if>
            <if test="companyCode != null">
                company_code,
            </if>
            <if test="orgType != null">
                org_type,
            </if>
            <if test="orgCode != null">
                org_code,
            </if>
            <if test="orgName != null">
                org_name,
            </if>
            <if test="topCode != null">
                top_code,
            </if>
            <if test="topName != null">
                top_name,
            </if>
            <if test="addressType != null">
                address_type,
            </if>
            <if test="locationCode != null">
                location_code,
            </if>
            <if test="locationName != null">
                location_name,
            </if>
            <if test="signInType != null">
                sign_in_type,
            </if>
            <if test="audit != null">
                audit,
            </if>
            <if test="auditor != null">
                auditor,
            </if>
            <if test="auditorId != null">
                auditor_id,
            </if>
            <if test="auditType != null">
                audit_type,
            </if>
            <if test="auditTime != null">
                audit_time,
            </if>
            <if test="auditRemark != null">
                audit_remark,
            </if>
            <if test="auditOutcome != null">
                audit_outcome,
            </if>
            <if test="checkInTime != null">
                check_in_time,
            </if>
            <if test="checkOutTime != null">
                check_out_time,
            </if>
            <if test="longitude != null">
                longitude,
            </if>
            <if test="latitude != null">
                latitude,
            </if>
            <if test="outLongitude != null">
                out_longitude,
            </if>
            <if test="outLatitude != null">
                out_latitude,
            </if>
            <if test="businessLabel != null">
                business_label,
            </if>
            <if test="activityLabel != null">
                activity_label,
            </if>
            <if test="spotCheckType != null">
                spot_check_type,
            </if>
            <if test="createdTime != null">
                created_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="deleted != null">
                is_deleted,
            </if>
            <if test="authentication != null">
                authentication,
            </if>
            <if test="reviewersCode != null">
                reviewers_code,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="employeeCode != null">
                #{employeeCode,jdbcType=VARCHAR},
            </if>
            <if test="employeeName != null">
                #{employeeName,jdbcType=VARCHAR},
            </if>
            <if test="companyName != null">
                #{companyName,jdbcType=VARCHAR},
            </if>
            <if test="companyCode != null">
                #{companyCode,jdbcType=VARCHAR},
            </if>
            <if test="orgType != null">
                #{orgType,jdbcType=VARCHAR},
            </if>
            <if test="orgCode != null">
                #{orgCode,jdbcType=VARCHAR},
            </if>
            <if test="orgName != null">
                #{orgName,jdbcType=VARCHAR},
            </if>
            <if test="topCode != null">
                #{topCode,jdbcType=VARCHAR},
            </if>
            <if test="topName != null">
                #{topName,jdbcType=VARCHAR},
            </if>
            <if test="addressType != null">
                #{addressType,jdbcType=VARCHAR},
            </if>
            <if test="locationCode != null">
                #{locationCode,jdbcType=VARCHAR},
            </if>
            <if test="locationName != null">
                #{locationName,jdbcType=VARCHAR},
            </if>
            <if test="signInType != null">
                #{signInType,jdbcType=VARCHAR},
            </if>
            <if test="audit != null">
                #{audit,jdbcType=VARCHAR},
            </if>
            <if test="auditor != null">
                #{auditor,jdbcType=VARCHAR},
            </if>
            <if test="auditorId != null">
                #{auditorId,jdbcType=VARCHAR},
            </if>
            <if test="auditType != null">
                #{auditType,jdbcType=VARCHAR},
            </if>
            <if test="auditTime != null">
                #{auditTime,jdbcType=TIME},
            </if>
            <if test="auditRemark != null">
                #{auditRemark,jdbcType=VARCHAR},
            </if>
            <if test="auditOutcome != null">
                #{auditOutcome,jdbcType=VARCHAR},
            </if>
            <if test="checkInTime != null">
                #{checkInTime,jdbcType=TIME},
            </if>
            <if test="checkOutTime != null">
                #{checkOutTime,jdbcType=TIME},
            </if>
            <if test="longitude != null">
                #{longitude,jdbcType=VARCHAR},
            </if>
            <if test="latitude != null">
                #{latitude,jdbcType=VARCHAR},
            </if>
            <if test="outLongitude != null">
                #{outLongitude,jdbcType=VARCHAR},
            </if>
            <if test="outLatitude != null">
                #{outLatitude,jdbcType=VARCHAR},
            </if>
            <if test="businessLabel != null">
                #{businessLabel,jdbcType=VARCHAR},
            </if>
            <if test="activityLabel != null">
                #{activityLabel,jdbcType=VARCHAR},
            </if>
            <if test="spotCheckType != null">
                #{spotCheckType,jdbcType=VARCHAR},
            </if>
            <if test="createdTime != null">
                #{createdTime,jdbcType=TIME},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIME},
            </if>
            <if test="deleted != null">
                #{deleted,jdbcType=BIT},
            </if>
            <if test="authentication != null">
                #{authentication,jdbcType=BIT},
            </if>
            <if test="reviewersCode != null">
                #{reviewersCode,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.hqins.agent.postsale.dao.entity.AgentAttendance">
        update agent_attendance
        <set>
            <if test="employeeCode != null">
                employee_code = #{employeeCode,jdbcType=VARCHAR},
            </if>
            <if test="employeeName != null">
                employee_name = #{employeeName,jdbcType=VARCHAR},
            </if>
            <if test="companyName != null">
                company_name = #{companyName,jdbcType=VARCHAR},
            </if>
            <if test="companyCode != null">
                company_code = #{companyCode,jdbcType=VARCHAR},
            </if>
            <if test="orgType != null">
                org_type = #{orgType,jdbcType=VARCHAR},
            </if>
            <if test="orgCode != null">
                org_code = #{orgCode,jdbcType=VARCHAR},
            </if>
            <if test="orgName != null">
                org_name = #{orgName,jdbcType=VARCHAR},
            </if>
            <if test="topCode != null">
                top_code = #{topCode,jdbcType=VARCHAR},
            </if>
            <if test="topName != null">
                top_name = #{topName,jdbcType=VARCHAR},
            </if>
            <if test="addressType != null">
                address_type = #{addressType,jdbcType=VARCHAR},
            </if>
            <if test="locationCode != null">
                location_code = #{locationCode,jdbcType=VARCHAR},
            </if>
            <if test="locationName != null">
                location_name = #{locationName,jdbcType=VARCHAR},
            </if>
            <if test="signInType != null">
                sign_in_type = #{signInType,jdbcType=VARCHAR},
            </if>
            <if test="audit != null">
                audit = #{audit,jdbcType=VARCHAR},
            </if>
            <if test="auditor != null">
                auditor = #{auditor,jdbcType=VARCHAR},
            </if>
            <if test="auditorId != null">
                auditor_id = #{auditorId,jdbcType=VARCHAR},
            </if>
            <if test="auditType != null">
                audit_type = #{auditType,jdbcType=VARCHAR},
            </if>
            <if test="auditTime != null">
                audit_time = #{auditTime,jdbcType=TIME},
            </if>
            <if test="auditRemark != null">
                audit_remark = #{auditRemark,jdbcType=VARCHAR},
            </if>
            <if test="auditOutcome != null">
                audit_outcome = #{auditOutcome,jdbcType=VARCHAR},
            </if>
            <if test="checkInTime != null">
                check_in_time = #{checkInTime,jdbcType=TIME},
            </if>
            <if test="checkOutTime != null">
                check_out_time = #{checkOutTime,jdbcType=TIME},
            </if>
            <if test="longitude != null">
                longitude = #{longitude,jdbcType=VARCHAR},
            </if>
            <if test="latitude != null">
                latitude = #{latitude,jdbcType=VARCHAR},
            </if>
            <if test="outLongitude != null">
                out_longitude = #{outLongitude,jdbcType=VARCHAR},
            </if>
            <if test="outLatitude != null">
                out_latitude = #{outLatitude,jdbcType=VARCHAR},
            </if>
            <if test="businessLabel != null">
                business_label = #{businessLabel,jdbcType=VARCHAR},
            </if>
            <if test="activityLabel != null">
                activity_label = #{activityLabel,jdbcType=VARCHAR},
            </if>
            <if test="spotCheckType != null">
                spot_check_type = #{spotCheckType,jdbcType=VARCHAR},
            </if>
            <if test="createdTime != null">
                created_time = #{createdTime,jdbcType=TIME},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIME},
            </if>
            <if test="deleted != null">
                is_deleted = #{deleted,jdbcType=BIT},
            </if>
            <if test="authentication != null">
                authentication = #{authentication,jdbcType=BIT},
            </if>
            <if test="reviewersCode != null">
                reviewers_code = #{reviewersCode,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateDeleteByPrimaryKey" parameterType="com.hqins.agent.postsale.dao.entity.AgentAttendance">
        update agent_attendance
        <set>
                <if test="employeeCode != null">
                    employee_code = #{employeeCode,jdbcType=VARCHAR},
                </if>
                <if test="employeeName != null">
                    employee_name = #{employeeName,jdbcType=VARCHAR},
                </if>
                <if test="companyName != null">
                    company_name = #{companyName,jdbcType=VARCHAR},
                </if>
                <if test="companyCode != null">
                    company_code = #{companyCode,jdbcType=VARCHAR},
                </if>
                <if test="orgType != null">
                    org_type = #{orgType,jdbcType=VARCHAR},
                </if>
                <if test="orgCode != null">
                    org_code = #{orgCode,jdbcType=VARCHAR},
                </if>
                <if test="orgName != null">
                    org_name = #{orgName,jdbcType=VARCHAR},
                </if>
                <if test="topCode != null">
                    top_code = #{topCode,jdbcType=VARCHAR},
                </if>
                <if test="topName != null">
                    top_name = #{topName,jdbcType=VARCHAR},
                </if>
                <if test="addressType != null">
                    address_type = #{addressType,jdbcType=VARCHAR},
                </if>
                <if test="locationCode != null">
                    location_code = #{locationCode,jdbcType=VARCHAR},
                </if>
                <if test="locationName != null">
                    location_name = #{locationName,jdbcType=VARCHAR},
                </if>
                <if test="signInType != null">
                    sign_in_type = #{signInType,jdbcType=VARCHAR},
                </if>
                <if test="audit != null">
                    audit = #{audit,jdbcType=VARCHAR},
                </if>
                <if test="auditor != null">
                    auditor = #{auditor,jdbcType=VARCHAR},
                </if>
                <if test="auditorId != null">
                    auditor_id = #{auditorId,jdbcType=VARCHAR},
                </if>
                <if test="auditType != null">
                    audit_type = #{auditType,jdbcType=VARCHAR},
                </if>
                <if test="auditTime != null">
                    audit_time = #{auditTime,jdbcType=TIME},
                </if>
                <if test="auditRemark != null">
                    audit_remark = #{auditRemark,jdbcType=VARCHAR},
                </if>
                <if test="auditOutcome != null">
                    audit_outcome = #{auditOutcome,jdbcType=VARCHAR},
                </if>
                <if test="checkInTime != null">
                    check_in_time = #{checkInTime,jdbcType=TIME},
                </if>
                <if test="checkOutTime != null">
                    check_out_time = #{checkOutTime,jdbcType=TIME},
                </if>
                <if test="longitude != null">
                    longitude = #{longitude,jdbcType=VARCHAR},
                </if>
                <if test="latitude != null">
                    latitude = #{latitude,jdbcType=VARCHAR},
                </if>
                <if test="outLongitude != null">
                    out_longitude = #{outLongitude,jdbcType=VARCHAR},
                </if>
                <if test="outLatitude != null">
                    out_latitude = #{outLatitude,jdbcType=VARCHAR},
                </if>
                <if test="businessLabel != null">
                    business_label = #{businessLabel,jdbcType=VARCHAR},
                </if>
                <if test="activityLabel != null">
                    activity_label = #{activityLabel,jdbcType=VARCHAR},
                </if>
                <if test="spotCheckType != null">
                    spot_check_type = #{spotCheckType,jdbcType=VARCHAR},
                </if>
                <if test="createdTime != null">
                    created_time = #{createdTime,jdbcType=TIME},
                </if>
                <if test="updateTime != null">
                    update_time = #{updateTime,jdbcType=TIME},
                </if>
                <if test="deleted != null">
                    is_deleted = #{deleted},
                </if>
                <if test="authentication != null">
                    authentication = #{authentication,jdbcType=BIT},
                </if>
                <if test="reviewersCode != null">
                    reviewers_code = #{reviewersCode,jdbcType=VARCHAR},
                </if>
        </set>
        where id = #{id,jdbcType=BIGINT} and is_deleted = false
    </update>
    <select id="selectCountAll" resultType="java.lang.Integer">
        select count(*) from (
        SELECT * FROM agent_attendance
        WHERE EMPLOYEE_CODE =  #{employeeCode,jdbcType=VARCHAR}
        AND CHECK_IN_TIME <![CDATA[ >= ]]> #{startTime,jdbcType=TIME}
        AND CHECK_IN_TIME <![CDATA[ <= ]]> #{endTime,jdbcType=TIME}
        <if test="dayList != null and !dayList.isEmpty()">
            AND  DATE(CHECK_IN_TIME) NOT IN
            <foreach collection="dayList" item="day" open="(" separator="," close=")">
                #{day}
            </foreach>
        </if>
        GROUP BY DATE(CHECK_IN_TIME)
        ) aa
    </select>
    <select id="selectNormalCount" resultType="java.lang.Integer">
        select count(*) from (
        SELECT * FROM agent_attendance
        WHERE EMPLOYEE_CODE =  #{employeeCode,jdbcType=VARCHAR}
        AND AUDIT NOT IN
        <foreach collection="auditList" item="audit" open="(" separator="," close=")">
            #{audit,jdbcType=VARCHAR}
        </foreach>
        AND CHECK_IN_TIME IS NOT  NULL
        AND CHECK_OUT_TIME IS NOT NULL
        AND CHECK_IN_TIME <![CDATA[ >= ]]> #{startTime,jdbcType=TIME}
        AND CHECK_IN_TIME <![CDATA[ <= ]]> #{endTime,jdbcType=TIME}
        <if test="dayList != null and !dayList.isEmpty()">
            AND  DATE(CHECK_IN_TIME) NOT IN
            <foreach collection="dayList" item="day" open="(" separator="," close=")">
                #{day}
            </foreach>
        </if>
        GROUP BY DATE(CHECK_IN_TIME)
        ) aa

    </select>
    <select id="selectChecktime" resultType="java.lang.Integer">
        select count(*) from agent_attendance where DATE(CHECK_IN_TIME) = #{date} and employee_code =#{employeeCode,jdbcType=VARCHAR}
    </select>
    <select id="selectNormalTeamCount" resultType="com.hqins.agent.postsale.dao.entity.AgentAttendance">
        SELECT * FROM agent_attendance
        WHERE EMPLOYEE_CODE in
        <foreach collection="employeeCode" item="code" open="(" separator="," close=")">
            #{code}
        </foreach>
        AND AUDIT NOT IN
        <foreach collection="auditList" item="audit" open="(" separator="," close=")">
            #{audit,jdbcType=VARCHAR}
        </foreach>
        AND CHECK_IN_TIME IS NOT  NULL
        AND CHECK_OUT_TIME IS NOT NULL
        AND CHECK_IN_TIME <![CDATA[ >= ]]> #{startTime,jdbcType=TIME}
        AND CHECK_IN_TIME <![CDATA[ <= ]]> #{endTime,jdbcType=TIME}
        <if test="dayList != null and !dayList.isEmpty()">
            AND  DATE(CHECK_IN_TIME) NOT IN
            <foreach collection="dayList" item="day" open="(" separator="," close=")">
                #{day}
            </foreach>
        </if>
        GROUP BY  employee_code,   DATE(CHECK_IN_TIME)
    </select>
    <select id="selectNoneTeamCount" resultType="java.lang.Integer">
        SELECT count(*) FROM agent_attendance
        WHERE EMPLOYEE_CODE = #{employeeCode}
        AND CHECK_IN_TIME  <![CDATA[ >= ]]> #{startTime,jdbcType=TIME}
        AND CHECK_IN_TIME <![CDATA[ <= ]]> #{endTime,jdbcType=TIME}
        <if test="dayList != null and !dayList.isEmpty()">
            AND  DATE(CHECK_IN_TIME) NOT IN
            <foreach collection="dayList" item="day" open="(" separator="," close=")">
                #{day}
            </foreach>
        </if>
        AND CHECK_OUT_TIME IS NULL ORDER BY  EMPLOYEE_CODE
    </select>
    <select id="selectSignCheck" resultType="java.lang.Integer">
        select count(1) from agent_attendance
        where check_in_time <![CDATA[ >= ]]> #{startTime}
          and check_in_time <![CDATA[ < ]]> #{endTime}
          and employee_code=#{employeeCode,jdbcType=VARCHAR}
          and audit ='TO_BE_REVIEWED'
    </select>

    <select id="randQueryByOrgCode" resultType="com.hqins.agent.postsale.dao.entity.AgentAttendance">
        select
        <include refid="Base_Column_List"/>
        from agent_attendance
        where org_code = #{orgCode} and created_time <![CDATA[ >= ]]> #{startTime} and created_time <![CDATA[ <= ]]> #{endTime}
        and audit = 'AUTO_BYPASS' and spot_check_type = 'UNTESTED'
        ORDER BY RAND()
        LIMIT #{limitCount}
    </select>
    <select id="selectExcelDataList" resultType="com.hqins.agent.postsale.dao.entity.AgentAttendance">
        SELECT *
        FROM agent_attendance
        WHERE
        CHECK_IN_TIME <![CDATA[ >= ]]> #{startTime,jdbcType=TIME}
        AND CHECK_IN_TIME <![CDATA[ <= ]]> #{endTime,jdbcType=TIME} AND
        ((DATE(check_in_time) != CURDATE())  OR
        CHECK_OUT_TIME IS NOT NULL)
        <if test="partnerCodes != null and !partnerCodes.isEmpty()">
            AND ORG_CODE IN
            <foreach collection="partnerCodes" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        GROUP BY EMPLOYEE_CODE
    </select>

    <select id="examineQuery" resultMap="Base_Result_Map">
        SELECT
        <include refid="Base_Column_List"/>
        FROM agent_attendance
        <where>
            <!-- 基本条件过滤 -->
            <if test="request.companyCode != null and request.companyCode != ''">
                AND company_code = #{request.companyCode}
            </if>
            <if test="request.addressType != null">
                AND address_type = #{request.addressType}
            </if>
            <if test="request.orgCode != null and request.orgCode != ''">
                AND org_code = #{request.orgCode}
            </if>
            <if test="request.topCode != null and request.topCode != ''">
                AND top_code = #{request.topCode}
            </if>
            <if test="request.employeeCode != null and request.employeeCode != ''">
                AND employee_code = #{request.employeeCode}
            </if>
            <if test="request.employeeName != null and request.employeeName != ''">
                AND employee_name = #{request.employeeName}
            </if>
            <if test="request.locationCode != null and request.locationCode != ''">
                AND location_code = #{request.locationCode}
            </if>
            <if test="request.signInType != null and request.signInType != ''">
                AND sign_in_type = #{request.signInType}
            </if>
            <if test="request.spotCheckType != null and request.spotCheckType != ''">
                AND spot_check_type = #{request.spotCheckType}
            </if>
            <if test="request.checkInTime != null and request.checkInTime != ''">
                AND check_in_time <![CDATA[ >= ]]> CONCAT(#{request.checkInTime}, ' 00:00:00')
            </if>
            <if test="request.checkOutTime != null and request.checkOutTime != ''">
                AND check_in_time <![CDATA[ <= ]]> CONCAT(#{request.checkOutTime}, ' 23:59:59')
            </if>
            <if test="partnerCodes != null and !partnerCodes.isEmpty()">
                AND org_code IN
                <foreach collection="partnerCodes" item="code" open="(" separator="," close=")">
                    #{code}
                </foreach>
            </if>

            <!-- 审核状态处理 -->
            <choose>
                <when test="request.audit != null and request.audit != '' and request.audit == 'PENDING_APPLICATION'">
                    AND audit NOT IN
                    <foreach collection="checkTypeList" item="type" open="(" separator="," close=")">
                        #{type}
                    </foreach>
                    AND check_out_time IS NULL
                    AND (DATE(check_in_time) <![CDATA[ <= ]]> DATE_SUB(CURDATE(), INTERVAL 1 DAY))
                </when>
                <when test="request.audit != null and request.audit != ''">
                    AND audit = #{request.audit}
                </when>
            </choose>

            <!-- 打卡状态处理 漏签退-->
            <choose>
                <when test="request.auditType != null and request.auditType != '' and request.auditType == 'NONE'">
                    AND check_in_time <![CDATA[ < ]]> CURDATE()
                    AND check_out_time IS NULL
                    AND top_code != 'P00001'
                    <if test="request.orgCode != null and request.orgCode != ''">
                        AND org_code = #{request.orgCode}
                    </if>
                    <if test="request.audit != null and request.audit != '' and request.audit != 'PENDING_APPLICATION'">
                        AND audit = #{request.audit}
                    </if>
                    <if test="request.addressType != null">
                        AND address_type = #{request.addressType}
                    </if>
                    <if test="request.topCode != null and request.topCode != ''">
                        AND top_code = #{request.topCode}
                    </if>
                    <if test="request.employeeCode != null and request.employeeCode != ''">
                        AND employee_code = #{request.employeeCode}
                    </if>
                    <if test="request.employeeName != null and request.employeeName != ''">
                        AND employee_name = #{request.employeeName}
                    </if>
                    <if test="request.locationCode != null and request.locationCode != ''">
                        AND location_code = #{request.locationCode}
                    </if>
                    <if test="request.signInType != null and request.signInType != ''">
                        AND sign_in_type = #{request.signInType}
                    </if>
                    <if test="request.spotCheckType != null and request.spotCheckType != ''">
                        AND spot_check_type = #{request.spotCheckType}
                    </if>
                    <if test="request.checkInTime != null and request.checkInTime != ''">
                        AND check_in_time <![CDATA[ >= ]]> CONCAT(#{request.checkInTime}, ' 00:00:00')
                    </if>
                    <if test="request.checkOutTime != null and request.checkOutTime != ''">
                        AND check_in_time <![CDATA[ <= ]]> CONCAT(#{request.checkOutTime}, ' 23:59:59')
                    </if>
                    <if test="partnerCodes != null and !partnerCodes.isEmpty()">
                        AND org_code IN
                        <foreach collection="partnerCodes" item="code" open="(" separator="," close=")">
                            #{code}
                        </foreach>
                    </if>

                    <if test="request.audit != null and request.audit != '' and request.audit == 'PENDING_APPLICATION'">
                        AND audit NOT IN
                        <foreach collection="checkTypeList" item="type" open="(" separator="," close=")">
                            #{type}
                        </foreach>
                        AND check_out_time IS NULL
                    </if>
                </when>
                <when test="request.auditType != null and request.auditType != '' and request.auditType == 'ABSENTEE'">
                    AND audit_type = 'APPOINT_ALL'
                </when>
                <when test="request.auditType != null and request.auditType != '' and request.auditType == 'SIGN_IN'">
                    AND (
                        (DATE(check_in_time) != CURDATE() AND audit_type = 'SIGN_IN' AND top_code = 'P00001')
                        OR (DATE(check_in_time) != CURDATE() AND audit_type = 'SIGN_IN' AND top_code != 'P00001' AND check_out_time IS NOT NULL)
                    )
                    <if test="request.companyCode != null and request.companyCode != ''">
                        AND company_code = #{request.companyCode}
                    </if>
                    <if test="request.addressType != null">
                        AND address_type = #{request.addressType}
                    </if>
                    <if test="request.orgCode != null and request.orgCode != ''">
                        AND org_code = #{request.orgCode}
                    </if>
                    <if test="request.topCode != null and request.topCode != ''">
                        AND top_code = #{request.topCode}
                    </if>
                    <if test="request.employeeCode != null and request.employeeCode != ''">
                        AND employee_code = #{request.employeeCode}
                    </if>
                    <if test="request.employeeName != null and request.employeeName != ''">
                        AND employee_name = #{request.employeeName}
                    </if>
                    <if test="request.locationCode != null and request.locationCode != ''">
                        AND location_code = #{request.locationCode}
                    </if>
                    <if test="request.signInType != null and request.signInType != ''">
                        AND sign_in_type = #{request.signInType}
                    </if>
                    <if test="request.spotCheckType != null and request.spotCheckType != ''">
                        AND spot_check_type = #{request.spotCheckType}
                    </if>
                    <if test="request.checkInTime != null and request.checkInTime != ''">
                        AND check_in_time <![CDATA[ >= ]]> CONCAT(#{request.checkInTime}, ' 00:00:00')
                    </if>
                    <if test="request.checkOutTime != null and request.checkOutTime != ''">
                        AND check_in_time <![CDATA[ <= ]]> CONCAT(#{request.checkOutTime}, ' 23:59:59')
                    </if>
                    <if test="partnerCodes != null and !partnerCodes.isEmpty()">
                        AND org_code IN
                        <foreach collection="partnerCodes" item="code" open="(" separator="," close=")">
                            #{code}
                        </foreach>
                    </if>
                </when>
                <when test="request.auditType != null and request.auditType != ''">
                    AND audit_type LIKE CONCAT('%', #{request.auditType}, '%')
                </when>
            </choose>

            <!-- 处理所有字段为空的情况 -->
            <if test="request == null or (request.companyCode == null or request.companyCode == '') and (request.addressType == null) and (request.orgCode == null or request.orgCode == '') and (request.topCode == null or request.topCode == '') and (request.employeeCode == null or request.employeeCode == '') and (request.employeeName == null or request.employeeName == '') and (request.locationCode == null or request.locationCode == '') and (request.signInType == null or request.signInType == '') and (request.spotCheckType == null or request.spotCheckType == '') and (request.checkInTime == null or request.checkInTime == '') and (request.checkOutTime == null or request.checkOutTime == '') and (request.audit == null or request.audit == '') and (request.auditType == null or request.auditType == '')">
                AND (
                    (DATE(check_in_time) <![CDATA[ <= ]]> DATE_SUB(CURDATE(), INTERVAL 1 DAY) AND audit_type NOT LIKE '%APPOINT_%' AND is_deleted = false)
                    OR (org_code IN (${partners}) AND DATE(check_in_time) = CURDATE() AND audit in('TO_BE_REVIEWED','AUDIT_FAILURE','PASS_THE_AUDIT','AUTO_BYPASS') AND check_out_time IS NOT NULL AND audit_type NOT LIKE '%APPOINT_%')
                )
            </if>
            <!-- 处理审核类型和审核状态都为空的情况 -->
            <if test="(request.auditType == null or request.auditType == '') and (request.audit == null or request.audit == '')">
                AND (
                    (DATE(check_in_time) != CURDATE() AND audit_type NOT LIKE '%APPOINT_%')
                    OR check_out_time IS NOT NULL
                )
                <if test="request.companyCode != null and request.companyCode != ''">
                    AND company_code = #{request.companyCode}
                </if>
                <if test="request.addressType != null">
                    AND address_type = #{request.addressType}
                </if>
                <if test="request.orgCode != null and request.orgCode != ''">
                    AND org_code = #{request.orgCode}
                </if>
                <if test="request.topCode != null and request.topCode != ''">
                    AND top_code = #{request.topCode}
                </if>
                <if test="request.employeeCode != null and request.employeeCode != ''">
                    AND employee_code = #{request.employeeCode}
                </if>
                <if test="request.employeeName != null and request.employeeName != ''">
                    AND employee_name = #{request.employeeName}
                </if>
                <if test="request.locationCode != null and request.locationCode != ''">
                    AND location_code = #{request.locationCode}
                </if>
                <if test="request.signInType != null and request.signInType != ''">
                    AND sign_in_type = #{request.signInType}
                </if>
                <if test="request.spotCheckType != null and request.spotCheckType != ''">
                    AND spot_check_type = #{request.spotCheckType}
                </if>
                <if test="request.checkInTime != null and request.checkInTime != ''">
                    AND check_in_time <![CDATA[ >= ]]> CONCAT(#{request.checkInTime}, ' 00:00:00')
                </if>
                <if test="request.checkOutTime != null and request.checkOutTime != ''">
                    AND check_in_time <![CDATA[ <= ]]> CONCAT(#{request.checkOutTime}, ' 23:59:59')
                </if>
                <if test="partnerCodes != null and !partnerCodes.isEmpty()">
                    AND org_code IN
                    <foreach collection="partnerCodes" item="code" open="(" separator="," close=")">
                        #{code}
                    </foreach>
                </if>
            </if>

            <!-- 不查询申请的记录 -->
            AND audit_type NOT LIKE '%APPOINT_%'
        </where>
        ORDER BY check_in_time DESC
    </select>

</mapper>
