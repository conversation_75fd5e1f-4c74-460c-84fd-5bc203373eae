<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <parent>
        <groupId>com.hqins.agent</groupId>
        <artifactId>hqins-agent-postsale</artifactId>
        <version>1.0.61-RELEASE</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>hqins-agent-postsale-provider</artifactId>
    <packaging>jar</packaging>

    <dependencies>
        <dependency>
            <groupId>com.hqins.common</groupId>
            <artifactId>hqins-common-utils</artifactId>
        </dependency>
        <dependency>
            <groupId>com.hqins.common</groupId>
            <artifactId>hqins-common-generator</artifactId>
        </dependency>
        <dependency>
            <groupId>com.hqins.thirdparty</groupId>
            <artifactId>hqins-thirdparty-api</artifactId>
            <version>1.1.9-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>cn.hqins.ihome</groupId>
            <artifactId>hqins-health-service-api</artifactId>
            <version>1.0.40</version>
        </dependency>
        <dependency>
            <groupId>com.hqins.agent</groupId>
            <artifactId>hqins-agent-postsale-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-to-slf4j</artifactId>
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-openfeign-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>
        <dependency>
            <groupId>com.hqins.common</groupId>
            <artifactId>hqins-common-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>redis.clients</groupId>
                    <artifactId>jedis</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.hqins.common</groupId>
            <artifactId>hqins-common-cache-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alicp.jetcache</groupId>
            <artifactId>jetcache-starter-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-jsr310</artifactId>
        </dependency>
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.hqins.agent</groupId>
            <artifactId>hqins-agent-org-api</artifactId>
            <version>1.1.7-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>cn.hqins.um</groupId>
            <artifactId>hqins-um-api</artifactId>
            <version>1.0.32</version>
        </dependency>
        <dependency>
            <groupId>cn.hqins.clue</groupId>
            <artifactId>hqins-clue-api</artifactId>
            <version>1.0.37</version>
        </dependency>
        <dependency>
            <groupId>com.hqins.admin</groupId>
            <artifactId>hqins-admin-cas-api</artifactId>
            <version>1.0.2-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
            <version>4.9.2</version>
        </dependency>
        <dependency>
            <groupId>com.hqins.job</groupId>
            <artifactId>hqins-job-core</artifactId>
            <version>2.3.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.belerweb</groupId>
            <artifactId>pinyin4j</artifactId>
            <version>2.5.0</version>
        </dependency>
        <dependency>
            <groupId>net.coobird</groupId>
            <artifactId>thumbnailator</artifactId>
            <version>0.4.14</version> <!-- 使用最新版本 -->
        </dependency>
        <dependency>
            <groupId>com.aliyun.openservices</groupId>
            <artifactId>ons-client</artifactId>
            <version>1.8.7.4.Final</version>
        </dependency>
        <dependency>
            <groupId>com.hqins.basic</groupId>
            <artifactId>hqins-basic-service-api</artifactId>
            <version>1.0.3-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.nacos</groupId>
            <artifactId>nacos-api</artifactId>
            <version>2.0.1</version>
        </dependency>
        <dependency>
            <groupId>com.hqins.agent</groupId>
            <artifactId>hqins-agent-marketing-api</artifactId>
            <version>1.1.25-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.hqins.common</groupId>
            <artifactId>hqins-common-boot-starter</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>nacos-api</artifactId>
                    <groupId>com.alibaba.nacos</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.hqins.file</groupId>
            <artifactId>hqins-file-service-api</artifactId>
            <version>1.0.19-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>3.2.1</version>
            <exclusions>
                <exclusion>
                    <artifactId>xmlbeans</artifactId>
                    <groupId>org.apache.xmlbeans</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>cn.hqins</groupId>
            <artifactId>hqins-policy-service-api</artifactId>
            <version>1.1.7</version>
        </dependency>
        <dependency>
            <groupId>org.apache.xmlbeans</groupId>
            <artifactId>xmlbeans</artifactId>
            <version>4.0.0</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-sleuth</artifactId>
            <version>2.1.6.RELEASE</version>
        </dependency>

        <!--极光推送-->
        <dependency>
            <groupId>cn.jpush.api</groupId>
            <artifactId>jpush-client</artifactId>
            <version>3.2.9</version>
        </dependency>

    </dependencies>

    <build>
        <finalName>hqins-agent-postsale</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>

    </build>

</project>
