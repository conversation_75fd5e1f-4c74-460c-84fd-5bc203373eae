package com.hqins.agent.postsale.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/3/3 16:53
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AttendanceAuditQueryVO implements Serializable {
    /**
     *id
     */
    @ApiModelProperty("主键ID")
    private Long id;

    /**
     * 工号
     */
    @ApiModelProperty("工号")
    private String employeeCode;

    /**
     * 姓名
     */
    @ApiModelProperty("姓名")
    private String employeeName;

    /**
     * 归属合伙人组织
     */
    @ApiModelProperty("归属合伙人组织name")
    private String companyName;



    /**
     * 归属合伙人机构name
     */
    @ApiModelProperty("归属合伙人机构name")
    private String orgName;

    /**
     * 归属名称
     */
    @ApiModelProperty("归属名称")
    private String topName;
    private String topCode;

    /**
     * 打卡地点名称
     */
    @ApiModelProperty("打卡地点名称")
    private String locationName;
    @ApiModelProperty("打卡地点编码")
    private String locationCode;

    /**
     * 打卡类型 指定/非指定
     */
    @ApiModelProperty("打卡类型 指定/非指定")
    private String signInType;

    /**
     * 审核情况：审核成功/审核不通过/待审核/自动审核通过（默认）
     */
    @ApiModelProperty("审核情况：审核成功/审核不通过/待审核/自动审核通过（默认）")
    private String audit;


    /**
     * 打卡状态 补签/定位异常/非分派网点/未出勤
     */
    @ApiModelProperty("打卡状态 补签/定位异常/非分派网点/未出勤")
    private String auditType;


    /**
     * 审核申请原因
     */
    @ApiModelProperty("审核申请原因(补卡原因)")
    private String auditRemark;

    /**
     * 打卡日期
     */
    @ApiModelProperty("打卡日期")
    private String date;

    /**
     * 签退时间
     */
    @ApiModelProperty("签退时间")
    private String checkOutTime;

    /**
     * 签到时间
     */
    @ApiModelProperty("签到时间")
    private String checkInTime;

    /**
     * 停留时长
     */
    @ApiModelProperty("停留时长")
    private String duration;


    /**
     * 抽查状态，抽查和未抽查
     */
    @ApiModelProperty("抽查状态，抽查和未抽查")
    private String spotCheckType;

    /**
     * 网点经营，逗号分隔
     */
    @ApiModelProperty("网点经营，逗号分隔")
    private String businessLabel;

    /**
     * 网点会议活动
     */
    @ApiModelProperty("网点会议活动")
    private String activityLabel;

    /**
     * 审核详情
     */
    @ApiModelProperty("审核不通过原因")
    private String auditOutcome;

    /**
     * 审核人id
     */
    @ApiModelProperty("审核人id")
    private String auditor;


    /**
     * 审核时间
     */
    @ApiModelProperty("审核时间")
    private LocalDateTime auditTime;

    /**
     * 客户线索详情
     */
    @ApiModelProperty("客户线索详情")
    private List<AttendanceCustomerVO> attendanceCustomers;

    /**
     * 推动及活动详情
     */
    @ApiModelProperty("推动及活动详情")
    private List<AttendanceActivityVO> attendanceActivities;

    /**
     * 影像
     */
    @ApiModelProperty("签到影像")
    private List<String> imageSignList;
    @ApiModelProperty("签退影像")
    private List<String> imageSignOutList;


}
