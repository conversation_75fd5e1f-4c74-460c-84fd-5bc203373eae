package com.hqins.agent.postsale.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/5/29 13:56
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class EmployeeLeaveQueryVO implements Serializable {
    /**
     * 工号
     */
    @ApiModelProperty("工号")
    private String employeeCode;
    /**
     * 姓名
     */
    @ApiModelProperty("姓名")
    private String employeeName;
    /**
     * 申请时间
     */
    @ApiModelProperty("申请时间")
    private String applyTime;
    /**
     * 申请时间
     */
    @ApiModelProperty("开始时间")
    private String beginTime;
    /**
     * 申请时间
     */
    @ApiModelProperty("结束时间")
    private String endTime;

}
