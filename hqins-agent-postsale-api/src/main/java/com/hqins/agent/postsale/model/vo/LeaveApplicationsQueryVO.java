package com.hqins.agent.postsale.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/3/13 09:24
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class LeaveApplicationsQueryVO implements Serializable {

    /**
     *审核类型 补卡/请假/销假
     */
    @ApiModelProperty("审核类型 补卡/请假/销假")
    private String type;

    /**
     *是否能销假
     */
    @ApiModelProperty("是否能销假 true能/false不能")
    private Boolean flag;


    /**
     *id
     */
    @ApiModelProperty("主键ID")
    private Long id;

    /**
     * 工号
     */
    @ApiModelProperty("工号")
    private String employeeCode;

    /**
     * 姓名
     */
    @ApiModelProperty("姓名")
    private String employeeName;


    /**
     * 打卡地点名称
     */
    @ApiModelProperty("打卡地点名称")
    private String locationName;

    /**
     * 打卡类型 指定/非指定
     */
    @ApiModelProperty("打卡类型 指定/非指定")
    private String signInType;

    /**
     * 审核情况
     */
    @ApiModelProperty("审核情况")
    private String audit;

    @ApiModelProperty("类型")
    private String auditType;

    /**
     * 审核申请原因
     */
    @ApiModelProperty("审核申请原因(补卡原因)事由")
    private String auditRemark;

    /**
     * 审核详情
     */
    @ApiModelProperty("审核不通过原因")
    private String auditOutcome;


    /**
     * 申请时间
     */
    @ApiModelProperty("申请时间")
    private String applyTime;

    /**
     * 申请时间
     */
    @ApiModelProperty("开始时间")
    private String beginTime;
    /**
     * 申请时间
     */
    @ApiModelProperty("结束时间")
    private String endTime;

    /**
     * 申请时间
     */
    @ApiModelProperty("创建时间")
    private String createTime;
    /**
     * 打卡日期
     */
    @ApiModelProperty("打卡日期")
    private String date;

    /**
     * 审核人id
     */
    @ApiModelProperty("审核人")
    private String auditor;

    /**
     * 审核时间
     */
    @ApiModelProperty("审核时间")
    private LocalDateTime auditTime;

    /**
     * 影像
     */
    @ApiModelProperty("影像")
    private List<String> imageList;

    /**
     * 原请假记录
     */
    @ApiModelProperty("原请假记录")
    private LeaveApplicationsVO leaveApplicationsVO;
    /**
     * 请假表主键id
     */
    private Long leaveApplicationsId;


    /**
     * 新老数据
     */
    private Boolean newFlag;

}
