package com.hqins.agent.postsale.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 请假记录表对象
 * <AUTHOR>
 * @since 2023-07-14
 */
@ApiModel("请假记录表对象")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LeaveApplicationsVO implements Serializable {

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("工号")
    private String employeeCode;

    @ApiModelProperty("人员姓名")
    private String employeeName;

    @ApiModelProperty("合伙人机构组织")
    private String companyName;
    private String companyCode;

    @ApiModelProperty("合伙人机构名称")
    private String companyInstName;
    private String companyInstCode;

    @ApiModelProperty("请假申请日期")
    private LocalDateTime applyTime;

    @ApiModelProperty("开始时间")
    private LocalDateTime beginTime;

    @ApiModelProperty("结束时间")
    private LocalDateTime endTime;

    @ApiModelProperty("开始时间")
    private String strTime;

    @ApiModelProperty("结束时间")
    private String finishTime;

    @ApiModelProperty("请假时长(天)")
    private String duration;

    @ApiModelProperty("请假说明")
    private String explainDetails;

    @ApiModelProperty("请假类型，参考LeaveType")
    private String leaveType;

    @ApiModelProperty("审核状态,参考AttendanceCheckType")
    private String audit;

    @ApiModelProperty("审核时间")
    private LocalDateTime auditTime;

    @ApiModelProperty("审核不通过原因")
    private String auditOutcome;

    @ApiModelProperty("审核人")
    private String auditor;

    @ApiModelProperty("已申请请假天数")
    private String applyCount;

    @ApiModelProperty("病假 已休,剩余,可用")
    private String sickLeave;
    @ApiModelProperty("婚假 已休,剩余,可用")
    private String marriageLeave;
    @ApiModelProperty("丧假 已休,剩余,可用")
    private String funeralLeave;
    @ApiModelProperty("产假 已休,剩余,可用")
    private String maternityLeave;
    @ApiModelProperty("陪产假 已休,剩余,可用")
    private String paternityLeave;
    @ApiModelProperty("事假 已休,剩余,可用")
    private String personalLeave;
    @ApiModelProperty("其他(带薪) 已休,剩余,可用")
    private String businessTravelLeave;

    @ApiModelProperty("事假(带薪) 已休,剩余,可用")
    private String annualLeave;

    @ApiModelProperty("审核通过请假天数")
    private String applyAuditCount;

    @ApiModelProperty("请假上下午标记 0:上午 1:下午")
    private Integer morningOrAfternoon;

    /**
     * 影像
     */
    @ApiModelProperty("影像")
    private List<String> imageList;


    @ApiModelProperty("活动详情记录")
    private String detailRecord;
    @ApiModelProperty("是否是新老数据")
    private Boolean newFlag;

    public String[] getAllLeaveTypes() {
        return new String[]{
                getSickLeave(),
                getMarriageLeave(),
                getFuneralLeave(),
                getMaternityLeave(),
                getPaternityLeave(),
                getPersonalLeave(),
                getBusinessTravelLeave(),
                getAnnualLeave()
        };
    }
}
