package com.hqins.agent.postsale.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class H5LeaveListVO implements Serializable {

    @ApiModelProperty("主键ID")
    private Long id;

    @ApiModelProperty("审批ID")
    private Long auditId;

    @ApiModelProperty("申请类型 请假、补卡")
    private String leaveType;

    @ApiModelProperty("补卡 补签退/签到加签退")
    private String appointType;

    @ApiModelProperty("请假类型")
    private String leaveName;

    @ApiModelProperty("工号")
    private String employeeCode;

    @ApiModelProperty("发起人")
    private String employeeName;

    @ApiModelProperty("请假、补卡时间")
    private String beginTime;

    @ApiModelProperty("请假时长")
    private String leaveTime;

    @ApiModelProperty("提交时间")
    private String applyTime;

    @ApiModelProperty("本月已申请请假、补卡次数")
    private String leaveCount;

    @ApiModelProperty("本月请假通过")
    private String leavePassCount;

    @ApiModelProperty("图片")
    private List<String> imageUrls;

    @ApiModelProperty("请假结束时间")
    private String endTime;

    @ApiModelProperty("事由")
    private String auditRemark;

    @ApiModelProperty("打卡地点名称")
    private String locationName;

    @ApiModelProperty("网点经营，逗号分隔")
    private String businessLabel;

    @ApiModelProperty("打卡类型 指定/非指定")
    private String signInType;

    @ApiModelProperty("审核不通过原因")
    private String auditOutcome;

    @ApiModelProperty("病假 已休,剩余,可用")
    private String sickLeave;
    @ApiModelProperty("婚假 已休,剩余,可用")
    private String marriageLeave;
    @ApiModelProperty("丧假 已休,剩余,可用")
    private String funeralLeave;
    @ApiModelProperty("产假 已休,剩余,可用")
    private String maternityLeave;
    @ApiModelProperty("陪产假 已休,剩余,可用")
    private String paternityLeave;
    @ApiModelProperty("事假 已休,剩余,可用")
    private String personalLeave;
    @ApiModelProperty("出差 已休,剩余,可用")
    private String businessTravelLeave;
    @ApiModelProperty("公出 已休,剩余,可用")
    private String officialBusinessLeave;
    @ApiModelProperty("年假 已休,剩余,可用")
    private String annualLeave;



   /* @ApiModelProperty("归属合伙人组织name")
    private String companyName;

    @ApiModelProperty("归属合伙人机构name")
    private String orgName;

    @ApiModelProperty("归属名称")
    private String topName;

    @ApiModelProperty("审核情况：审核成功/审核不通过/待审核/自动审核通过（默认）")
    private String audit;

    @ApiModelProperty("打卡状态 补签/定位异常/非分派网点/未出勤")
    private String auditType;

    @ApiModelProperty("审核不通过原因")
    private String auditOutcome;

    @ApiModelProperty("请假时长")
    private String duration;

    @ApiModelProperty("打卡日期")
    private String date;

    @ApiModelProperty("签退时间")
    private String checkOutTime;

    @ApiModelProperty("签到时间")
    private String checkInTime;

    @ApiModelProperty("抽查状态，抽查和未抽查")
    private String spotCheckType;

    @ApiModelProperty("网点会议活动")
    private String activityLabel;

    @ApiModelProperty("审核人id")
    private String auditor;

    @ApiModelProperty("审核时间")
    private LocalDateTime auditTime;

    @ApiModelProperty("客户线索详情")
    private List<AttendanceCustomerVO> attendanceCustomers;

    @ApiModelProperty("推动及活动详情")
    private List<AttendanceActivityVO> attendanceActivities;

    @ApiModelProperty("影像")
    private List<String> imageList;

    @ApiModelProperty("补卡(APPOINT)or请假(LEAVE)")
    private String flag;*/
}
