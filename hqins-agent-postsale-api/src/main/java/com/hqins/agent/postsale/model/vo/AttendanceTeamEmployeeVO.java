package com.hqins.agent.postsale.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2023/7/19 17:56
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AttendanceTeamEmployeeVO {

    @ApiModelProperty("工号")
    private String employeeCode;

    @ApiModelProperty("打卡天数")
    private Double dayCount;

    @ApiModelProperty("异常次数")
    private Double noneCount;

    @ApiModelProperty("应出勤天数")
    private Double needAttendanceDays;
}
